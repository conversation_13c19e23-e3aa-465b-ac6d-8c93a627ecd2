﻿using System;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.UI
{
    /// <summary>
    /// 图像巡检信息显示界面
    /// 外部界面传值，或者显示最新图像巡检结果数据
    /// </summary>
    public partial class UC_ImagePatrolResult : UIUserControl, IRefreshUI
    {
        #region 字段属性

        /// <summary>
        /// 界面对应图像巡检结果
        /// </summary>
        private ImagePatrolResult _imagePatrolResult;

        #endregion

        #region 构造

        public UC_ImagePatrolResult()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 点击刷新按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 是否显示刷新按钮
        /// </summary>
        /// <param name="visible"></param>
        public void SetRefreshBtnVisible(bool visible)
        {
            btnRefresh.Visible = visible;
        }

        /// <summary>
        /// 设置关联的巡检结果
        /// 历史数据查询用
        /// </summary>
        /// <param name="imagePatrolResult"></param>
        public void SetTragetPatrolResult(ImagePatrolResult imagePatrolResult)
        {
            _imagePatrolResult = imagePatrolResult;
            RefreshUI();
        }

        #endregion

        #region IRefreshUI

        public void RefreshUI()
        {
            ImagePatrolResult imagePatrolResult = null;
            // 传入特定历史数据时
            if(_imagePatrolResult != null)
            {
                imagePatrolResult = _imagePatrolResult;
            }
            // 未有传入值时，显示最新巡检信息
            else
            {
                imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult;
            }

            if(imagePatrolResult != null)
            {
                // 开始时间
                lblStartTime.Text = imagePatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                // 触发方式
                lblPatrolTriggerType.Text = imagePatrolResult.PatrolTriggerType.ToString();
                // 清空老信息
                pnlMain.Controls.Clear();

                // 添加各模块显示内容
                foreach(var modelResult in imagePatrolResult.ModelResultList)
                {
                    if(modelResult != null)
                    {
                        pnlMain.Controls.Add(new UC_OneModelImageResult(modelResult));
                    }
                }
            }
        }

        #endregion
    }
}