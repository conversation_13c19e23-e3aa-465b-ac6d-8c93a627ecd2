﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Fpi.Communication.Converter;

namespace Fpi.WMS3000.Equipment.WCS3900
{
    /// <summary>
    /// 公共寄存器区
    /// </summary>
    public class WCS3900CommonParam
    {
        #region 属性

        /// <summary>
        /// PH、电导率、溶解氧、浊度公共寄存器区集合
        /// </summary>
        public Dictionary<eWCSNodeType, OneCommonParam> CommonParams = new Dictionary<eWCSNodeType, OneCommonParam>();

        /// <summary>
        /// 界面软件版本号
        /// </summary>
        public string SoftVision { get; private set; }

        /// <summary>
        /// 主板版本号
        /// </summary>
        public string MainBoardVision { get; private set; }

        /// <summary>
        /// SN号
        /// </summary>
        public string SNCode { get; private set; }

        #endregion

        #region 构造

        public WCS3900CommonParam()
        {
            // 初始化字典
            foreach(var item in Enum.GetValues(typeof(eWCSNodeType)))
            {
                CommonParams.Add((eWCSNodeType)item, new OneCommonParam());
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新参数
        /// </summary>
        /// <param name="data"></param>
        public void UpdateValue(byte[] data, int startIndex)
        {
            SoftVision = DataConverter.GetInstance().ToString(data, startIndex, 32);
            MainBoardVision = DataConverter.GetInstance().ToString(data, startIndex + 32, 32);
            SNCode = DataConverter.GetInstance().ToString(data, startIndex + 64, 12);
        }

        #endregion
    }

    public class OneCommonParam
    {
        #region 属性

        /// <summary>
        /// 流程步骤总数
        /// </summary>
        [Description("流程步骤总数")]
        public int TotalStepCount { get; private set; } = -1;

        /// <summary>
        /// 当前步骤数
        /// </summary>
        [Description("当前步骤数")]
        public int CurrentStep { get; private set; } = -1;

        /// <summary>
        /// 点位状态
        /// </summary>
        [Description("点位状态")]
        public Dictionary<eWCS3900ElementType, bool> ElementState { get; } = new Dictionary<eWCS3900ElementType, bool>();

        /// <summary>
        /// 液位状态
        /// </summary>
        [Description("液位状态")]
        public Dictionary<eWCS3900LevelType, bool> LevelState { get; } = new Dictionary<eWCS3900LevelType, bool>();

        /// <summary>
        /// 电极更换时间
        /// </summary>
        [Description("电极更换时间")]
        public DateTime ElectrodeReplaceDays { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 电极最久可用时间
        /// </summary>
        [Description("电极最久可用时间")]
        public DateTime ElectrodeLogestUseTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 标液1总量
        /// </summary>
        [Description("标液1总量")]
        public float Liquid1TotalNum { get; private set; } = -1;

        /// <summary>
        /// 标液2总量
        /// </summary>
        [Description("标液2总量")]
        public float Liquid2TotalNum { get; private set; } = -1;

        /// <summary>
        /// 标液3总量
        /// </summary>
        [Description("标液3总量")]
        public float Liquid3TotalNum { get; private set; } = -1;

        /// <summary>
        /// 标液1余量
        /// </summary>
        [Description("标液1余量")]
        public float Liquid1Allowance { get; private set; } = -1;

        /// <summary>
        /// 标液2余量
        /// </summary>
        [Description("标液2余量")]
        public float Liquid2Allowance { get; private set; } = -1;

        /// <summary>
        /// 标液3余量
        /// </summary>
        [Description("标液3余量")]
        public float Liquid3Allowance { get; private set; } = -1;

        /// <summary>
        /// 标液1更换时刻
        /// </summary>
        [Description("标液1更换时刻")]
        public DateTime Liquid1ChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 标液2更换时刻
        /// </summary>
        [Description("标液2更换时刻")]
        public DateTime Liquid2ChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 标液3更换时刻
        /// </summary>
        [Description("标液3更换时刻")]
        public DateTime Liquid3ChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 标液1保质期
        /// </summary>
        [Description("标液1保质期")]
        public int Liquid1GuaranteePeriod { get; private set; } = -1;

        /// <summary>
        /// 标液2保质期
        /// </summary>
        [Description("标液2保质期")]
        public int Liquid2GuaranteePeriod { get; private set; } = -1;

        /// <summary>
        /// 标液3保质期
        /// </summary>
        [Description("标液3保质期")]
        public int Liquid3GuaranteePeriod { get; private set; } = -1;

        /// <summary>
        /// D0口使用次数
        /// </summary>
        public Dictionary<eWCS3900ElementType, long> ElementUseTimes { get; private set; } = new Dictionary<eWCS3900ElementType, long>();

        #endregion

        #region 构造

        public OneCommonParam()
        {
            // 字典初始化
            foreach(var item in Enum.GetValues(typeof(eWCS3900ElementType)))
            {
                ElementUseTimes.Add((eWCS3900ElementType)item, -1);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新参数
        /// </summary>
        /// <param name="data"></param>
        public void UpdateValue(byte[] data, int startIndex, eWCSNodeType eWCSNodeType)
        {
            TotalStepCount = DataConverter.GetInstance().ToInt32(data, startIndex);
            CurrentStep = DataConverter.GetInstance().ToInt32(data, startIndex + 2);

            int elementState = (int)DataConverter.GetInstance().ToInt32(data, startIndex + 4);
            for(int i = 1; i <= Enum.GetValues(typeof(eWCS3900ElementType)).Length; i++)
            {
                ElementState[(eWCS3900ElementType)i] = (elementState & 0x0001) == 0x0001;
                // 移位
                elementState >>= 1;
            }

            int levelState = (int)DataConverter.GetInstance().ToInt32(data, startIndex + 6);
            for(int i = 1; i <= Enum.GetValues(typeof(eWCS3900LevelType)).Length; i++)
            {
                LevelState[(eWCS3900LevelType)i] = (levelState & 0x0001) == 0x0001;
                // 移位
                levelState >>= 1;
            }

            ElectrodeReplaceDays = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 8);
            ElectrodeLogestUseTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 12);

            // 溶解氧，特殊解析
            if(eWCSNodeType != eWCSNodeType.w01009)
            {
                Liquid1TotalNum = DataConverter.GetInstance().ToSingle(data, startIndex + 16);
                Liquid2TotalNum = DataConverter.GetInstance().ToSingle(data, startIndex + 20);
                Liquid3TotalNum = DataConverter.GetInstance().ToSingle(data, startIndex + 24);

                Liquid1Allowance = DataConverter.GetInstance().ToSingle(data, startIndex + 28);
                Liquid2Allowance = DataConverter.GetInstance().ToSingle(data, startIndex + 32);
                Liquid3Allowance = DataConverter.GetInstance().ToSingle(data, startIndex + 36);

                Liquid1ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 40);
                Liquid2ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 44);
                Liquid3ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 48);

                Liquid1GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 52);
                Liquid2GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 54);
                Liquid3GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 56);

                for(int i = 1; i <= ElementUseTimes.Count; i++)
                {
                    ElementUseTimes[(eWCS3900ElementType)i] = DataConverter.GetInstance().ToInt64(data, startIndex + 58 + 4 * (i - 1));
                }
            }
            else
            {
                Liquid1TotalNum = DataConverter.GetInstance().ToSingle(data, startIndex + 16);
                Liquid2TotalNum = DataConverter.GetInstance().ToSingle(data, startIndex + 20);

                Liquid1Allowance = DataConverter.GetInstance().ToSingle(data, startIndex + 24);
                Liquid2Allowance = DataConverter.GetInstance().ToSingle(data, startIndex + 28);

                Liquid1ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 32);
                Liquid2ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 36);

                Liquid1GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 40);
                Liquid2GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 42);

                for(int i = 1; i <= ElementUseTimes.Count; i++)
                {
                    ElementUseTimes[(eWCS3900ElementType)i] = DataConverter.GetInstance().ToInt64(data, startIndex + 44 + 4 * (i - 1));
                }
            }
        }

        #endregion
    }
}