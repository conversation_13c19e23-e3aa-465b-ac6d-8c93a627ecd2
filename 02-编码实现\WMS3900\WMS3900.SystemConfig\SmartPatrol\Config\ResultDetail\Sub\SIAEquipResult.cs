﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using Fpi.Alarm;
using Fpi.Devices;
using Fpi.Util.EnumRelated;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.SIA3900;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 单个SIA仪表巡检结果
    /// </summary>
    public class SIAEquipResult : SubModelResultBase
    {
        #region 字段属性

        #region 仪表状态

        /// <summary>
        /// 通讯状态
        /// </summary>
        [Description("通讯状态")]
        public eModuleWorkingState ComState { get; set; }

        /// <summary>
        /// 系统状态
        /// </summary>
        [Description("系统状态")]
        public eSIA3900SystemState SystemState { get; set; }

        /// <summary>
        /// 报警状态
        /// </summary>
        [Description("报警状态")]
        public eModuleWorkingState AlarmState { get; set; }

        /// <summary>
        /// 当前报警列表
        /// </summary>
        [Description("当前报警列表")]
        public List<string> CurrentAlarmList { get; set; } = new List<string>();

        /// <summary>
        /// 器件自检结果
        /// 异常时显示（自检类型，自检时间，是否正常）
        /// 暂不实现
        /// </summary>
        [Description("器件自检结果")]
        public eModuleWorkingState SelfCheckResult { get; set; }

        /// <summary>
        /// 管路脏污状态
        /// </summary>
        [Description("管路脏污状态")]
        public eSmutState PipeSmutState { get; set; }

        /// <summary>
        /// 反应单元脏污状态
        /// </summary>
        [Description("反应单元脏污状态")]
        public eSmutState ReactionUnitSmutState { get; set; }

        #endregion

        #region 器件使用信息

        /// <summary>
        /// 选向阀使用信息
        /// </summary>
        [Description("选向阀使用信息")]
        public KeyDeviceUsageState DirectionalValveUsageState { get; set; } = new KeyDeviceUsageState(eSIA3900DeviceType.选项阀.ToString());

        /// <summary>
        /// 柱塞泵使用信息
        /// </summary>
        [Description("柱塞泵使用信息")]
        public KeyDeviceUsageState PlungerPumpUsageState { get; set; } = new KeyDeviceUsageState(eSIA3900DeviceType.柱塞泵.ToString());

        /// <summary>
        /// 消解阀使用信息
        /// </summary>
        [Description("消解阀使用信息")]
        public KeyDeviceUsageState DissolvingValveUsageState { get; set; } = new KeyDeviceUsageState(eSIA3900DeviceType.消解阀.ToString());

        /// <summary>
        /// 加热丝使用信息
        /// </summary>
        [Description("加热丝使用信息")]
        public KeyDeviceUsageState HeatingWireUsageState { get; set; } = new KeyDeviceUsageState(eSIA3900DeviceType.加热丝.ToString());

        #endregion

        #region 关键参数

        /// <summary>
        /// 量程
        /// </summary>
        [Description("量程")]
        public double Range { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public eSIA3900UnitShow Unit { get; private set; } = (eSIA3900UnitShow)(-1);

        /// <summary>
        /// 标定时间
        /// </summary>
        [Description("标定时间")]
        public DateTime CalibrateTime { get; set; }

        /// <summary>
        /// 吸光度
        /// </summary>
        [Description("吸光度")]
        public double AbsorbanceValue { get; set; }

        /// <summary>
        /// 曲线斜率k
        /// </summary>
        [Description("曲线斜率k")]
        public double K { get; set; }

        /// <summary>
        /// 曲线截距b
        /// </summary>
        [Description("曲线截距b")]
        public double B { get; set; }

        /// <summary>
        /// 消解温度(℃)
        /// </summary>
        [Description("消解温度(℃)")]
        public int DigestionTemp { get; set; }

        /// <summary>
        /// 消解时间(min)
        /// </summary>
        [Description("消解时间(min)")]
        public int DigestionTime { get; set; }

        /// <summary>
        /// 显色温度(℃)
        /// </summary>
        [Description("显色温度(℃)")]
        public int ColorationTemp { get; set; }

        /// <summary>
        /// 显色时间(min)
        /// </summary>
        [Description("显色时间(min)")]
        public int ColorationTime { get; set; }

        /// <summary>
        /// 检出限值
        /// </summary>
        [Description("检出限值")]
        public double DetectionLimit { get; set; }

        #endregion

        #endregion

        #region 构造

        public SIAEquipResult()
        {
            ModelName = "SIA仪表";
        }

        public SIAEquipResult(SIA3900Equipment siaEquip)
        {
            if(siaEquip == null)
            {
                return;
            }

            if(Enum.TryParse(siaEquip.TypeDesc, out eDeviceMeasureType deviceType))
            {
                ModelName = EnumOperate.GetEnumDesc(deviceType) + "分析仪";
            }
            else
            {
                ModelName = siaEquip.name;
            }

            #region 仪表状态

            ComState = siaEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            SystemState = siaEquip.HomePageStatus.SystemState;
            AlarmState = siaEquip.IsAlarmExceptComError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            if(AlarmState == eModuleWorkingState.异常)
            {
                CurrentAlarmList = AlarmManager.GetInstance().GetCurrentAlarms().
                    Where(x => x.AlarmSource.id == siaEquip.AlarmSourceId && x.AlarmCode.id != siaEquip.ComErrorAlarmCodeId).
                    Select(x => x.AlarmCode.description).
                    Distinct().
                    ToList();
            }

            if(siaEquip.TypeDesc == eDeviceMeasureType.CODMn.ToString())
            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<CodMnNH4EquipPatrolResult>();

                if(imagePatrolResult != null)
                {
                    PipeSmutState = imagePatrolResult.CodMnPipeSmutState;
                    ReactionUnitSmutState = imagePatrolResult.CodMnReactionUnitSmutState;
                }
            }
            else if(siaEquip.TypeDesc == eDeviceMeasureType.NH4.ToString())
            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<CodMnNH4EquipPatrolResult>();

                if(imagePatrolResult != null)
                {
                    PipeSmutState = imagePatrolResult.NH4PipeSmutState;
                    ReactionUnitSmutState = imagePatrolResult.NH4ReactionUnitSmutState;
                }
            }
            else if(siaEquip.TypeDesc == eDeviceMeasureType.TP.ToString())
            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<TPTNEquipPatrolResult>();

                if(imagePatrolResult != null)
                {
                    PipeSmutState = imagePatrolResult.TPPipeSmutState;
                    ReactionUnitSmutState = imagePatrolResult.TPReactionUnitSmutState;
                }
            }
            else if(siaEquip.TypeDesc == eDeviceMeasureType.TN.ToString())
            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<TPTNEquipPatrolResult>();

                if(imagePatrolResult != null)
                {
                    PipeSmutState = imagePatrolResult.TNPipeSmutState;
                    ReactionUnitSmutState = imagePatrolResult.TNReactionUnitSmutState;
                }
            }

            #endregion

            #region 器件使用信息

            if(siaEquip.LifeMonitors.DeviceLifeInfos.ContainsKey(eSIA3900DeviceType.选项阀))
            {
                var info = siaEquip.LifeMonitors.DeviceLifeInfos[eSIA3900DeviceType.选项阀];
                DirectionalValveUsageState.UsageCount = info.DeviceWorkTime;
                DirectionalValveUsageState.MaxUsageCount = info.MaxUsageTime;
            }

            if(siaEquip.LifeMonitors.DeviceLifeInfos.ContainsKey(eSIA3900DeviceType.柱塞泵))
            {
                var info = siaEquip.LifeMonitors.DeviceLifeInfos[eSIA3900DeviceType.柱塞泵];
                PlungerPumpUsageState.UsageCount = info.DeviceWorkTime;
                PlungerPumpUsageState.MaxUsageCount = info.MaxUsageTime;
            }

            if(siaEquip.LifeMonitors.DeviceLifeInfos.ContainsKey(eSIA3900DeviceType.消解阀))
            {
                var info = siaEquip.LifeMonitors.DeviceLifeInfos[eSIA3900DeviceType.消解阀];
                DissolvingValveUsageState.UsageCount = info.DeviceWorkTime;
                DissolvingValveUsageState.MaxUsageCount = info.MaxUsageTime;
            }

            if(siaEquip.LifeMonitors.DeviceLifeInfos.ContainsKey(eSIA3900DeviceType.加热丝))
            {
                var info = siaEquip.LifeMonitors.DeviceLifeInfos[eSIA3900DeviceType.加热丝];
                HeatingWireUsageState.UsageCount = info.DeviceWorkTime;
                HeatingWireUsageState.MaxUsageCount = info.MaxUsageTime;
            }

            #endregion

            #region 关键参数

            Range = siaEquip.DeviceKeyParams.RangeUpper;
            Unit = siaEquip.MeasureParams.Unit;
            CalibrateTime = siaEquip.DeviceKeyParams.CalibrateTime;
            AbsorbanceValue = siaEquip.DeviceKeyParams.MensurationValue;
            K = siaEquip.DeviceKeyParams.K;
            B = siaEquip.DeviceKeyParams.B;
            DigestionTemp = siaEquip.DeviceKeyParams.DigestionTemp;
            DigestionTime = siaEquip.DeviceKeyParams.DigestionTime;
            ColorationTemp = siaEquip.DeviceKeyParams.ColorationTemp;
            ColorationTime = siaEquip.DeviceKeyParams.ColorationTime;
            DetectionLimit = siaEquip.DeviceKeyParams.DetectionLimit;

            #endregion

            // 结果状态判断
            if(ComState != eModuleWorkingState.正常 || SystemState != eSIA3900SystemState.系统正常 || AlarmState != eModuleWorkingState.正常 || PipeSmutState != eSmutState.正常 || ReactionUnitSmutState != eSmutState.正常)
            {
                PatrolResult = ePatrolResult.异常;
            }
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override string GetResultStr()
        {
            // 报警详情
            string alarmDetail = string.Join(",", CurrentAlarmList);

            StringBuilder resultStr = new StringBuilder();

            resultStr.AppendLine(ModelName)
                .AppendLine($"巡检结果：{PatrolResult}")
                .AppendLine($"1.仪表状态")
                .AppendLine($"通讯状态：{ComState}")
                .AppendLine($"系统状态：{SystemState}")
                .AppendLine($"报警状态：{AlarmState}");
            if(!string.IsNullOrEmpty(alarmDetail))
            {
                resultStr.AppendLine(alarmDetail);
            }
            resultStr.AppendLine($"管路脏污状态：{PipeSmutState}")
                .AppendLine($"反应单元脏污状态：{ReactionUnitSmutState}")
                .AppendLine($"2.器件使用信息：")
                .Append(DirectionalValveUsageState.GetResultStr())
                .Append(PlungerPumpUsageState.GetResultStr())
                .Append(DissolvingValveUsageState.GetResultStr())
                .Append(HeatingWireUsageState.GetResultStr())
                .AppendLine($"3.关键参数：")
                .AppendLine($"量程：{Range.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat)}")
                .AppendLine($"单位：{EnumOperate.GetEnumDesc(Unit)}")
                .AppendLine($"标定时间：{CalibrateTime.ToDisplayTime()}")
                .AppendLine($"吸光度：{AbsorbanceValue.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat)}")
                .AppendLine($"曲线斜率k：{K.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat)}")
                .AppendLine($"曲线截距b：{B.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat)}")
                .AppendLine($"消解温度：{DigestionTemp.ToDisplayFormat("℃")}")
                .AppendLine($"消解时间：{DigestionTime.ToDisplayFormat()}")
                .AppendLine($"显色温度：{ColorationTemp.ToDisplayFormat("℃")}")
                .AppendLine($"显色时间：{ColorationTime.ToDisplayFormat()}")
                .AppendLine($"检出限值：{DetectionLimit.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat)}");

            return resultStr.ToString();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 打印异常巡检结果
        /// </summary>
        /// <returns></returns>
        public string GetErrorResultStr()
        {
            StringBuilder resultStr = new StringBuilder();

            if(ComState != eModuleWorkingState.正常)
            {
                resultStr.Append("通讯状态异常，");
            }
            if(SystemState != eSIA3900SystemState.系统正常)
            {
                resultStr.Append($"系统状态为{SystemState.ToString()}，");
            }
            if(AlarmState != eModuleWorkingState.正常)
            {
                // 报警详情
                string alarmDetail = string.Join("、", CurrentAlarmList);

                resultStr.Append($"报警详情:{alarmDetail}，");
            }
            if(PipeSmutState != eSmutState.正常)
            {
                resultStr.Append("管路脏污，");
            }
            if(ReactionUnitSmutState != eSmutState.正常)
            {
                resultStr.Append("反应单元脏污，");
            }

            return resultStr.ToString().TrimEnd('，');
        }

        #endregion
    }
}