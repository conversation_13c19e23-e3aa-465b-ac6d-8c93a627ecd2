﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    ///总磷总氮分析仪状态检测执行类
    /// </summary>
    public class TPTNEquipPatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public TPTNEquipPatrol()
        {
            UnitId = "TPTNEquip";
            UnitName = "总磷总氮分析仪状态检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(ref ImageUnitPatrolResultBase patrolResult)
        {
            patrolResult = new TPTNEquipPatrolResult();

            if(ExterEquipConfigManager.GetInstance().CameraSelect.TPTNEquipCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            // 拍照
            ExterEquipConfigManager.GetInstance().CameraSelect.TPTNEquipCamera.ScreenShot(out string picPath);

            // 算法分析
            AlgorithmHelper.CheckSIA3900State(new Mat(picPath), out bool leftPipeSmutState, out bool leftReactionUnitSmutState, out bool rightPipeSmutState, out bool rightReactionUnitSmutState);

            patrolResult.ImagePath = FileExtension.GetRelativePath(picPath);
            ((TPTNEquipPatrolResult)patrolResult).TPPipeSmutState = leftPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNEquipPatrolResult)patrolResult).TPReactionUnitSmutState = leftReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNEquipPatrolResult)patrolResult).TNPipeSmutState = rightPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNEquipPatrolResult)patrolResult).TNReactionUnitSmutState = rightReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;
        }

        #endregion
    }
}