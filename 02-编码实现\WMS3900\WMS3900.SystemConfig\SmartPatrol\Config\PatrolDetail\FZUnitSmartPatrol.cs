﻿using System.Linq;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 辅助单元巡检执行类
    /// </summary>
    public class FZUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public FZUnitSmartPatrol()
        {
            UnitId = "Auxiliary";
            UnitName = "辅助单元";
            Description = "水站版";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            FZUnitPatrolResult result = new FZUnitPatrolResult();

            if(ExterEquipConfigManager.GetInstance().DeviceSelect.UPSDevice is YstEA900UpsEquipment upsEquip)
            {
                result.UPSComState = upsEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
                result.UPSParams = upsEquip.DeviceParams;
                result.UpsUsageState = UsageStatisticsManager.GetInstance().EquipUsageInfoList.FirstOrDefault(x => x.IsUsed && x.Id == upsEquip.id)?.ActivationDate;
            }

            if(ExterEquipConfigManager.GetInstance().DeviceSelect.MainElectricityMonitorDevice is DS6CNZDESEquipment dsEquip)
            {
                result.ElectricMeterComState = dsEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
                result.ElectricMeterParams = dsEquip.DeviceParams;
            }

            if(ExterEquipConfigManager.GetInstance().DeviceSelect.FLDevice is LXAM125Equipment lxEquip)
            {
                result.ThunderComState = lxEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
                result.ThunderParams = lxEquip.DeviceParams;
            }

            if(ExterEquipConfigManager.GetInstance().DeviceSelect.PDUDevice is LJWD200Equipment pduEquip)
            {
                result.PDUComState = pduEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            }

            if(ExterEquipConfigManager.GetInstance().DeviceSelect.AirControlDevice is JDRKRSEquip airEquip)
            {
                result.KTComState = airEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
                result.KTParams = airEquip.DeviceParams;
                result.KTUsageState = UsageStatisticsManager.GetInstance().EquipUsageInfoList.FirstOrDefault(x => x.IsUsed && x.Id == airEquip.id)?.ActivationDate;
            }

            result.OzoneUsageState = UsageStatisticsManager.GetInstance().DeviceUsageInfoList.
                FirstOrDefault(x => x.IsUsed && x.Id == ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.OzoneNodeId)?.ActivationDate;

            result.AirPressUsageState = UsageStatisticsManager.GetInstance().DeviceUsageInfoList.
                FirstOrDefault(x => x.IsUsed && x.Id == ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.AirPressNodeId)?.ActivationDate;

            result.AirVentilatorUsageState = UsageStatisticsManager.GetInstance().DeviceUsageInfoList.
                FirstOrDefault(x => x.IsUsed && x.Id == ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.AirVentilatorNodeId)?.ActivationDate;

            if(ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.TempNode != null)
            {
                result.Temp = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.TempNode.GetValue();
                result.TempState = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.TempNode.IsOverAlarmLimit() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            }

            if(ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.HumidityNode != null)
            {
                result.Humidity = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.HumidityNode.GetValue();
                result.HumidityState = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.HumidityNode.IsOverAlarmLimit() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            }

            if(ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.SmokeNode != null)
            {
                result.SmokeAlarmState = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.SmokeNode.GetValue() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            }

            if(ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.WaterLeakageNode != null)
            {
                result.WaterLeakageAlarmState = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.WaterLeakageNode.GetValue() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            }

            result.DoorBanComState = eModuleWorkingState.正常;
            result.DoorBanClothState = eModuleWorkingState.正常;
            result.DoorBanAlarmState = eModuleWorkingState.正常;

            return result;
        }

        #endregion
    }
}