﻿using System;
using System.ComponentModel;
using Fpi.Data.Config;
using Newtonsoft.Json;

namespace Fpi.WMS3000.SystemConfig
{
    /// <summary>
    /// 单个器件使用情况
    /// </summary>
    public class DeviceUsageInfo
    {
        #region 字段属性

        /// <summary>
        /// 编号
        /// </summary>
        [Description("编号")]
        public string Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        public string Name { get; set; }

        /// <summary>
        /// 器件类型
        /// </summary>
        [Description("开关量类型")]
        public eStateNodeType DeviceType { get; set; }

        /// <summary>
        /// 启用日期
        /// </summary>
        [Description("启用日期")]
        public DateTime ActivationDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 有效期（器件质保期）
        /// </summary>
        [Description("有效期")]
        public int ValidityPeriod { get; set; } = 365;

        /// <summary>
        /// 截止使用日期(启用日期+有效期)
        /// </summary>
        [JsonIgnore]
        [Description("截止使用日期")]
        public DateTime ExpirationDate
        {
            get
            {
                var time = DateTime.MinValue;

                // 启用日期+有效期
                if(ActivationDate != DateTime.MinValue)
                {
                    time = ActivationDate.AddDays(ValidityPeriod);
                }

                return time;
            }
        }

        /// <summary>
        /// 已用次数（时长/秒）
        /// </summary>
        [Description("已用次数（时长/秒）")]
        public int UsageCount { get; set; }

        /// <summary>
        /// 最大使用次数（时长/秒）
        /// </summary>
        [Description("最大使用次数（时长/秒）")]
        public int MaxUsageCount { get; set; } = 100000;

        /// <summary>
        /// 最大使用时长（天）
        /// </summary>
        [JsonIgnore]
        [Description("最大使用时长（天）")]
        public int MaxUsageDay
        {
            get
            {
                int day = 0;
                if(DeviceType == eStateNodeType.Pump)
                {
                    day = MaxUsageCount / 365;
                }

                return day;
            }
            set
            {
                if(DeviceType == eStateNodeType.Pump)
                {
                    MaxUsageCount = value * 365;
                }
            }
        }

        #region 泵参数

        /// <summary>
        /// 泵累计工作时长
        /// </summary>
        [JsonIgnore]
        [Description("泵累计工作时长")]
        public TimeSpan PumpWorkTime
        {
            get
            {
                var time = TimeSpan.FromSeconds(UsageCount);

                // 累加本次启动时长
                if(PumpOpenTime != DateTime.MinValue)
                {
                    int count = (int)(DateTime.Now - PumpOpenTime).TotalSeconds;
                    if(count > 0)
                    {
                        time = time.Add(TimeSpan.FromSeconds(count));
                    }
                }

                return time;
            }
        }

        /// <summary>
        /// 泵累计启用时间
        /// </summary>
        [JsonIgnore]
        [Description("泵累计启用时间")]
        public TimeSpan PumpUsedTime
        {
            get
            {
                if(DateTime.MinValue != ActivationDate)
                {
                    return DateTime.Now - ActivationDate;
                }
                else
                {
                    return TimeSpan.MinValue;
                }
            }
        }

        /// <summary>
        /// 泵启动时刻
        /// </summary>
        [JsonIgnore]
        [Description("泵启动时刻")]
        public DateTime PumpOpenTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 是否启用（初始化时找到了对应因子，则启用）
        /// </summary>
        [JsonIgnore]
        [Description("是否启用")]
        public bool IsUsed { get; set; }

        #endregion

        #endregion

        #region 构造

        public DeviceUsageInfo(StateNode stateNode)
        {
            Id = stateNode.id;
            Name = stateNode.name;
            DeviceType = stateNode.NodeType;
            ActivationDate = DateTime.Now;
            UsageCount = 0;
        }

        public DeviceUsageInfo()
        {
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新启用时间
        /// </summary>
        public void UpdateActivationTime()
        {
            ActivationDate = DateTime.Now;
            UsageCount = 0;
        }

        #region 阀门、控制器

        /// <summary>
        /// 使用次数加一
        /// </summary>
        public void AddOneCount()
        {
            UsageCount++;
        }

        #endregion

        #region 泵

        /// <summary>
        /// 更新泵启动时刻
        /// </summary>
        public void StartPumpUsageTimeCount()
        {
            if(DeviceType == eStateNodeType.Pump)
            {
                PumpOpenTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 泵运行时长累加
        /// </summary>
        public void StopPumpUsageTimeCount()
        {
            if(DeviceType == eStateNodeType.Pump)
            {
                // 泵已启动
                if(PumpOpenTime != DateTime.MinValue)
                {
                    // 本次启动时长
                    int count = (int)(DateTime.Now - PumpOpenTime).TotalSeconds;
                    if(count > 0)
                    {
                        UsageCount += count;
                    }

                    // 启动时间置空
                    PumpOpenTime = DateTime.MinValue;
                }
            }
        }

        /// <summary>
        /// 获取水泵运行时长字符串描述
        /// </summary>
        /// <returns></returns>
        public string GetPumpUsageTimeStr()
        {
            return PumpWorkTime.TotalHours.ToString("F2") + "H";
            //string timeStr = string.Empty;
            //var ts = PumpWorkTime;
            //if(ts.Days != 0)
            //{
            //    timeStr += ts.Days + "天";
            //}
            //if(ts.Hours != 0)
            //{
            //    timeStr += ts.Hours + "小时";
            //}
            //if(ts.Minutes != 0)
            //{
            //    timeStr += ts.Minutes + "分";
            //}
            //timeStr += ts.Seconds + "秒";
            //return timeStr;
        }

        #endregion

        #endregion
    }
}