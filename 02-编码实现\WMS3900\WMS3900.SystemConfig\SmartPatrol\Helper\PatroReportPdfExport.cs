﻿using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Helper
{
    public static class PatroReportPdfExport
    {
        /// <summary>
        /// 巡检结果导出PDF
        /// </summary>
        /// <param name="outputPath"></param>
        /// <param name="units"></param>
        public static void GenerateInspectionPdf(string outputPath, InspectionHierarchy units)
        {
            Document document = new Document(PageSize.A4, 36, 36, 36, 36);
            var writer = PdfWriter.GetInstance(document, new FileStream(outputPath, FileMode.Create));

            // 注册页脚
            writer.PageEvent = new HeaderFooterEvent();

            document.Open();

            PdfPTable table = new PdfPTable(6);
            table.WidthPercentage = 100;
            float[] columnWidths = { 0.8f, 1f, 2f, 0.5f, 2f, 3.7f };
            table.SetWidths(columnWidths);

            // 添加表头
            AddHeader(table);

            // 添加数据行
            for(int i = 0; i < units.FirstLayer.Count; i++)
            {
                AddInspectionUnit(table, units.FirstLayer[i], i + 1);
            }

            document.Add(table);
            document.Close();
        }

        #region 私有方法

        /// <summary>
        /// 添加表头
        /// </summary>
        /// <param name="table"></param>
        private static void AddHeader(PdfPTable table)
        {
            var headers = new[] { "序号", "巡检单元", "子项", "状态", "巡检项", "巡检结果" };
            foreach(var header in headers)
            {
                PdfPCell cell = new PdfPCell(new Phrase(header, GetFont())) { HorizontalAlignment = Element.ALIGN_CENTER, VerticalAlignment = Element.ALIGN_MIDDLE };
                cell.BackgroundColor = new BaseColor(247, 250, 255);
                table.AddCell(cell);
            }
        }

        /// <summary>
        /// 获取字体格式
        /// </summary>
        /// <returns></returns>
        private static Font GetFont()
        {
            BaseFont baseFont = BaseFont.CreateFont("C:/Windows/Fonts/simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            return new Font(baseFont, 10);
        }

        /// <summary>
        /// 巡检数据填充
        /// </summary>
        /// <param name="table"></param>
        /// <param name="unit">巡检三层结构数据</param>
        /// <param name="i"></param>
        private static void AddInspectionUnit(PdfPTable table, OneUnitLayer unit, int i)
        {
            int totalRows = 0;

            // 计算当前巡检单元的总行数
            foreach(var subItem in unit.SecondLayer)
            {
                totalRows += subItem.LeafProperties.Count;
            }

            // 添加序号
            PdfPCell numCell = new PdfPCell(new Phrase($"{i}", GetFont()))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                VerticalAlignment = Element.ALIGN_MIDDLE
            };
            numCell.Rowspan = totalRows;
            table.AddCell(numCell);

            // 添加巡检单元名称（合并单元格）
            PdfPCell unitCell = new PdfPCell(new Phrase($"{unit.UnitName}", GetFont()))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                VerticalAlignment = Element.ALIGN_MIDDLE
            };
            unitCell.Rowspan = totalRows;
            table.AddCell(unitCell);

            // 递归添加子项及其巡检项
            int rowIndex = 0;
            foreach(var subItem in unit.SecondLayer)
            {
                int inspectionItemCount = subItem.LeafProperties.Count;

                // 添加子项名称和状态（合并单元格）
                PdfPCell subNameCell = new PdfPCell(new Phrase(subItem.PropertyName, GetFont()))
                {
                    HorizontalAlignment = Element.ALIGN_CENTER,
                    VerticalAlignment = Element.ALIGN_MIDDLE
                };
                subNameCell.Rowspan = inspectionItemCount;
                table.AddCell(subNameCell);

                PdfPCell statusCell = new PdfPCell(new Phrase(subItem.StatusDescription, GetFont()))
                {
                    HorizontalAlignment = Element.ALIGN_CENTER,
                    VerticalAlignment = Element.ALIGN_MIDDLE
                };
                statusCell.Rowspan = inspectionItemCount;
                table.AddCell(statusCell);

                // 添加每个巡检项
                foreach(var item in subItem.LeafProperties)
                {
                    table.AddCell(new PdfPCell(new Phrase(item.Name, GetFont()))
                    {
                        HorizontalAlignment = Element.ALIGN_CENTER,
                        VerticalAlignment = Element.ALIGN_MIDDLE
                    });
                    table.AddCell(new PdfPCell(new Phrase(item.Value.ToString(), GetFont()))
                    {
                        HorizontalAlignment = Element.ALIGN_CENTER,
                        VerticalAlignment = Element.ALIGN_MIDDLE
                    });
                    rowIndex++;
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// 页脚自定义格式
    /// </summary>
    public class HeaderFooterEvent : PdfPageEventHelper
    {
        private BaseFont _baseFont = BaseFont.CreateFont("C:/Windows/Fonts/simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

        public override void OnEndPage(PdfWriter writer, Document document)
        {
            // 添加页脚
            PdfPTable table = new PdfPTable(1);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 1f });

            PdfPCell cell = new PdfPCell(new Phrase($"第{writer.PageNumber}页", new Font(_baseFont, 10)))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                Border = Rectangle.NO_BORDER,
                PaddingBottom = 5
            };
            table.AddCell(cell);

            // 确保表格宽度与页面相匹配
            float tableWidth = document.PageSize.Width - document.LeftMargin - document.RightMargin;
            table.TotalWidth = tableWidth;
            table.LockedWidth = true;

            table.WriteSelectedRows(0, -1, 36, 36, writer.DirectContent);
        }
    }
}