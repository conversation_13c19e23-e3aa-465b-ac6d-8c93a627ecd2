﻿using System;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment.SIA3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900DeviceOperControl : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        #endregion

        public UC_SIA3900DeviceOperControl()
        {
            InitializeComponent();
        }

        #region 公共方法

        internal void SetTragetDevice(SIA3900Equipment device)
        {
            _device = device;
            EnumOperate.BandEnumToCmb(cmbModeType, typeof(eGbDeviceMeasureMode));
            EnumOperate.BandEnumToCmb(cmbRange, typeof(eDeviceRangeModel));
        }

        #endregion

        #region 流程控制

        #region 无参

        /// <summary>
        /// 启动测量
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMeasure_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Measure);

                FpiMessageBox.ShowInfo($"触发启动测量成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 标样核查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCheck_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Check);

                FpiMessageBox.ShowInfo($"触发标样核查成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 零点核查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBlankCheck_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.BlankCheck);

                FpiMessageBox.ShowInfo($"触发零点核查成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 跨度核查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCheck_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.RangeCheck);

                FpiMessageBox.ShowInfo($"触发跨度核查成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 空白测试
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBlankTest_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.BlankTest);

                FpiMessageBox.ShowInfo($"触发空白测试成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 平行样测试
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnParallel_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Parallel);

                FpiMessageBox.ShowInfo($"触发平行样测试成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 加标回收
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Add);

                FpiMessageBox.ShowInfo($"触发加标回收成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 空白校准
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBlankCalibration_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.BlankCalibration);

                FpiMessageBox.ShowInfo($"触发空白校准成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 初始化（复位排空清洗）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnInitialize_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Initialize);

                FpiMessageBox.ShowInfo($"触发初始化（复位排空）成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 停止测试
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStopMeasture_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.StopMeasture);

                FpiMessageBox.ShowInfo($"触发停止测试成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 仪器重启
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReStart_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.ReStart);

                FpiMessageBox.ShowInfo($"触发仪器重启成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 标定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDemarcate_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Demarcate);

                FpiMessageBox.ShowInfo($"触发标定成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 信号调整
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSignalTransformate_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.SignalTransformate);

                FpiMessageBox.ShowInfo($"触发信号调整成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 管路冲洗
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLineFlush_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.LineFlush);

                FpiMessageBox.ShowInfo($"触发管路冲洗成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 试剂导入
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReagentIntroduct_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.ReagentIntroduct);

                FpiMessageBox.ShowInfo($"触发试剂导入成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 仪表标定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnEquipCalibrate_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.EquipCalibrate);

                FpiMessageBox.ShowInfo($"触发仪表标定成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 量程校准
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCalibration_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.RangeCalibration);

                FpiMessageBox.ShowInfo($"触发量程校准成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 设备自诊断
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelfCheck_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.SelfCheck);

                FpiMessageBox.ShowInfo($"触发设备自诊断成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 测标液
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTraceSolution_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.TraceSolution);

                FpiMessageBox.ShowInfo($"触发测标液成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 系统排空(复位排空清洗)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnEmpty_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Empty);

                FpiMessageBox.ShowInfo($"触发系统排空（复位排空）成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 清洗（检测室清洗）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnWash_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Wash);

                FpiMessageBox.ShowInfo($"触发清洗（检测室清洗）成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 一键维护
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMaintain_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Maintain);

                FpiMessageBox.ShowInfo($"触发一键维护成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 多点线性
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLinearity_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.Linearity);

                FpiMessageBox.ShowInfo($"触发多点线性成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 任意浓度核查
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnArbitraryConCheck_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.ArbitraryConCheck);

                FpiMessageBox.ShowInfo($"触发任意浓度核查成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 带参

        /// <summary>
        /// 校时
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTimeCalibrate_Click(object sender, EventArgs e)
        {
            try
            {
                _device.SetDeviceTime(DateTime.Now);

                FpiMessageBox.ShowInfo($"触发设备时间校准成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 模式设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnModeSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(cmbModeType.SelectedIndex == -1)
                {
                    throw new Exception("请选择设置模式！");
                }
                _device.SetDeviceMode((eGbDeviceMeasureMode)cmbModeType.SelectedValue);

                FpiMessageBox.ShowInfo($"模式设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 消解温度设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDigestTemperate_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtDigestTemp.Text, out int digestTemp))
                {
                    throw new Exception("消解温度输入不合法！");
                }
                _device.SetDigestTemp(digestTemp);

                FpiMessageBox.ShowInfo($"消解温度设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 消解时长设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDigestTime_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtDigestTime.Text, out int digestTime))
                {
                    throw new Exception("消解温度输入不合法！");
                }
                _device.SetDigestTime(digestTime);

                FpiMessageBox.ShowInfo($"消解时长设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #region 间隔

        /// <summary>
        /// 测量间隔设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMeasureTimeStamp_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtMeasureTimeStamp.Text, out int measureTimeStamp))
                {
                    throw new Exception("测量间隔输入不合法！");
                }
                _device.SetMeasureStamp(measureTimeStamp);

                FpiMessageBox.ShowInfo($"测量间隔设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 零点核查间隔设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnZeroCheckTimeStamp_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtZeroCheckTimeStamp.Text, out int zeroCheckTimeStamp))
                {
                    throw new Exception("零点核查间隔输入不合法！");
                }
                _device.SetZeroCheckStamp(zeroCheckTimeStamp);

                FpiMessageBox.ShowInfo($"零点核查间隔设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 跨度核查间隔设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSpanCheckDateStamp_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtSpanCheckDateStamp.Text, out int spanCheckDateStamp))
                {
                    throw new Exception("跨度核查间隔输入不合法！");
                }
                _device.SetRangeCheckStamp(spanCheckDateStamp);

                FpiMessageBox.ShowInfo($"跨度核查间隔设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 标样核查间隔设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnGuidSampleCheckTimeStamp_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtGuidSampleCheckTimeStamp.Text, out int guidSampleCheckTimeStamp))
                {
                    throw new Exception("标样核查间隔输入不合法！");
                }
                _device.SetGuidSampleCheckStamp(guidSampleCheckTimeStamp);

                FpiMessageBox.ShowInfo($"标样核查间隔设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 标定间隔
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCalibrateTimeStamp_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtCalibrateTimeStamp.Text, out int calibrateTimeStamp))
                {
                    throw new Exception("标定间隔输入不合法！");
                }
                _device.SetCalibrateStamp(calibrateTimeStamp);

                FpiMessageBox.ShowInfo($"标定间隔设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        /// <summary>
        /// 修改当前量程
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeModify_Click(object sender, EventArgs e)
        {
            try
            {
                if(cmbRange.SelectedIndex == -1)
                {
                    throw new Exception("请选择量程！");
                }
                _device.SetCurrentRange((eDeviceRangeModel)cmbRange.SelectedValue);

                FpiMessageBox.ShowInfo($"修改当前量程成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 特殊水样测量流程
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOperSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtOperParam.Text, out int operParam))
                {
                    throw new Exception("特殊水样测量流程参数输入不合法！");
                }
                _device.SpecialOperSet(operParam);

                FpiMessageBox.ShowInfo($"特殊水样测量流程触发成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }


        #region 点位触发

        /// <summary>
        /// 控制机箱风扇
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnControlllerFan_Click(object sender, EventArgs e)
        {
            try
            {
                eSIA3900ControlType state = rdbControlllerFanOpen.Checked ? eSIA3900ControlType.开启状态 : eSIA3900ControlType.关闭状态;
                _device.WriteSwitchValueToDevice(0x20, (short)state);

                FpiMessageBox.ShowInfo($"控制机箱风扇触发成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 检测室风扇
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTestRoomFan_Click(object sender, EventArgs e)
        {
            try
            {
                eSIA3900ControlType state = rdbTestRoomFanOpen.Checked ? eSIA3900ControlType.开启状态 : eSIA3900ControlType.关闭状态;
                _device.WriteSwitchValueToDevice(0x21, (short)state);

                FpiMessageBox.ShowInfo($"检测室风扇触发成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 光源
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLight_Click(object sender, EventArgs e)
        {
            try
            {
                eSIA3900ControlType state = rdbLightOpen.Checked ? eSIA3900ControlType.开启状态 : eSIA3900ControlType.关闭状态;
                _device.WriteSwitchValueToDevice(0x22, (short)state);

                FpiMessageBox.ShowInfo($"光源触发成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 蠕动泵
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnPump_Click(object sender, EventArgs e)
        {
            try
            {
                eSIA3900ControlType state = rdbPumpOpen.Checked ? eSIA3900ControlType.开启状态 : eSIA3900ControlType.关闭状态;
                _device.WriteSwitchValueToDevice(0x23, (short)state);

                FpiMessageBox.ShowInfo($"蠕动泵触发成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 上电磁阀
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpperRadiotube_Click(object sender, EventArgs e)
        {
            try
            {
                eSIA3900ControlType state = rdbUpperRadiotubeOpen.Checked ? eSIA3900ControlType.开启状态 : eSIA3900ControlType.关闭状态;
                _device.WriteSwitchValueToDevice(0x29, (short)state);

                FpiMessageBox.ShowInfo($"上电磁阀触发成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 下电磁阀
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBotRadiotube_Click(object sender, EventArgs e)
        {
            try
            {
                eSIA3900ControlType state = rdbBotRadiotubeOpen.Checked ? eSIA3900ControlType.开启状态 : eSIA3900ControlType.关闭状态;
                _device.WriteSwitchValueToDevice(0x2A, (short)state);

                FpiMessageBox.ShowInfo($"下电磁阀触发成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #endregion

        #endregion 
    }
}
