using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.DB;
using Fpi.HB.Business.HisData;
using Fpi.WMS3000.SystemConfig;
using Fpi.WMS3000.UI.Report;
using Fpi.Xml;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using HorizontalAlignment = System.Windows.Forms.HorizontalAlignment;

namespace Fpi.WMS3000.UI.DataQuery
{
    public partial class ReportOutputUC : UserControl
    {
        #region 属性、字段
        private QueryGroup _querGroup = null;
        private ArrayList statNodes = null;
        private ArrayList fieldList = null;
        //private string measPointId;
        //private int measPointIndex;
        private string querySql = ""; //查询语句
        private int[] waterLevel = new int[1];

        /// <summary>
        /// 一个月最多31天
        /// </summary>
        private const int MAX_MONTH_DAYS = 31;

        private const string PREFIX = "F";

        //数据表
        private string table;

        /// <summary>
        /// 待查询的节点
        /// </summary>
        private List<QueryNode> _queryNodes = null;

        private ReportManager _reportManager;



        private ReportType reportType;
        /// <summary>
        /// 报表类型
        /// </summary>
        public ReportType ReportType
        {
            get { return reportType; }
            set { reportType = value; }
        }

        private string[] rowNames = null;



        #endregion

        #region 构造

        public ReportOutputUC()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void ReportOutputUC_Load(object sender, EventArgs e)
        {
            radDay.Checked = true;

            this.numYear.Value = DateTime.Now.Year;
            this.numMonth.Value = DateTime.Now.Month;
            this.numDay.Value = DateTime.Now.Day;
            this.lvReport.Clear();

            statNodes = new ArrayList();
            fieldList = new ArrayList();

            this._reportManager = ReportManager.GetInstance();
            this._queryNodes = new List<QueryNode>();
            if(this._reportManager.queryGroups.GetCount() > 0)
            {
                foreach(QueryGroup queryGroup in this._reportManager.queryGroups)
                {
                    foreach(QueryNode queryNode in queryGroup.queryNodes)
                    {
                        this._queryNodes.Add(queryNode);
                    }
                }
            }


            SetReportViewHead();
        }

        private void btnReportQuery_Click(object sender, EventArgs e)
        {
            SetReportViewHead();
            this.Cursor = Cursors.WaitCursor;
            try
            {
                if(chkCycle.Checked)
                {
                    table = "concen_cycle";
                }
                else
                {
                    if(CustomTimerManager.GetInstance().dataAnalySisTimer != null || CustomTimerManager.GetInstance().quanZhouTimer != null)
                    {
                        table = "fpi_sys_min";
                    }
                    else
                    {
                        table = "fpi_sys";
                    }
                }

                rowNames = ReportConst.GetStatTimes(this.reportType, (int)this.numYear.Value, (int)this.numMonth.Value);
                this.lvReport.Items.Clear();
                string beginTime, endTime;
                GetBeginEndTime(out beginTime, out endTime);
                string allsql = this.BuildSelectAllString() + " from " + table + " where DateTime>='" + beginTime + "' and DateTime<='" + endTime + "'";
                float[] alldata = DbAccess.GetFloatValues(allsql);
                for(int j = 0; j < alldata.Length; j++)
                {
                    ValueNode valueNode = DataManager.GetInstance().GetVarNode(_queryNodes[j].fullNodeId) as ValueNode;
                    if(!string.IsNullOrEmpty(_queryNodes[j].unitId))//转换单位
                    {
                        alldata[j] = (float)UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, alldata[j], _queryNodes[j].unitId);
                    }
                }
                ArrayList reportData = new ArrayList();
                //平均值，最大值，最小值，样本数
                int dataCols = this.lvReport.Columns.Count - 1; //数据列数
                int dataRows = 0; //数据行数
                float[] sum = new float[dataCols];
                float[] avg = new float[dataCols];
                float[] max = new float[dataCols - 1];
                float[] min = new float[dataCols - 1];
                float[] all = new float[dataCols - 1];
                int[] validValues = new int[dataCols];//有效行数 

                //初始化
                for(int i = 0; i < dataCols - 1; i++)
                {
                    sum[i] = 0.0f;
                    avg[i] = 0.0f;
                    max[i] = 0.0f;
                    validValues[i] = dataRows;
                    min[i] = float.MaxValue;
                    all[i] = 0.0f;
                }

                string averageStr = this.BuildAverSelectString() + " from " + table + " where DateTime>='" + beginTime + "' and DateTime<='" + endTime + "'";
                avg = DbAccess.GetFloatValues(averageStr);
                for(int j = 0; j < avg.Length; j++)
                {
                    ValueNode valueNode = DataManager.GetInstance().GetVarNode(_queryNodes[j].fullNodeId) as ValueNode;
                    if(!string.IsNullOrEmpty(_queryNodes[j].unitId))//转换单位
                    {
                        avg[j] = (float)UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, avg[j], _queryNodes[j].unitId);
                    }
                }
                for(int i = 0; i < this.rowNames.Length; i++)
                {
                    GetBeginEndTime(i, out beginTime, out endTime);
                    DateTime beginDateTime;
                    DateTime endDateTime;
                    if((!DateTime.TryParse(beginTime, out beginDateTime)) || (!DateTime.TryParse(endTime, out endDateTime)))
                    {
                        continue;
                    }
                    String sql = this.BuildSelectString() + " from " + table + " where DateTime>='" + beginTime + "' and DateTime<='" + endTime + "'";
                    float[] data = DbAccess.GetFloatValues(sql);
                    for(int j = 0; j < _queryNodes.Count; j++)
                    {
                        ValueNode valueNode = DataManager.GetInstance().GetVarNode(_queryNodes[j].fullNodeId) as ValueNode;
                        if(!string.IsNullOrEmpty(_queryNodes[j].unitId))//转换单位
                        {
                            data[j] = (float)UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, data[j], _queryNodes[j].unitId);
                        }
                    }

                    if(data != null)
                    {
                        reportData.Add(data);
                        this.lvReport.Items.Add(GetItem(rowNames[i], data));
                    }
                    else
                    {
                        this.lvReport.Items.Add(GetItem(rowNames[i], "---", _queryNodes.Count));
                    }
                }
                dataRows = reportData.Count;
                if(dataRows == 0)
                {
                    this.lvReport.Items.Add(GetItem("平均值", "---", _queryNodes.Count));
                    this.lvReport.Items.Add(GetItem("最大值", "---", _queryNodes.Count));
                    this.lvReport.Items.Add(GetItem("最小值", "---", _queryNodes.Count));
                    this.lvReport.Items.Add(GetItem("总量", "---", _queryNodes.Count));
                }
                else
                {
                    for(int row = 0; row < dataRows; row++)
                    {
                        float[] data = (float[])reportData[row];
                        for(int col = 0; col < dataCols - 1; col++)
                        {
                            if(data[col] != 0.0f)
                            {
                                sum[col] += data[col];
                                all[col] += data[col];
                                if(data[col] > max[col])
                                {
                                    max[col] = data[col];
                                }

                                if(data[col] < min[col])
                                {
                                    min[col] = data[col];
                                }

                            }
                            else
                            {
                                validValues[col]--;
                            }
                        }
                    }

                    for(int col = 0; col < dataCols - 1; col++)
                    {
                        if(validValues[col] > 0)
                        {
                            avg[col] = sum[col] / validValues[col];
                        }
                    }

                    for(int col = 0; col < dataCols - 1; col++)
                    {
                        if(Math.Abs(min[col] - float.MaxValue) <= 0)
                        {
                            min[col] = 0.0f;
                        }
                        if(this.lvReport.Columns[col + 1].Text.Contains("累计流量"))
                        {
                            alldata[col] = float.NaN;
                        }
                    }

                    this.lvReport.Items.Add(GetItem("平均值", avg));
                    this.lvReport.Items.Add(GetItem("最大值", max));
                    this.lvReport.Items.Add(GetItem("最小值", min));
                    this.lvReport.Items.Add(GetItem("总量", alldata));
                }

                this.lvReport.Update();
            }
            catch(Exception ex)
            {
                MessageBox.Show("报表查询错误:" + ex.Message, "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            this.Cursor = Cursors.Arrow;

        }

        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                int num = 1;
                Cursor = Cursors.WaitCursor;
                this.Enabled = false;

                if(this.lvReport.Items.Count <= 0)
                {
                    this.Enabled = true;
                    Cursor = Cursors.Arrow;
                    MessageBox.Show("无数据可导出", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                //导出到excel,拷贝模板文件
                string reportPath = System.Windows.Forms.Application.StartupPath + "\\report\\";
                if(!Directory.Exists(reportPath))
                {
                    Directory.CreateDirectory(reportPath);
                }
                var saveFileDlg = new SaveFileDialog
                {
                    InitialDirectory = reportPath,
                    FileName = GetReportName(),
                    Filter = @"(*.xls)|*.xls"
                };

                if(saveFileDlg.ShowDialog() != DialogResult.OK)
                {
                    this.Enabled = true;
                    return;
                }

                //按模板导出
                string reportFilename = /*reportPath + "\\" + GetReportName() + ".xls";//*/saveFileDlg.FileName;// + ".xls";
                string templatePath = System.Windows.Forms.Application.StartupPath + "\\template\\";
                string templateFilename = templatePath;

                switch(ReportType)
                {
                    case ReportType.DayReport:
                        templateFilename += "day.xls";
                        break;
                    case ReportType.MonthReport:
                        templateFilename += "month.xls";
                        break;
                    case ReportType.YearReport:
                        templateFilename += "year.xls";
                        break;
                }

                if(!System.IO.File.Exists(templateFilename))
                {
                    Enabled = true;
                    MessageBox.Show("报表模板不存在，请先生成报表模板！", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Cursor = Cursors.Arrow;
                    return;
                }
                FileStream file = File.OpenRead(templateFilename);
                HSSFWorkbook hssfworkbook = new HSSFWorkbook(file);
                Sheet sheet = hssfworkbook.GetSheetAt(0);
                //设置时间
                Row timeRow = GetRow(sheet, 1);
                if(ReportType == ReportType.DayReport)
                {
                    GetCell(timeRow, 0).SetCellValue(this.numYear.Value + "年 " + this.numMonth.Value + "月 " + this.numDay.Value + "日");
                }
                else if(ReportType == ReportType.MonthReport)
                {
                    GetCell(timeRow, 0).SetCellValue(this.numYear.Value + "年 " + this.numMonth.Value + "月 " + "   日");
                }
                else if(ReportType == ReportType.YearReport)
                {
                    GetCell(timeRow, 0).SetCellValue(this.numYear.Value + "年   月   日");
                }

                #region 设置excel表头

                string pointName = VarConfig.GetValue("PointName");
                string pointNo = VarConfig.GetValue("PointNum");
                Row headRow = GetRow(sheet, 3);
                GetCell(headRow, 1).SetCellValue(pointName);
                GetCell(headRow, 4).SetCellValue(pointNo);
                Row head2Row = GetRow(sheet, 4);
                foreach(QueryNode queryNode in _queryNodes)
                {
                    string headtext = queryNode.name + "\r\n(" + queryNode.unitId + ")";
                    if(headtext.ToUpper().Contains("PH"))
                    {
                        headtext = "PH值";
                    }
                    if(headtext.Contains("高锰酸盐"))
                    {
                        headtext = "CODcr" + "\r\n(" + queryNode.unitId + ")";
                    }

                    if(ReportType == ReportType.DayReport)
                    {
                        if(headtext.Contains("瞬时流量"))
                        {
                            headtext = "小时流量" + "\r\n（m3）"; ;
                        }
                    }
                    //else
                    //{
                    //    if(headtext.Contains("瞬时流量"))
                    //    {
                    //        continue;
                    //    }
                    //}

                    GetCell(head2Row, num).SetCellValue(headtext);
                    num++;
                }
                GetCell(head2Row, num).SetCellValue("备注");

                #endregion

                int itemrow = lvReport.Items.Count;
                int itemcol = lvReport.Columns.Count;
                for(int i = 0; i < itemrow; i++)
                {
                    ListViewItem item = lvReport.Items[i];
                    Row item2Row = GetRow(sheet, 7 + i);
                    GetCell(item2Row, 0).SetCellValue(item.SubItems[0].Text);
                    for (int j = 1; j < item.SubItems.Count; j++)
                    {
                        if(lvReport.Columns[j].Text.ToUpper().Contains("PH") && item.Text.Contains("总量"))
                        {
                            GetCell(item2Row, j).SetCellValue("\\");
                        }
                        else if(lvReport.Columns[j].Text.Contains("累计流量"))
                        {
                            if(item.Text.Contains("最小") || item.Text.Contains("最大") ||
                                item.Text.Contains("均值"))
                            {
                                GetCell(item2Row, j).SetCellValue("\\");
                            }
                            else
                            {
                                GetCell(item2Row, j).SetCellValue(item.SubItems[j].Text);
                            }
                        }
                        else
                        {
                            GetCell(item2Row, j).SetCellValue(item.SubItems[j].Text);
                        }
                    }
                }

                //画边框线
                NPOI.SS.UserModel.Font font = hssfworkbook.CreateFont();
                font.FontName = "宋体";
                font.FontHeightInPoints = 9;
                CellStyle blackBorder = hssfworkbook.CreateCellStyle();
                blackBorder.BorderBottom = CellBorderType.MEDIUM;
                blackBorder.BorderLeft = CellBorderType.MEDIUM;
                blackBorder.BorderRight = CellBorderType.MEDIUM;
                blackBorder.BorderTop = CellBorderType.MEDIUM;
                blackBorder.BottomBorderColor = HSSFColor.BLACK.index;
                blackBorder.LeftBorderColor = HSSFColor.BLACK.index;
                blackBorder.RightBorderColor = HSSFColor.BLACK.index;
                blackBorder.TopBorderColor = HSSFColor.BLACK.index;
                blackBorder.WrapText = true;//自动换行
                blackBorder.SetFont(font);
                for(int iRow = 4; iRow <= itemrow + 6; iRow++)
                {
                    for(int iCol = 1; iCol < itemcol; iCol++)
                    {
                        Row row = GetRow(sheet, iRow);
                        Cell cell = GetCell(row, iCol);
                        cell.CellStyle = blackBorder;
                    }
                }

                FileStream reportFile = new FileStream(reportFilename, FileMode.Create);
                hssfworkbook.Write(reportFile);
                file.Close();
                reportFile.Close();
                FileInfo fileInfo = new FileInfo(reportFilename);
                if(fileInfo.Length > 1024 * 1024 * 10)//大于10M提示
                {
                    if(MessageBox.Show("导出文件成功，数据文件较大是否打开?", "系统提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(reportFilename);
                    }
                }
                else
                {
                    System.Diagnostics.Process.Start(reportFilename);
                }
                Enabled = true;
                Cursor = Cursors.Arrow;
            }
            catch(Exception ex)
            {
                SystemHelper.InfoMessage(ex.Message);
            }
            finally
            {
                this.Enabled = true;
            }
        }

        private void btnTxtExport_Click(object sender, EventArgs e)
        {
            this.Enabled = false;
            try
            {
                string reportPath = System.Windows.Forms.Application.StartupPath + "\\report\\";
                if(!Directory.Exists(reportPath))
                {
                    Directory.CreateDirectory(reportPath);
                }

                string reportFilename = reportPath + GetReportName() + ".txt";
                if(ReportHelper.SaveListViewToTxtFile(this.lvReport, reportFilename))
                {
                    System.Diagnostics.Process.Start(reportFilename);
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show("导出文本文件失败:" + ex.Message, "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            this.Enabled = true;

        }

        #region 改变报表类型

        private void radDay_Click(object sender, EventArgs e)
        {
            this.reportType = ReportType.DayReport;
            numDay.Visible = true;
            lblDay.Visible = true;
            numMonth.Visible = true;
            lblMonth.Visible = true;
        }

        private void radMonth_Click(object sender, EventArgs e)
        {
            this.reportType = ReportType.MonthReport;
            numDay.Visible = false;
            lblDay.Visible = false;
            numMonth.Visible = true;
            lblMonth.Visible = true;
        }

        private void radSeason_Click(object sender, EventArgs e)
        {
            this.reportType = ReportType.SeasonReport;
            numDay.Visible = false;
            lblDay.Visible = false;
            numMonth.Visible = false;
            lblMonth.Visible = false;
        }

        private void radYear_Click(object sender, EventArgs e)
        {
            int a = 1;
            this.reportType = ReportType.YearReport;
            numDay.Visible = false;
            lblDay.Visible = false;
            numMonth.Visible = false;
            lblMonth.Visible = false;
        }

        #endregion End

        #endregion

        #region 私有方法

        /// <summary>
        /// 得到统计表头
        /// </summary>
        private void SetReportViewHead()
        {
            this.lvReport.Columns.Clear();
            using(Graphics g = this.CreateGraphics())
            {
                //时间
                this.lvReport.Columns.Add("数据时间", 140, HorizontalAlignment.Center);
                //数据列
                foreach(QueryNode queryNode in _queryNodes)
                {

                    string headtext = queryNode.name + "(" + queryNode.unitId + ")";
                    this.lvReport.Columns.Add(headtext, (int)g.MeasureString(headtext, this.lvReport.Font).Width + 10, HorizontalAlignment.Center);
                }
                //备注
                this.lvReport.Columns.Add("备注", 140, HorizontalAlignment.Center);
            }

        }

        /// <summary>
        /// 得到查询语句的Select部分
        /// </summary>
        /// <returns></returns>
        private string BuildSelectString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select ");
            foreach(QueryNode queryNode in _queryNodes)
            {
                switch(this.reportType)
                {
                    case ReportType.DayReport:
                        sb.Append("avg(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                    case ReportType.MonthReport:
                        sb.Append("avg(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                    case ReportType.YearReport:
                        sb.Append("avg(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                }
            }
            return sb.ToString().TrimEnd(',');
        }
        /// <summary>
        /// 添加总量select语句
        /// </summary>
        /// <returns></returns>
        private string BuildSelectAllString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select ");
            foreach(QueryNode queryNode in _queryNodes)
            {

                switch(this.reportType)
                {
                    case ReportType.DayReport:
                        sb.Append("sum(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                    case ReportType.MonthReport:
                        sb.Append("sum(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                    case ReportType.YearReport:
                    case ReportType.SeasonReport:
                        sb.Append("sum(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                }
            }
            return sb.ToString().TrimEnd(',');
        }

        /// <summary>
        /// 取得平均值的Select部分（为了日报表的平均值和周报表月报表等对应） Added by Tianyi_Shu
        /// </summary>
        /// <returns></returns>
        private string BuildAverSelectString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select ");

            foreach(QueryNode queryNode in _queryNodes)
            {

                switch(this.reportType)
                {
                    case ReportType.DayReport:
                        sb.Append("avg(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                    case ReportType.MonthReport:
                        sb.Append("avg(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                    case ReportType.YearReport:
                    case ReportType.SeasonReport:
                        sb.Append("avg(").Append(PREFIX + queryNode.fullNodeId + ")").Append(",");
                        break;
                }

            }

            return sb.ToString().TrimEnd(',');
        }

        /// <summary>
        /// 得到查询开始结束时间
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        private void GetBeginEndTime(out string beginTime, out string endTime)
        {
            beginTime = "";
            endTime = "";
            int days;
            switch(reportType)
            {
                case ReportType.DayReport:
                    beginTime = this.numYear.Value + "-" + this.numMonth.Value + "-" + this.numDay.Value + " 0:0:0";
                    endTime = this.numYear.Value + "-" + this.numMonth.Value + "-" + this.numDay.Value + " 23:59:59";
                    break;
                case ReportType.MonthReport:
                    beginTime = this.numYear.Value + "-" + this.numMonth.Value + "-1 0:0:0";
                    days = DateTime.DaysInMonth((int)this.numYear.Value, (int)this.numMonth.Value);
                    endTime = this.numYear.Value + "-" + this.numMonth.Value + "-" + days + " 23:59:59";
                    break;
                case ReportType.YearReport:
                    beginTime = this.numYear.Value + "-1-1 0:0:0";
                    endTime = this.numYear.Value + "-12-31 23:59:59";
                    break;

            }
        }

        /// <summary>
        /// 得到一个时间段的开始结束时间
        /// </summary>
        /// <param name="number">序号:从0开始计数</param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        private void GetBeginEndTime(int number, out string beginTime, out string endTime)
        {
            beginTime = "";
            endTime = "";
            int days;
            switch(reportType)
            {
                case ReportType.DayReport:
                    beginTime = this.numYear.Value + "-" + this.numMonth.Value + "-" + this.numDay.Value
                        + " " + number + ":0:0";
                    endTime = this.numYear.Value + "-" + this.numMonth.Value + "-" + this.numDay.Value
                        + " " + number + ":59:59";
                    break;
                case ReportType.MonthReport:
                    beginTime = this.numYear.Value + "-" + this.numMonth.Value + "-" + (number + 1) + " 0:0:0";
                    endTime = this.numYear.Value + "-" + this.numMonth.Value + "-" + (number + 1) + " 23:59:59";
                    break;
                case ReportType.YearReport:
                    days = DateTime.DaysInMonth((int)this.numYear.Value, number + 1);
                    beginTime = this.numYear.Value + "-" + (number + 1) + "-1 0:0:0";
                    endTime = this.numYear.Value + "-" + (number + 1) + "-" + days + " 23:59:59";
                    break;
            }
        }

        /// <summary>
        /// 得到行Item
        /// </summary>
        /// <param name="text">行文本</param>
        /// <param name="data">行数据</param>
        /// <returns></returns>
        private ListViewItem GetItem(string text, float[] data)
        {
            ListViewItem item = new ListViewItem();
            item.Text = text;
            if(data != null)
            {
                for(int j = 0; j < data.Length; j++)
                {
                    if(float.IsNaN(data[j]))
                    {
                        item.SubItems.Add("---");
                    }
                    else
                    {
                        item.SubItems.Add(data[j].ToString("F3"));
                    }
                }
            }
            return item;
        }

        private ListViewItem GetItem(string text, string value, int rowCount)
        {
            ListViewItem item = new ListViewItem();
            item.Text = text;
            for(int j = 0; j < rowCount; j++)
            {
                item.SubItems.Add(value);
            }

            return item;
        }

        /// <summary>
        /// 得到报表名称
        /// </summary>
        /// <returns></returns>
        private string GetReportName()
        {
            string datetime = "";
            if(this.reportType == ReportType.DayReport)
            {
                datetime = this.numYear.Value + "年" + this.numMonth.Value + "月" + this.numDay.Value + "日";
            }
            else if(this.reportType == ReportType.MonthReport)
            {
                datetime = this.numYear.Value + "年" + this.numMonth.Value + "月";
            }
            else if(this.reportType == ReportType.YearReport)
            {
                datetime = this.numYear.Value + "年";
            }

            return ReportConst.ReportName[(int)reportType] + "(" + datetime + ")";
        }

        private Cell GetCell(Row row, int col)
        {

            Cell cell = row.GetCell(col);
            if(cell == null)
            {
                cell = row.CreateCell(col);
            }
            return cell;
        }

        private Row GetRow(Sheet sheet, int i)
        {
            Row row = sheet.GetRow(i);
            if(row == null)
            {
                row = sheet.CreateRow(i);
            }
            return row;
        }

        #endregion
    }
}
