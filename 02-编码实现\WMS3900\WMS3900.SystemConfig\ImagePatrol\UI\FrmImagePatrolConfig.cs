﻿using System;
using System.Windows.Forms;
using Fpi.Util.Reflection;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig.UI
{
    /// <summary>
    /// 图像巡检参数配置
    /// </summary>
    public partial class FrmImagePatrolConfig : UIForm
    {
        #region 构造

        public FrmImagePatrolConfig()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmSmartPatrolConfig_Load(object sender, EventArgs e)
        {
            // 先按顺序加载已启用项
            foreach(var unitType in ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList)
            {
                try
                {
                    ImageUnitSmartPatrolBase unit = (ImageUnitSmartPatrolBase)ReflectionHelper.CreateInstance(unitType);
                    var text = unit.UnitName;
                    if(!string.IsNullOrWhiteSpace(unit.Description))
                    {
                        text += $"（{unit.Description}）";
                    }
                    var node = tvUnitList.Nodes.Add(text);
                    node.Tag = unit;
                    node.Checked = true;
                }
                catch
                {
                }
            }

            // 再加载未启用项
            // 反射加载所有巡检项
            Type[] types = ReflectionHelper.GetChildTypes(typeof(ImageUnitSmartPatrolBase));
            foreach(Type type in types)
            {
                // 跳过已加载项
                if(!ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Contains(type.FullName))
                {
                    try
                    {
                        ImageUnitSmartPatrolBase unit = (ImageUnitSmartPatrolBase)ReflectionHelper.CreateInstance(type);
                        var text = unit.UnitName;
                        if(!string.IsNullOrWhiteSpace(unit.Description))
                        {
                            text += $"（{unit.Description}）";
                        }
                        var node = tvUnitList.Nodes.Add(text);
                        node.Tag = unit;
                    }
                    catch
                    {
                    }
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            // 巡检项启用情况
            ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Clear();
            foreach(TreeNode node in tvUnitList.Nodes)
            {
                if(node.Checked && node.Tag is ImageUnitSmartPatrolBase smartPatrolUnit)
                {
                    string typeName = smartPatrolUnit.GetType().FullName;
                    if(!ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Contains(typeName))
                    {
                        ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Add(typeName);
                    }
                }
            }

            ImagePatrolManager.GetInstance().Save();
            ImagePatrolManager.GetInstance().RebuildPatrolUnitList();
        }

        #endregion
    }
}