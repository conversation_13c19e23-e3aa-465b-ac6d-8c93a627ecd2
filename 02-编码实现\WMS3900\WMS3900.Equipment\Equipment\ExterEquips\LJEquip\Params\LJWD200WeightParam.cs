﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Fpi.Communication.Converter;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.Equipment.LJWD200
{
    /// <summary>
    /// 重量参数
    /// </summary>
    public class LJWD200WeightParam
    {
        #region 字段属性

        /// <summary>
        /// 各路参数
        /// </summary>
        public List<OneRoadWeightParam> AccessRoadParamList { get; set; } = new List<OneRoadWeightParam>();

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新各路重量参数
        /// </summary>
        /// <param name="data"></param>
        /// <param name="startIndex"></param>
        public void UpdateValue(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + AccessRoadParamList.Count * 4)
            {
                throw new Exception("读取各支路参数回应数据不完整！");
            }

            for(int i = 0; i < AccessRoadParamList.Count; i++)
            {
                var electricParam = AccessRoadParamList[i];
                electricParam.CurrentWeight = DataConverter.GetInstance().ToInt64(data, startIndex + i * 4) / 100000f;
            }
        }

        #endregion
    }

    /// <summary>
    /// 支路参数
    /// </summary>
    public class OneRoadWeightParam
    {
        #region 字段属性

        /// <summary>
        /// 配置参数
        /// </summary>
        [Description("配置参数")]
        public WeightConfig Config { get; private set; }

        /// <summary>
        /// 当前重量
        /// </summary>
        [Description("当前重量")]
        public double CurrentWeight { get; set; } = double.NaN;

        /// <summary>
        /// 占满重量的百分比
        /// </summary>
        public double CurrentPersent => double.IsNaN(CurrentWeight) ? double.NaN : CurrentWeight / Config.FullWeight;

        #endregion

        #region 构造

        public OneRoadWeightParam()
        {

        }

        public OneRoadWeightParam(WeightConfig config)
        {
            Config = config;
        }

        #endregion
    }
}