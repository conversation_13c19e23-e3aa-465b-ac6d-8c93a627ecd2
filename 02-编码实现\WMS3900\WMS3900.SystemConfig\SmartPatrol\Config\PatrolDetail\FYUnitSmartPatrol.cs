﻿using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 废液收集单元巡检执行类
    /// </summary>
    public class FYUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public FYUnitSmartPatrol()
        {
            UnitId = "WasteLiquid";
            UnitName = "废液收集单元";
            Description = "水站版";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            FYUnitPatrolResult result = new FYUnitPatrolResult();

            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<CodMnNH4QCDPatrolResult>();

                if(imagePatrolResult != null)
                {
                    result.CodMnNH4WasteWaterBucketState = imagePatrolResult.CodMnNH4WasteWaterBucketState;
                    result.CodMnNH4WasteTankBucketState = imagePatrolResult.CodMnNH4WasteTankBucketState;
                    result.CodMnNH4WaterBucketState = imagePatrolResult.CodMnNH4WaterBucketState;
                }
            }

            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<TPTNQCDPatrolResult>();

                if(imagePatrolResult != null)
                {
                    result.TPTNWasteWaterBucketState = imagePatrolResult.TPTNWasteWaterBucketState;
                    result.TPTNWasteTankBucketState = imagePatrolResult.TPTNWasteTankBucketState;
                    result.TPTNWaterBucketState = imagePatrolResult.TPTNWaterBucketState;
                }
            }

            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<FiveParamBucketPatrolResult>();

                if(imagePatrolResult != null)
                {
                    result.FiveParamWasteTankBucketState = imagePatrolResult.FiveParamWasteTankBucketState;
                    result.FiveParamWaterBucketState = imagePatrolResult.FiveParamWaterBucketState;
                }
            }

            return result;
        }

        #endregion
    }
}