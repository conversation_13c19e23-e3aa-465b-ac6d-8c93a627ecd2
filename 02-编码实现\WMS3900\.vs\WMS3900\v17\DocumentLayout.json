{"Version": 1, "WorkspaceRootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F7391C4B-70B6-4830-84F6-9D45EC022320}|WMS3900.Pollution\\Fpi.WMS3900.Pollution.csproj|e:\\01-数采软件\\01-wms3900\\02-编码实现\\wms3900\\wms3900.pollution\\ui\\dataquery\\uc\\uc_querydatareport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F7391C4B-70B6-4830-84F6-9D45EC022320}|WMS3900.Pollution\\Fpi.WMS3900.Pollution.csproj|solutionrelative:wms3900.pollution\\ui\\dataquery\\uc\\uc_querydatareport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F7391C4B-70B6-4830-84F6-9D45EC022320}|WMS3900.Pollution\\Fpi.WMS3900.Pollution.csproj|e:\\01-数采软件\\01-wms3900\\02-编码实现\\wms3900\\wms3900.pollution\\ui\\dataquery\\uc\\uc_queryhourpollutiondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F7391C4B-70B6-4830-84F6-9D45EC022320}|WMS3900.Pollution\\Fpi.WMS3900.Pollution.csproj|solutionrelative:wms3900.pollution\\ui\\dataquery\\uc\\uc_queryhourpollutiondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0E2971B7-6727-4FBB-9DE0-3CE857591DED}|WMS3900.RemoteProtocol\\Fpi.WMS3900.Remote.csproj|e:\\01-数采软件\\01-wms3900\\02-编码实现\\wms3900\\wms3900.remoteprotocol\\remoteprotocols\\gb2017protocol\\hjreceiver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0E2971B7-6727-4FBB-9DE0-3CE857591DED}|WMS3900.RemoteProtocol\\Fpi.WMS3900.Remote.csproj|solutionrelative:wms3900.remoteprotocol\\remoteprotocols\\gb2017protocol\\hjreceiver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\Config\\UIManager.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 15, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:140:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:137:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e5c86464-96be-4d7c-9a8b-abcb3bbf5f92}"}, {"$type": "Bookmark", "Name": "ST:136:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:138:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:139:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "HJReceiver.cs", "DocumentMoniker": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.RemoteProtocol\\RemoteProtocols\\GB2017Protocol\\HJReceiver.cs", "RelativeDocumentMoniker": "WMS3900.RemoteProtocol\\RemoteProtocols\\GB2017Protocol\\HJReceiver.cs", "ToolTip": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.RemoteProtocol\\RemoteProtocols\\GB2017Protocol\\HJReceiver.cs", "RelativeToolTip": "WMS3900.RemoteProtocol\\RemoteProtocols\\GB2017Protocol\\HJReceiver.cs", "ViewState": "AgIAAC4EAAAAAAAAAADgvzoEAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:19:20.047Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "UC_QueryHourPollutionData.cs", "DocumentMoniker": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryHourPollutionData.cs", "RelativeDocumentMoniker": "WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryHourPollutionData.cs", "ToolTip": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryHourPollutionData.cs", "RelativeToolTip": "WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryHourPollutionData.cs", "ViewState": "AgIAAJsAAAAAAAAAAAAQwHcAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T01:02:15.216Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "UC_QueryDataReport.cs", "DocumentMoniker": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryDataReport.cs", "RelativeDocumentMoniker": "WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryDataReport.cs", "ToolTip": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryDataReport.cs", "RelativeToolTip": "WMS3900.Pollution\\UI\\DataQuery\\UC\\UC_QueryDataReport.cs", "ViewState": "AgIAAFACAAAAAAAAAAASwGoCAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T08:57:37.261Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UIManager.xml", "DocumentMoniker": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\Config\\UIManager.xml", "RelativeDocumentMoniker": "..\\Product\\Debug\\Config\\UIManager.xml", "ToolTip": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\Config\\UIManager.xml", "RelativeToolTip": "..\\Product\\Debug\\Config\\UIManager.xml", "ViewState": "AgIAACQAAAAAAAAAAAAAAC8AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-07-04T08:16:08.941Z", "EditorCaption": ""}]}]}]}