﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Timers;
using Fpi.Util.Extensions;
using Fpi.Util.Interfaces.Initialize;
using Fpi.Xml;
using Microsoft.Owin.Hosting;
using Timer = System.Timers.Timer;

namespace Fpi.WMS3000.Api
{
    /// <summary>
    /// API模块管理类
    /// 看门狗，代理程序保活
    /// 启动、停止API服务
    /// </summary>
    public class ApiManager : BaseNode, IInitialization
    {
        #region 字段属性

        /// <summary>
        /// 自动运行
        /// </summary>
        [Description("自动运行")]
        public bool AutoStart;

        /// <summary>
        /// 监听端口
        /// </summary>
        [Description("监听端口")]
        public int ApiPort = 8080;

        /// <summary>
        /// 代理服务路径
        /// </summary>
        [Description("代理服务路径")]
        public string ProxyServerPath;

        /// <summary>
        /// 服务器IP
        /// </summary>
        [Description("服务器IP")]
        public string ServerIP;

        /// <summary>
        /// 服务器端口
        /// </summary>
        [Description("服务器端口")]
        public int ServerPort;

        /// <summary>
        /// 站点MN号
        /// </summary>
        [Description("站点MN号")]
        public string StationMN;

        /// <summary>
        /// 站点密码
        /// </summary>
        [Description("站点密码")]
        public string StationPassword;

        /// <summary>
        /// 自动保活
        /// </summary>
        [Description("自动保活")]
        public bool AutoKeepAlive;

        /// <summary>
        /// 服务实例
        /// </summary>
        [Description("服务实例")]
        private IDisposable _server;

        /// <summary>
        /// 代理程序进程
        /// </summary>
        private Process _consoleProcess;

        /// <summary>
        /// 保活检查定时器
        /// </summary>
        private Timer _checkTimer;

        #endregion

        #region 单例

        private ApiManager()
        {
            loadXml();
        }

        private static readonly object SyncObj = new object();
        private static ApiManager _instance;

        public static ApiManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new ApiManager();
                }
            }
            return _instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 启动服务
        /// </summary>
        public void StartWebApi()
        {
            _server = WebApp.Start<OwinStartup>($"http://127.0.0.1:{ApiPort}/");
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public void StopWebApi()
        {
            if(_server != null)
            {
                _server.Dispose();
                _server = null;
            }
        }

        /// <summary>
        /// 启动代理服务程序
        /// </summary>
        public void StarProxyServer()
        {
            var arguments = $"-ip {ServerIP} -port {ServerPort} -mn {StationMN} -pw {StationPassword}";

            // 获取代理服务绝对路径
            var path = FileExtension.GetAbsolutePath(ProxyServerPath);

            // 根据程序名终止之前由外部程序启动的代理进程
            TerminateProcessByName(path);

            // 创建一个新的 Process 对象
            _consoleProcess = new Process();
            _consoleProcess.StartInfo.FileName = path;
            _consoleProcess.StartInfo.UseShellExecute = false;
            _consoleProcess.StartInfo.RedirectStandardOutput = true;
            _consoleProcess.StartInfo.RedirectStandardError = true;
            _consoleProcess.StartInfo.CreateNoWindow = true;
            _consoleProcess.StartInfo.Arguments = arguments;
            // 订阅 OutputDataReceived 事件
            _consoleProcess.OutputDataReceived += (sender, e) =>
            {
                if(!string.IsNullOrEmpty(e.Data))
                {
                    ApiLogHelper.WriteLog($"代理程序日志:{e.Data}");
                }
            };

            // 启动进程
            _consoleProcess.Start();

            // 开始异步读取输出
            _consoleProcess.BeginOutputReadLine();
        }

        /// <summary>
        /// 停止代理服务程序
        /// </summary>
        public void StopProxyServer()
        {
            try
            {
                if(_checkTimer != null)
                {
                    _checkTimer.Close();
                    _checkTimer = null;
                }

                if(_consoleProcess != null && !_consoleProcess.HasExited)
                {
                    _consoleProcess.Kill();
                    _consoleProcess = null;
                }
            }
            catch
            {
            }
        }

        #endregion

        #region IInitialization 成员

        public void Initialize()
        {
            if(AutoStart)
            {
                // 启动服务
                try
                {
                    StartWebApi();

                    ApiLogHelper.WriteLog($"启动API服务");
                }
                catch(Exception ex)
                {
                    ApiLogHelper.WriteLog($"启动API服务出错：{ex.Message}");
                }

                // 启动代理服务
                if(AutoKeepAlive)
                {
                    try
                    {
                        // 启动代理服务
                        StarProxyServer();

                        ApiLogHelper.WriteLog($"启动代理服务");

                        // 设置定时器每分钟检查一次进程是否还在运行
                        _checkTimer = new Timer(60000);
                        _checkTimer.Elapsed += CheckTimer_Elapsed;
                        _checkTimer.AutoReset = true;
                        _checkTimer.Enabled = true;

                        ApiLogHelper.WriteLog("代理程序监控已启动...");
                    }
                    catch(Exception ex)
                    {
                        ApiLogHelper.WriteLog($"启动代理程序出错：{ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 定时检测代理程序是否运行中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void CheckTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            if(_consoleProcess == null || _consoleProcess.HasExited)
            {
                try
                {
                    StarProxyServer();

                    ApiLogHelper.WriteLog("代理程序已退出,尝试重新启动成功！");
                }
                catch(Exception ex)
                {
                    ApiLogHelper.WriteLog("代理程序已退出并尝试重新启动失败: " + ex.Message);
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 根据程序名停掉程序
        /// </summary>
        /// <param name="processName"></param>
        private void TerminateProcessByName(string processName)
        {
            try
            {
                // 提取程序名
                processName = Path.GetFileNameWithoutExtension(processName);

                // 根据程序名获取进程列表
                Process[] processes = Process.GetProcessesByName(processName);

                if(processes.Length > 0)
                {
                    foreach(Process process in processes)
                    {
                        process.Kill();
                        process.WaitForExit(); // 等待进程完全退出
                    }
                }
            }
            catch
            {
            }
        }

        #endregion
    }
}