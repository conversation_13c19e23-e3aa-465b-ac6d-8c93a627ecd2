﻿using System;
using System.Collections.Generic;
using Fpi.UI.PC.DockForms;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.UI;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;

namespace Fpi.WMS3000.UI
{
    /// <summary>
    /// 动环设备参数查看界面
    /// </summary>
    public partial class FrmExterDeviceParam : DockView
    {
        #region 字段属性

        /// <summary>
        /// 待刷新界面集合
        /// </summary>
        private List<IRefreshUI> _ucList = new List<IRefreshUI>();

        #endregion

        #region 构造

        public FrmExterDeviceParam()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmExterDeviceParam_Load(object sender, EventArgs e)
        {
            this.InitDeviceInfo();

            // 记录系统操作日志
            SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"查询动环设备参数", eOpType.浏览操作, eOpStyle.本地操作));
        }

        private void btnRefreshUI_Click(object sender, EventArgs e)
        {
            foreach(var uc in _ucList)
            {
                uc.RefreshUI();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化仪器参数显示
        /// </summary>
        private void InitDeviceInfo()
        {
            _ucList.Clear();
            pnlPDUParams.Controls.Clear();

            // 总供电模块
            uc_DS6CNZDESDeviceState.SetTragetDevice(ExterEquipConfigManager.GetInstance().DeviceSelect.MainElectricityMonitorDevice as DS6CNZDESEquipment);
            _ucList.Add(uc_DS6CNZDESDeviceState);

            // 水泵供电模块
            uc_PumpZTNB2LE.SetTragetDevice(ExterEquipConfigManager.GetInstance().DeviceSelect.PumpElectricityMonitorDevice as ZTNB2LEEquipment);
            _ucList.Add(uc_PumpZTNB2LE);

            // 支路供电模块
            if(ExterEquipConfigManager.GetInstance().DeviceSelect.PDUDevice is TPRSXC3007Equipment tprsEquip)
            {
                foreach(var roadParam in tprsEquip.DeviceParams.AccessRoadParamList)
                {
                    var uc = new UC_OneRoadPduParam(roadParam);
                    _ucList.Add(uc);
                    pnlPDUParams.Controls.Add(uc);
                }
            }

            // 称重模块
            if(ExterEquipConfigManager.GetInstance().DeviceSelect.WeighingDevice is LJWD200Equipment ljEquip)
            {
                foreach(var roadParam in ljEquip.DeviceParams.AccessRoadParamList)
                {
                    var uc = new UC_OneRoadWeightParam(roadParam);
                    _ucList.Add(uc);
                    pnlWeightParams.Controls.Add(uc);
                }
            }

            // 空调控制器模块
            uc_JDRKRSDeviceState.SetTragetDevice(ExterEquipConfigManager.GetInstance().DeviceSelect.AirControlDevice as JDRKRSEquip);
            _ucList.Add(uc_JDRKRSDeviceState);

            // 采水模块
            uc_CollectionModuleState.RefreshUI();
            _ucList.Add(uc_CollectionModuleState);

            // 预处理模块
            uc_PretreatmentModuleState.RefreshUI();
            _ucList.Add(uc_PretreatmentModuleState);

            // 配水模块
            uc_DistributeModuleState.RefreshUI();
            _ucList.Add(uc_DistributeModuleState);

            // 电子围栏模块
            uc_VssModuleState.RefreshUI();
            _ucList.Add(uc_VssModuleState);

            // UPS模块
            uc_YstEA900DeviceState.SetTragetDevice(ExterEquipConfigManager.GetInstance().DeviceSelect.UPSDevice as YstEA900UpsEquipment);
            _ucList.Add(uc_YstEA900DeviceState);

            // 防雷模块
            uc_LXAM125DeviceState.SetTragetDevice(ExterEquipConfigManager.GetInstance().DeviceSelect.FLDevice as LXAM125Equipment);
            _ucList.Add(uc_LXAM125DeviceState);

            // 试剂冰箱温度模块
            uc_DL10BDeviceState.SetTragetDevice(ExterEquipConfigManager.GetInstance().DeviceSelect.TempDevice as DL10BEquipment);
            _ucList.Add(uc_DL10BDeviceState);

            // 站房环境模块
            uc_StationModuleState.RefreshUI();
            _ucList.Add(uc_StationModuleState);

            // 门禁模块
            uc_DoorBanModuleState.RefreshUI();
            _ucList.Add(uc_DoorBanModuleState);

            // 器件运行情况统计模块
            uc_AllDeviceUsageInfos.InitUI();
            _ucList.Add(uc_AllDeviceUsageInfos);

            // 设备运行情况统计模块
            uc_AllEquipUsageInfos.InitUI();
            _ucList.Add(uc_AllEquipUsageInfos);

            // 摄像机运行情况统计模块
            uc_AllCameraUsageInfos.InitUI();
            _ucList.Add(uc_AllCameraUsageInfos);

            // 图像巡检信息
            uc_ImagePatrolResult.SetRefreshBtnVisible(true);
            uc_ImagePatrolResult.RefreshUI();
            _ucList.Add(uc_ImagePatrolResult);
        }

        #endregion
    }
}