﻿using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Fpi.WMS3000.Inspection
{
    /// <summary>
    /// ReportUserControl.xaml 的交互逻辑
    /// </summary>
    public partial class ReportUserControl : UserControl
    {
        public ReportUserControl()
        {
            InitializeComponent();
        }

        #region UI渲染

        /// <summary>
        /// 绘制控件
        /// </summary>
        /// <param name="columns">列数</param>
        /// <param name="checkUnits">数据源</param>
        public void Render(List<Column> columns, List<CheckUnit> checkUnits)
        {
            // 1、初始化表格尺寸
            var rowCount = checkUnits.Select(p => p.CheckItemsCount).Aggregate((p1, p2) => p1 + p2) + 1;
            var columnCount = columns.Count;

            for(var i = 0; i < rowCount; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition() { Height = GridLength.Auto });
            }
            for(var j = 0; j < columnCount; j++)
            {
                grid.ColumnDefinitions.Add(new ColumnDefinition() { Width = columns[j].Width });
            }

            // 2、添加列头
            for(var j = 0; j < columnCount; j++)
            {
                var columnHeaderCell = ResolveMergedCell(columns[j].Name, 0, j, 1, 1);
                columnHeaderCell.Background = new SolidColorBrush(Color.FromRgb(247, 250, 255));
                grid.Children.Add(columnHeaderCell);
            }

            // 3、添加内容数据
            var unitBeginIndex = 1;
            for(var a = 0; a < checkUnits.Count; a++)
            {
                var checkUnit = checkUnits[a];
                var currentUnitRowCount = checkUnit.CheckItemsCount;

                // 序号
                var unitSeqNo = ResolveMergedCell(checkUnit.SeqNo, unitBeginIndex, 0, checkUnit.CheckItemsCount, 1);
                grid.Children.Add(unitSeqNo);

                // 巡查单元
                var unitNameCell = ResolveMergedCell(checkUnit.Name, unitBeginIndex, 1, checkUnit.CheckItemsCount, 1);
                grid.Children.Add(unitNameCell);

                // 所有子项
                var subUnitBeginIndex = unitBeginIndex;
                for(var b = 0; b < checkUnit.CheckSubUnits.Count; b++)
                {
                    var checkSubUnit = checkUnit.CheckSubUnits[b];

                    // 名称
                    var checkSubUnitName = ResolveMergedCell(checkSubUnit.Name, subUnitBeginIndex, 2, checkSubUnit.CheckItemsCount, 1);
                    grid.Children.Add(checkSubUnitName);

                    // 状态
                    var checkSubUnitState = ResolveMergedCell(checkSubUnit.State, subUnitBeginIndex, 3, checkSubUnit.CheckItemsCount, 1);
                    grid.Children.Add(checkSubUnitState);

                    // 巡查项
                    var checkItemBeginIndex = subUnitBeginIndex;
                    for(var c = 0; c < checkSubUnit.CheckItems.Count; c++)
                    {
                        var checkItem = checkSubUnit.CheckItems[c];

                        // 名称
                        var checkItemName = ResolveMergedCell(checkItem.Name, checkItemBeginIndex, 4, 1, 1);
                        grid.Children.Add(checkItemName);

                        // 结果
                        var resultName = ResolveMergedCell(checkItem.Result, checkItemBeginIndex, 5, 1, 1);
                        //resultName.fore = new SolidColorBrush(Colors.Red);
                        TextBlock.SetForeground(resultName, new SolidColorBrush(Colors.Black));
                        grid.Children.Add(resultName);

                        checkItemBeginIndex++;
                    }

                    subUnitBeginIndex += checkSubUnit.CheckItemsCount;
                }

                unitBeginIndex += currentUnitRowCount;
            }
        }

        private Border ResolveMergedCell(string content, int rowIndex, int colIndex, int rowSpan, int colSpan)
        {
            var textBlock = new TextBlock() { Text = content, Padding = new Thickness(8), HorizontalAlignment = HorizontalAlignment.Center, VerticalAlignment = VerticalAlignment.Center, TextWrapping = TextWrapping.Wrap };
            var border = new Border() { BorderThickness = GetBorderThickness(rowIndex, colIndex), BorderBrush = new SolidColorBrush(Color.FromRgb(134, 144, 156)) };
            border.Background = new SolidColorBrush(Colors.White);

            border.Child = textBlock;

            Grid.SetRow(border, rowIndex);
            Grid.SetRowSpan(border, rowSpan);

            Grid.SetColumn(border, colIndex);
            Grid.SetColumnSpan(border, colSpan);

            return border;
        }

        private Thickness GetBorderThickness(int i, int j)
        {
            var thickness = new Thickness(0, 0, 1, 1);

            if(i == 0)
                thickness.Top = 1;
            if(j == 0)
                thickness.Left = 1;

            return thickness;
        }
        #endregion

        public void ExportToPng(string path)
        {
            GridToImageExporter.ExportToPng(grid, path);
        }
    }

    public class Column
    {
        public string Name { get; set; }
        public GridLength Width { get; set; }
    }
}
