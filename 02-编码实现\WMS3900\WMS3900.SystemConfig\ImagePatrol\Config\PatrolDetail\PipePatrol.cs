﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    ///配水预处理管路脏污检测执行类
    /// </summary>
    public class PipePatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public PipePatrol()
        {
            UnitId = "Pipe";
            UnitName = "配水预处理管路脏污检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(ref ImageUnitPatrolResultBase patrolResult)
        {
            patrolResult = new PipePatrolResult();

            if(ExterEquipConfigManager.GetInstance().CameraSelect.PipeCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            // 拍照
            ExterEquipConfigManager.GetInstance().CameraSelect.PipeCamera.ScreenShot(out string picPath);

            // 算法分析
            var state = AlgorithmHelper.CheckPipeSmutState(new Mat(picPath)) ? eSmutState.脏污 : eSmutState.正常;

            patrolResult.ImagePath = FileExtension.GetRelativePath(picPath);
            ((PipePatrolResult)patrolResult).PipeSmutState = state;
        }

        #endregion
    }
}