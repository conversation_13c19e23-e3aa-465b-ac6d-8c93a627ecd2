﻿using System;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.Util;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 核查数据
    /// </summary>
    public partial class UC_WCS3900CheckData : UIUserControl
    {
        #region 字段属性

        private WCS3900CheckDataBase _checkData;

        #endregion

        #region 构造

        public UC_WCS3900CheckData()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if(dgvDetailData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Application.StartupPath + "\\query\\";
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName = $"{filePath}\\溯源过程数据{_checkData.CheckTime:yyyy-MM-dd HH-mm-ss}.xlsx";

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    FileExportHelper.SaveDataGridViewToExcelFile(dgvDetailData, saveFileDialog.FileName);
                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"溯源数据导出出错：{ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置界面关联的属性
        /// </summary>
        /// <param name="checkData"></param>
        public void SetTragetParams(WCS3900CheckDataBase checkData)
        {
            _checkData = checkData;
            InitUI();
            RefreshUI();
        }

        internal void RefreshUI()
        {
            // 测量参数区
            CommonFunctionHelper.ReflectAttributeToUI(pnlCheckData, _checkData);

            dgvDetailData.ClearRows();
            int rowIndex = 1;

            // 过程溯源数据
            if(_checkData is WCS3900PHCheckData phCheckData)
            {
                foreach(WCS3900PHCheckDetailData detailData in phCheckData.CheckDetailData)
                {
                    int index = dgvDetailData.Rows.Add();
                    DataGridViewRow dr = dgvDetailData.Rows[index];
                    dr.Tag = detailData;
                    dr.Cells[0].Value = rowIndex++;
                    dr.Cells[1].Value = detailData.DataTime.ToString(DbConfig.DATETIME_FORMAT);
                    dr.Cells[2].Value = detailData.DataValue;
                    dr.Cells[3].Value = detailData.DataVoltage;
                }
            }
            else if(_checkData is WCS3900TurbCheckData turbCheckData)
            {
                foreach(WCS3900TurbCheckDetailData detailData in turbCheckData.CheckDetailData)
                {
                    int index = dgvDetailData.Rows.Add();
                    DataGridViewRow dr = dgvDetailData.Rows[index];
                    dr.Tag = detailData;
                    dr.Cells[0].Value = rowIndex++;
                    dr.Cells[1].Value = detailData.DataTime.ToString(DbConfig.DATETIME_FORMAT);
                    dr.Cells[2].Value = detailData.DataValue;
                    dr.Cells[3].Value = detailData.DataVoltage;
                }
            }
            else if(_checkData is WCS3900OxyCheckData oxyCheckData)
            {
                foreach(WCS3900OxyCheckDetailData detailData in oxyCheckData.CheckDetailData)
                {
                    int index = dgvDetailData.Rows.Add();
                    DataGridViewRow dr = dgvDetailData.Rows[index];
                    dr.Tag = detailData;
                    dr.Cells[0].Value = rowIndex++;
                    dr.Cells[1].Value = detailData.DataTime.ToString(DbConfig.DATETIME_FORMAT);
                    dr.Cells[2].Value = detailData.DataValue;
                    dr.Cells[3].Value = detailData.DataTemp.ToString("F2");
                }
            }
            else if(_checkData is WCS3900ConduCheckData conduCheckData)
            {
                foreach(WCS3900ConduCheckDetailData detailData in conduCheckData.CheckDetailData)
                {
                    int index = dgvDetailData.Rows.Add();
                    DataGridViewRow dr = dgvDetailData.Rows[index];
                    dr.Tag = detailData;
                    dr.Cells[0].Value = rowIndex++;
                    dr.Cells[1].Value = detailData.DataTime.ToString(DbConfig.DATETIME_FORMAT);
                    dr.Cells[2].Value = detailData.DataValue;
                }
            }
            else if(_checkData is WCS3900TempCheckData tempCheckData)
            {
                foreach(WCS3900TempCheckDetailData detailData in tempCheckData.CheckDetailData)
                {
                    int index = dgvDetailData.Rows.Add();
                    DataGridViewRow dr = dgvDetailData.Rows[index];
                    dr.Tag = detailData;
                    dr.Cells[0].Value = rowIndex++;
                    dr.Cells[1].Value = detailData.DataTime.ToString(DbConfig.DATETIME_FORMAT);
                    dr.Cells[2].Value = detailData.DataValue;
                }
            }
        }

        #endregion

        #region 私有方法

        private void InitUI()
        {
            if(_checkData != null)
            {
                dgvDetailData.ClearAll();
                dgvDetailData.AddColumn("序号", "num");
                dgvDetailData.AddColumn("数据时间", "DataTime");
                dgvDetailData.AddColumn("测量值", "DataValue");

                if(_checkData is WCS3900PHCheckData phCheckData || _checkData is WCS3900TurbCheckData turbCheckData)
                {
                    dgvDetailData.AddColumn("电压值", "DataVoltage");
                }
                else if(_checkData is WCS3900OxyCheckData oxyCheckData)
                {
                    dgvDetailData.AddColumn("温度值", "DataTemp");
                }
            }
        }

        #endregion

    }
}