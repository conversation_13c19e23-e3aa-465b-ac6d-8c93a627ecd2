﻿using System;
using System.Text;
using System.Windows.Forms;
using Fpi.Communication.Manager;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Fpi.Util.Reflection;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Config;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig.UI
{
    /// <summary>
    /// 智能巡检参数配置
    /// </summary>
    public partial class FrmSmartPatrolConfig : UIForm
    {
        #region 构造

        public FrmSmartPatrolConfig()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmSmartPatrolConfig_Load(object sender, EventArgs e)
        {
            #region 上传通道

            // 填充全部数据传输通道
            this.cmbMulRemoteSelect.Nodes.Clear();
            foreach(Pipe pipe in PortManager.GetInstance().pipes)
            {
                if(pipe.valid && pipe.id.ToLower().StartsWith("remote") && pipe.Protocol.Sender is IDataUpload dataUpload && dataUpload.UploadDataType == typeof(eFpiHttpUploadDataType))
                {
                    this.cmbMulRemoteSelect.Nodes.Add(pipe.name);
                }
            }

            StringBuilder sb = new StringBuilder();
            try
            {
                if(!string.IsNullOrEmpty(SmartPatrolManager.GetInstance().UploadPipesId))
                {
                    foreach(TreeNode selectedNode in this.cmbMulRemoteSelect.Nodes)
                    {
                        selectedNode.Checked = SmartPatrolManager.GetInstance().UploadPipesId.Contains(selectedNode.Text);
                        if(selectedNode.Checked)
                        {
                            sb.Append($"{selectedNode.Text};");
                        }
                    }
                }
            }
            catch
            {
            }
            cmbMulRemoteSelect.Text = sb.ToString();

            #endregion

            #region 巡检项启用

            // 先按顺序加载已启用项
            foreach(var unitType in SmartPatrolManager.GetInstance().UsedPatrolUnitTypeList)
            {
                try
                {
                    SingleUnitSmartPatrolBase unit = (SingleUnitSmartPatrolBase)ReflectionHelper.CreateInstance(unitType);
                    var text = unit.UnitName;
                    if(!string.IsNullOrWhiteSpace(unit.Description))
                    {
                        text += $"（{unit.Description}）";
                    }
                    var node = tvUnitList.Nodes.Add(text);
                    node.Tag = unit;
                    node.Checked = true;
                }
                catch
                {
                }
            }

            // 再加载未启用项
            // 反射加载所有巡检项
            Type[] types = ReflectionHelper.GetChildTypes(typeof(SingleUnitSmartPatrolBase));
            foreach(Type type in types)
            {
                // 跳过已加载项
                if(!SmartPatrolManager.GetInstance().UsedPatrolUnitTypeList.Contains(type.FullName))
                {
                    try
                    {
                        SingleUnitSmartPatrolBase unit = (SingleUnitSmartPatrolBase)ReflectionHelper.CreateInstance(type);
                        var text = unit.UnitName;
                        if(!string.IsNullOrWhiteSpace(unit.Description))
                        {
                            text += $"（{unit.Description}）";
                        }
                        var node = tvUnitList.Nodes.Add(text);
                        node.Tag = unit;
                    }
                    catch
                    {
                    }
                }
            }

            #endregion

            // 网络测试地址
            txtNetTestAddr.Text = SmartPatrolManager.GetInstance().NetTestAddr;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            // 巡检项启用情况
            SmartPatrolManager.GetInstance().UsedPatrolUnitTypeList.Clear();
            foreach(TreeNode node in tvUnitList.Nodes)
            {
                if(node.Checked && node.Tag is SingleUnitSmartPatrolBase smartPatrolUnit)
                {
                    string typeName = smartPatrolUnit.GetType().FullName;
                    if(!SmartPatrolManager.GetInstance().UsedPatrolUnitTypeList.Contains(typeName))
                    {
                        SmartPatrolManager.GetInstance().UsedPatrolUnitTypeList.Add(typeName);
                    }
                }
            }

            // 数据上传通道
            SmartPatrolManager.GetInstance().UploadPipesId = cmbMulRemoteSelect.Text;

            // 网络测试地址
            SmartPatrolManager.GetInstance().NetTestAddr = txtNetTestAddr.Text;

            SmartPatrolManager.GetInstance().Save();
            SmartPatrolManager.GetInstance().RebuildPatrolUnitList();
        }

        #endregion
    }
}