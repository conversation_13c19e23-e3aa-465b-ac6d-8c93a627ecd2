﻿using System.ComponentModel;
using Newtonsoft.Json;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 视频算法单元数据
    /// </summary>
    public class VideoAlgorithmModuleState
    {
        #region 无需存储属性

        #region 采水点

        /// <summary>
        /// 采水点摄像机图像地址
        /// </summary>
        [Description("采水点摄像机图像地址")]
        public string WaterPointCameraImagePath { get; set; }

        /// <summary>
        /// 采水装置偏移状态
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("采水装置偏移状态")]
        public eWaterTrapMigration WaterTrapMigration { get; set; }

        /// <summary>
        /// 采水点水面漂浮物
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("采水点水面漂浮物")]
        public eModuleWorkingState WaterTrapFloat { get; set; }

        /// <summary>
        /// 采水点人员入侵
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("采水点人员入侵")]
        public eModuleWorkingState WaterTrapIntrusion { get; set; }

        /// <summary>
        /// 采水点水体颜色
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("采水点水体颜色")]
        public eWaterColor WaterTrapWaterColor { get; set; }

        /// <summary>
        /// 采水点断面实际水位
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("采水点断面实际水位")]
        public double WaterTrapWaterLevel { get; set; }

        #endregion

        #region 站房外

        /// <summary>
        /// 监测站外围摄像机图像地址
        /// </summary>
        [Description("监测站外围摄像机图像地址")]
        public string StationOutsideCameraImagePath { get; set; }

        /// <summary>
        /// 监测站外围人员入侵
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("监测站外围人员入侵")]
        public eModuleWorkingState StationOutsideIntrusion { get; set; }

        #endregion

        #region 站房内

        /// <summary>
        /// 室内摄像机图像地址
        /// </summary>
        [Description("室内摄像机图像地址")]
        public string StationInsideCameraImagePath { get; set; }

        /// <summary>
        /// 监测站内人员入侵
        /// </summary>
        [JsonIgnore]
        [Description("监测站内人员入侵")]
        public eModuleWorkingState StationInsideIntrusion { get; set; }

        /// <summary>
        /// 未穿工服
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("未穿工服")]
        public eModuleWorkingState UndressedSuit { get; set; }

        /// <summary>
        /// 人员吸烟
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("人员吸烟")]
        public eModuleWorkingState Smoking { get; set; }

        /// <summary>
        /// 物料乱堆放及物品遗留
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("物料乱堆放及物品遗留")]
        public eModuleWorkingState MaterialStacking { get; set; }

        #endregion

        // TODO:
        // 监测站内人员位置定位识别

        #endregion
    }
}