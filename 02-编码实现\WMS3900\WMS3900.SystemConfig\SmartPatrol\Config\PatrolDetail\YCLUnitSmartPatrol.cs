﻿using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 预处理单元巡检执行类
    /// </summary>
    public class YCLUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public YCLUnitSmartPatrol()
        {
            UnitId = "Pretreatment";
            UnitName = "预处理单元";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            YCLUnitPatrolResult result = new YCLUnitPatrolResult();

            // 沉砂池液位状态
            result.SandSinkLevelState = CheckCscLevelState();

            // 沉砂池脏污状态
            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<SandSinkPatrolResult>();

                if(imagePatrolResult != null)
                {
                    result.SandSinkSmutState = imagePatrolResult.SandSinkSmutState;
                }
            }

            // 配水管脏污状态
            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<PipePatrolResult>();

                if(imagePatrolResult != null)
                {
                    result.PipeSmutState = imagePatrolResult.PipeSmutState;
                }
            }

            return result;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查沉砂池液位状态
        /// </summary>
        /// <returns></returns>
        private eModuleWorkingState CheckCscLevelState()
        {
            // 改用上次测量采水是否正常作为沉砂池液位判断依据
            return GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState == eWaterCollectionState.无水报警 ? eModuleWorkingState.异常 : eModuleWorkingState.正常;

            //if(ExterEquipConfigManager.GetInstance().WpusConfigInfo?.SandSinkLiquidLevelNode != null)
            //{
            //    return ExterEquipConfigManager.GetInstance().WpusConfigInfo.SandSinkLiquidLevelNode.GetValue() ? eModuleWorkingState.正常 : eModuleWorkingState.异常;
            //}
            //return eModuleWorkingState.正常;
        }

        #endregion
    }
}