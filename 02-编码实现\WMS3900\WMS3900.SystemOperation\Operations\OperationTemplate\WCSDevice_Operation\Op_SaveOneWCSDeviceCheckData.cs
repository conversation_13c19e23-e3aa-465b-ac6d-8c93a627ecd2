﻿using System;
using Fpi.Devices;
using Fpi.Operations;
using Fpi.Operations.Interfaces;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.SystemConfig;
using Fpi.WMS3000.SystemOperation.Interfaces;

namespace Fpi.WMS3000.SystemOperation.OperationTemplate
{
    /// <summary>
    /// 保存五参数设备核查或水样比对数据
    /// </summary>
    public class Op_SaveOneWCSDeviceCheckData : CustomOperation, IOpTemplateParaConfigView
    {
        #region 字段属性

        /// <summary>
        /// 是否初始化
        /// </summary>
        private bool _inited;

        private Device _dev;

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return "保存五参数设备核查或水样比对数据";
        }

        public override object CustomDo(string instrumentId, bool manual, object inputData, IOperationListener operationListener)
        {
            try
            {
                if(!_inited)
                {
                    InitProperty();
                }

                // 数据存储时间
                DateTime saveTime = SystemHelper.FiveParamCheckTime;
                // 未调用获取五参数核查时间直接调用保存
                if(saveTime == DateTime.MinValue)
                {
                    saveTime = DateTime.Now;
                }

                // 获取本次启动了哪些因子核查/水样比对
                var operNedParams = (_dev as IWCSDeviceOperation).LastOperNedParams;

                // 遍历各因子
                foreach(Enum value in Enum.GetValues(typeof(eWCSNodeType)))
                {
                    eWCSNodeType _nodeType = (eWCSNodeType)value;
                    // 历史记录中，未启用当前因子，不存储
                    if(operNedParams != null && operNedParams.ContainsKey(_nodeType) && !operNedParams[_nodeType].IsEnable)
                    {
                        SystemLogHelper.WriteOperationLog($"[{GetOperationName()}]:保存[{_dev.name}][{EnumOperate.GetEnumDesc(_nodeType)}]因子核查数据操作跳过，当前平台反控操作未启用本因子!");
                    }
                    // 历史记录不存在，或者历史记录中显示已启用当前因子
                    else
                    {
                        ((IWCSDeviceOperation)_dev).SaveCheckDataToDB(_nodeType, saveTime);
                        SystemLogHelper.WriteOperationLog($"[{GetOperationName()}]:保存[{_dev.name}][{EnumOperate.GetEnumDesc(_nodeType)}]因子核查数据操作成功!");
                    }
                }
            }
            catch(Exception ex)
            {
                SystemLogHelper.WriteSystemErrorLog("[" + GetOperationName() + "]操作异常: " + ex.Message);
                SystemLogHelper.WriteOperationLog("[" + GetOperationName() + "]操作异常: " + ex.Message);
            }
            return null;
        }

        #endregion

        #region IOpTemplateParaConfigView 成员

        public IOpConfig TemplateParaConfigView => new UC_WCSDeviceSelect();

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化参数
        /// </summary>
        private void InitProperty()
        {
            string tmp = operation.GetPropertyValue(GlobalNameDefine.PropertyName_DeviceId);
            if(string.IsNullOrEmpty(tmp))
            {
                throw new Exception("被控仪表未配置！");
            }
            _dev = DeviceManager.GetInstance().GetDeviceById(tmp);
            if(_dev == null)
            {
                throw new Exception(string.Format("被控仪表配置不合法：当前配置[{0}],无对应编号设备！", tmp));
            }
            if(!_dev.IsUsed)
            {
                throw new Exception(string.Format("当前被控仪表[{0}]未启用！", _dev.name));
            }
            // 设备不具备此功能
            if(!(_dev is IWCSDeviceOperation))
            {
                throw new Exception(string.Format("当前被控仪表[{0}]无此功能！", _dev.name));
            }

            _inited = true;
        }

        #endregion
    }
}