﻿using System;
using System.Text;
using System.Windows.Forms;
using Fpi.Communication.Manager;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig.UI
{
    public partial class FrmPatrolResultUploadConfig : UIForm
    {
        #region 字段属性

        /// <summary>
        /// 数据传输通道编号
        /// </summary>
        public string UploadPipesId;

        #endregion

        #region 构造


        public FrmPatrolResultUploadConfig(string uploadPipesId)
        {
            this.UploadPipesId = uploadPipesId;
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmPatrolResultUploadConfig_Load(object sender, EventArgs e)
        {
            // 填充全部数据传输通道
            this.cmbMulRemoteSelect.Nodes.Clear();
            foreach(Pipe pipe in PortManager.GetInstance().pipes)
            {
                if(pipe.valid && pipe.id.ToLower().StartsWith("remote") && pipe.Protocol.Sender is IDataUpload dataUpload && dataUpload.UploadDataType == typeof(eFpiHttpUploadDataType))
                {
                    this.cmbMulRemoteSelect.Nodes.Add(pipe.name);
                }
            }

            StringBuilder sb = new StringBuilder();

            // 更新选中情况
            try
            {
                if(!string.IsNullOrEmpty(UploadPipesId))
                {
                    foreach(TreeNode selectedNode in this.cmbMulRemoteSelect.Nodes)
                    {
                        selectedNode.Checked = UploadPipesId.Contains(selectedNode.Text);
                        if(selectedNode.Checked)
                        {
                            sb.Append($"{selectedNode.Text};");
                        }
                    }
                }
            }
            catch(Exception) { }

            cmbMulRemoteSelect.Text = sb.ToString();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            UploadPipesId = cmbMulRemoteSelect.Text;
        }

        #endregion
    }
}