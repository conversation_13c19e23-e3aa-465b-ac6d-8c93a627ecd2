﻿using System.Windows.Forms;
using Fpi.UI.PC.DockForms;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    public partial class FrmQueryDataReport : BaseWindow
    {
        #region 字段

        private string _param;

        #endregion

        #region 构造

        public FrmQueryDataReport()
        {
            InitializeComponent();
        }

        public FrmQueryDataReport(string param) : this()
        {
            _param = param;
        }

        #endregion

        #region 事件

        private void FrmQueryMinutesData_Load(object sender, System.EventArgs e)
        {
            var uc = new UC_QueryDataReport(_param);
            this.Controls.Add(uc);
            uc.Dock = DockStyle.Fill;
        }

        #endregion
    }
}