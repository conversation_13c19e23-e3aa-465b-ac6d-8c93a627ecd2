﻿using System.Linq;
using Fpi.Devices;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 分析单元巡检执行类
    /// </summary>
    public class FXUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public FXUnitSmartPatrol()
        {
            UnitId = "WaterAnalysis";
            UnitName = "分析单元";
            Description = "水站版";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            FXUnitPatrolResult result = new FXUnitPatrolResult();
            foreach(var siaEquip in DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.WMS).OfType<SIA3900Equipment>())
            {
                result.SIAEquipList.Add(new SIAEquipResult(siaEquip));
            }

            WCS3900Equip wcs3900Equip = DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.WMS).OfType<WCS3900Equip>().FirstOrDefault();
            result.WCSEquipResult = new WCSEquipResult(wcs3900Equip);

            return result;
        }

        #endregion
    }
}