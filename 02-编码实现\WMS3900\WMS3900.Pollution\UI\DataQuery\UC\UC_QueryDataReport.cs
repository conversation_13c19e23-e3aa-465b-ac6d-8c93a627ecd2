using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Fpi.DB;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.Util;
using Fpi.Util.Extensions;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Pollution.DB;
using Sunny.UI;
using DbConfig = Fpi.WMS3000.Pollution.DB.DbConfig;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 数据报表
    /// 日、周、月、季、年报表查询
    /// </summary>
    public partial class UC_QueryDataReport : UIUserControl
    {
        #region 属性、字段

        /// <summary>
        /// 左上角显示表名
        /// </summary>
        public string TitleName { get; set; } = "数据统计报表";

        /// <summary>
        /// 对应统计因子
        /// </summary>
        private List<QueryNode> _queryNodes = new List<QueryNode>();

        /// <summary>
        /// 当前选择的时间周期类型
        /// </summary>
        private ePollutionReportPeriodType _currentPeriodType = ePollutionReportPeriodType.Day;

        /// <summary>
        /// 选择的时间点（某天、某月等）
        /// </summary>
        private DateTime _selectedTime;

        /// <summary>
        /// 实际查询起始时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _startTime;

        /// <summary>
        /// 实际查询结束时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _endTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 小时流量列名称常量
        /// </summary>
        private const string HOUR_FLOW_COLUMN_NAME = "HourFlow";

        /// <summary>
        /// 累计流量因子
        /// </summary>
        private QueryNode _totalFlowNode;

        /// <summary>
        /// 小时流量列引用
        /// </summary>
        private DataGridViewColumn _hourFlowColumn;

        #endregion

        #region 构造

        public UC_QueryDataReport()
        {
            InitializeComponent();

        }

        public UC_QueryDataReport(string groupId)
        {
            InitializeComponent();

            // 设置报表因子组
            QueryGroup queryGroup = ReportManager.GetInstance().GetQueryGroupByGroupIdOrFirst(groupId);
            if(queryGroup != null)
            {
                foreach(QueryNode queryNode in queryGroup.QueryNodes)
                {
                    this._queryNodes.Add(queryNode);
                }

                TitleName = queryGroup.name;
            }
        }

        #endregion

        #region 事件

        private void UC_QueryDataReport_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                // 初始化界面标题
                InitialTitle();
                // 初始化时间选择器
                InitialDateTimePicker();
                // 更新日期选择器的类型
                UpdateDateSelectorVisibility();
                // 更新当前统计周期标签
                UpdateCurrentPeriodLabel();
                // 设置数据表格头部
                SetDataGridViewHead();
            }
        }

        /// <summary>
        /// 时间周期选择变更事件
        /// </summary>
        private void rbPeriodType_CheckedChanged(object sender, EventArgs e)
        {
            var radioButton = sender as UIRadioButton;
            if(radioButton != null && radioButton.Checked)
            {
                // 根据单选按钮确定周期类型
                if(radioButton.Name == "rbDay")
                    _currentPeriodType = ePollutionReportPeriodType.Day;
                else if(radioButton.Name == "rbWeek")
                    _currentPeriodType = ePollutionReportPeriodType.Week;
                else if(radioButton.Name == "rbMonth")
                    _currentPeriodType = ePollutionReportPeriodType.Month;
                else if(radioButton.Name == "rbQuarter")
                    _currentPeriodType = ePollutionReportPeriodType.Quarter;
                else if(radioButton.Name == "rbYear")
                    _currentPeriodType = ePollutionReportPeriodType.Year;

                // 清空界面数据
                dgvData.ClearRows();
                // 重新计算查询时间范围
                CalculateQueryTimeRange();
                // 更新当前周期标签
                UpdateCurrentPeriodLabel();
                // 更新日期选择器的类型
                UpdateDateSelectorVisibility();
                // 更新小时流量列的可见性
                UpdateHourFlowColumnVisibility();
            }
        }

        /// <summary>
        /// 时间选择变更事件
        /// </summary>
        private void dtpSelectedTime_ValueChanged(object sender, DateTime value)
        {
            // 更新选择的时间点
            _selectedTime = value;
            // 根据选择的时间点和周期类型计算查询时间范围
            CalculateQueryTimeRange();
            // 更新当前周期标签
            UpdateCurrentPeriodLabel();
        }

        /// <summary>
        /// 点击查询按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void btnStartQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;

                // 清空界面数据
                dgvData.ClearRows();

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"查询{TitleName}", eOpType.浏览操作, eOpStyle.本地操作));

                // 根据当前周期类型计算查询时间范围
                CalculateQueryTimeRange();

                // 检查查询条件
                Check();

                // 刷新页面显示
                FlushView();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 报告导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if(dgvData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Path.Combine(Application.StartupPath, "query");
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName = Path.Combine(filePath, $"{TitleName}_{_currentPeriodType.GetDescription()}_{lblCurrentPeriod.Text}.xlsx");

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvData, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if(_startTime > _endTime)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            // 检查查询因子
            if(_queryNodes == null || _queryNodes.Count == 0)
            {
                throw new Exception("未配置查询因子！");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            try
            {
                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");
                // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                Thread.Sleep(50);

                // 清空原数据
                dgvData.Rows.Clear();

                // 根据报表类型生成数据
                if(_currentPeriodType == ePollutionReportPeriodType.Day)
                {
                    GenerateDayReport();
                }
                else
                {
                    GeneratePeriodReport();
                }

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        #region 初始化控件

        /// <summary>
        /// 设置表头
        /// </summary>
        private void InitialTitle()
        {
            this.Text = TitleName;
        }

        /// <summary>
        /// 初始化时间选择控件
        /// </summary>
        private void InitialDateTimePicker()
        {
            // 初始化选择时间点为今天
            _selectedTime = DateTime.Today;
            // 设置默认时间
            dtpSelectedTime.Value = _selectedTime;
            // 根据当前周期类型计算实际查询时间范围
            CalculateQueryTimeRange();
        }

        /// <summary>
        /// 更新日期选择器的类型
        /// </summary>
        private void UpdateDateSelectorVisibility()
        {
            // 根据当前周期类型设置时间选择控件的显示方式
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：显示日期选择器（年月日）
                    dtpSelectedTime.ShowType = UIDateType.YearMonthDay;
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：显示日期选择器（选择某一天，自动计算所在周）
                    dtpSelectedTime.ShowType = UIDateType.YearMonthDay;
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：显示年月选择器
                    dtpSelectedTime.ShowType = UIDateType.YearMonth;
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：显示年月选择器（选择某个月，自动计算所在季度）
                    dtpSelectedTime.ShowType = UIDateType.YearMonth;
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：显示年选择器
                    dtpSelectedTime.ShowType = UIDateType.Year;
                    break;
            }
        }

        /// <summary>
        /// 更新当前统计周期标签
        /// </summary>
        private void UpdateCurrentPeriodLabel()
        {
            // 根据具体的标签控件来显示周期信息
            lblCurrentPeriod.Text = GetPeriodDisplayText(_currentPeriodType, _selectedTime);
        }

        /// <summary>
        /// 根据选择的时间点和报表周期类型计算查询时间范围
        /// </summary>
        private void CalculateQueryTimeRange()
        {
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：查询选择日期当天的数据
                    _startTime = _selectedTime.Date;
                    _endTime = _startTime.AddDays(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：查询选择日期所在周的数据（周一作为一周开始）
                    var weekStart = GetMondayOfWeek(_selectedTime);
                    _startTime = weekStart.Date;
                    _endTime = weekStart.AddDays(7).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：查询选择日期所在月的数据
                    var monthStart = new DateTime(_selectedTime.Year, _selectedTime.Month, 1);
                    _startTime = monthStart;
                    _endTime = monthStart.AddMonths(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：查询选择日期所在季度的数据
                    var quarterStart = GetQuarterStart(_selectedTime);
                    _startTime = quarterStart;
                    _endTime = quarterStart.AddMonths(3).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：查询选择日期所在年的数据
                    var yearStart = new DateTime(_selectedTime.Year, 1, 1);
                    _startTime = yearStart;
                    _endTime = yearStart.AddYears(1).AddSeconds(-1);
                    break;
            }
        }

        /// <summary>
        /// 获取指定日期所在周的周一日期
        /// </summary>
        private DateTime GetMondayOfWeek(DateTime date)
        {
            // 计算距离周一的天数
            int daysFromMonday = ((int)date.DayOfWeek - 1 + 7) % 7;
            return date.AddDays(-daysFromMonday);
        }

        /// <summary>
        /// 获取季度开始时间
        /// </summary>
        private DateTime GetQuarterStart(DateTime date)
        {
            int quarter = (date.Month - 1) / 3 + 1;
            int startMonth = (quarter - 1) * 3 + 1;
            return new DateTime(date.Year, startMonth, 1);
        }

        /// <summary>
        /// 获取周期显示文本
        /// </summary>
        private string GetPeriodDisplayText(ePollutionReportPeriodType periodType, DateTime selectedTime)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Day:
                    return $"{selectedTime:yyyy年MM月dd日}";
                case ePollutionReportPeriodType.Week:
                    var week = new GregorianCalendar().GetWeekOfYear(selectedTime, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
                    return $"{selectedTime.Year}年第{week}周";
                case ePollutionReportPeriodType.Month:
                    return $"{selectedTime:yyyy年MM月}";
                case ePollutionReportPeriodType.Quarter:
                    int quarter = (selectedTime.Month - 1) / 3 + 1;
                    return $"{selectedTime.Year}年第{quarter}季度";
                case ePollutionReportPeriodType.Year:
                    return $"{selectedTime.Year}年";
                default:
                    return "";
            }
        }

        /// <summary>
        /// 设置DataGridView表头
        /// </summary>
        private void SetDataGridViewHead()
        {
            try
            {
                // 清除现有列
                dgvData.Columns.Clear();

                // 添加时间列
                dgvData.AddColumn(@"时间\参数", "Time", 250);

                // 添加常规统计因子列
                foreach(var queryNode in _queryNodes)
                {
                    // 累计流量最后显示
                    if(queryNode.id != ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId)
                    {
                        string factorName = queryNode.name;
                        string unit = queryNode.UnitId ?? "";

                        // 添加因子数值列，设置DataPropertyName为数据库字段名
                        var col = dgvData.AddColumn($"{factorName}({unit})", $"{DbConfig.PREFIX_F}{queryNode.id}", 200);
                    }
                    else
                    {
                        _totalFlowNode = queryNode;
                    }
                }

                // 添加小时流量列
                _hourFlowColumn = dgvData.AddColumn("小时流量(m³)", DbConfig.TOTAL_FLOW, 300);

                // 添加累计流量列
                if(_totalFlowNode != null)
                {
                    string factorName = _totalFlowNode.name;
                    string unit = _totalFlowNode.UnitId ?? "";

                    // 添加因子数值列，设置DataPropertyName为数据库字段名
                    var col = dgvData.AddColumn($"{factorName}({unit})", $"{DbConfig.PREFIX_F}{_totalFlowNode.id}", 300);
                }

                // 设置列宽和样式
                foreach(DataGridViewColumn column in dgvData.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.SortMode = DataGridViewColumnSortMode.NotSortable;
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"设置表头失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新小时流量列的可见性
        /// </summary>
        private void UpdateHourFlowColumnVisibility()
        {
            if(_hourFlowColumn != null)
            {
                _hourFlowColumn.Visible = (_currentPeriodType == ePollutionReportPeriodType.Day);
            }
        }

        #endregion

        #region 数据查询

        /// <summary>
        /// 生成日报表数据
        /// </summary>
        private void GenerateDayReport()
        {
            try
            {
                // 构建查询字段列表
                StringBuilder selectFields = new StringBuilder("datatime, ");
                foreach(var queryNode in _queryNodes)
                {
                    selectFields.Append($"{DbConfig.PREFIX_F}{queryNode.id}, ");
                }
                selectFields.Append(DbConfig.TOTAL_FLOW);

                // 查询小时数据
                string strSql = $"select {selectFields} from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={(int)ePollutionDataType.小时数据} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 清空现有数据
                dgvData.Rows.Clear();

                // 存储所有小时数据用于统计计算
                var allHourData = new List<List<DataRow>>();

                // 生成24小时时段的报表
                for(int hour = 0; hour < 24; hour++)
                {
                    DateTime hourTime = _startTime.Date.AddHours(hour);
                    string timeLabel = $"{hour:D2}～{(hour + 1):D2}时";

                    // 获取该小时的数据
                    var hourDataRows = dataTable.AsEnumerable()
                        .Where(row => DateTime.Parse(row["datatime"].ToString()).Hour == hour)
                        .ToList();

                    // 创建显示行
                    int rowIndex = dgvData.Rows.Add();
                    DataGridViewRow displayRow = dgvData.Rows[rowIndex];

                    // 设置时间列
                    SetCellValueByDataPropertyName(displayRow, "Time", timeLabel);

                    // 如果有数据，取第一条记录作为代表（小时数据通常每小时只有一条）
                    if(hourDataRows.Any())
                    {
                        var hourDataRow = hourDataRows.First();
                        allHourData.Add(hourDataRows);

                        // 设置因子数值列
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            string disPlayValueStr = "--";
                            if(hourDataRow.Table.Columns.Contains(fieldName) &&
                               hourDataRow[fieldName] != DBNull.Value &&
                               double.TryParse(hourDataRow[fieldName].ToString(), out double value))
                            {
                                disPlayValueStr = value.ToString("F2");
                            }

                            SetCellValueByDataPropertyName(displayRow, fieldName, disPlayValueStr);
                        }

                        // 设置小时流量数据
                        {
                            string flowValueStr = "--";
                            if(hourDataRow.Table.Columns.Contains(DbConfig.TOTAL_FLOW) &&
                               hourDataRow[DbConfig.TOTAL_FLOW] != DBNull.Value &&
                               double.TryParse(hourDataRow[DbConfig.TOTAL_FLOW].ToString(), out double flowValue))
                            {
                                flowValueStr = flowValue.ToString("F2");
                            }
                            SetCellValueByDataPropertyName(displayRow, DbConfig.TOTAL_FLOW, flowValueStr);
                        }
                    }
                    else
                    {
                        // 没有数据时设置所有列为"--"
                        foreach(var queryNode in _queryNodes)
                        {
                            if(queryNode != _totalFlowNode)
                            {
                                string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                                SetCellValueByDataPropertyName(displayRow, fieldName, "--");
                            }
                        }
                        SetCellValueByDataPropertyName(displayRow, DbConfig.TOTAL_FLOW, "--");
                    }
                }

                // 添加统计行
                AddStatisticsRows(allHourData);
            }
            catch(Exception ex)
            {
                throw new Exception($"生成日报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 生成周期报表数据（周、月、季、年）
        /// </summary>
        private void GeneratePeriodReport()
        {
            try
            {
                // 构建查询字段列表
                StringBuilder selectFields = new StringBuilder("datatime, ");
                foreach(var queryNode in _queryNodes)
                {
                    selectFields.Append($"{DbConfig.PREFIX_F}{queryNode.id}, ");
                }
                selectFields.Append(DbConfig.TOTAL_FLOW);

                // 查询日数据
                int dataType = (int)ePollutionDataType.日数据;
                string strSql = $"select {selectFields} from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={dataType} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 清空现有数据
                dgvData.Rows.Clear();

                // 根据周期类型处理数据聚合
                var processedData = ProcessDataByPeriodType(dataTable, _currentPeriodType);
                var allPeriodData = new List<Dictionary<string, object>>();

                // 获取所有预期的时间段（确保即使没有数据也显示）
                var allExpectedTimeKeys = GetAllExpectedTimeKeys(_currentPeriodType);

                foreach(string timeKey in allExpectedTimeKeys)
                {
                    // 创建显示行
                    int rowIndex = dgvData.Rows.Add();
                    DataGridViewRow displayRow = dgvData.Rows[rowIndex];

                    // 设置时间列
                    SetCellValueByDataPropertyName(displayRow, "Time", timeKey);

                    // 检查是否有该时间段的数据
                    if(processedData.ContainsKey(timeKey))
                    {
                        var timeData = processedData[timeKey];
                        allPeriodData.Add(timeData);

                        // 添加因子数值
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            string disPlayValueStr = "--";

                            if(timeData.ContainsKey(fieldName) && timeData[fieldName] != null)
                            {
                                if(double.TryParse(timeData[fieldName].ToString(), out double value))
                                {
                                    disPlayValueStr = value.ToString("F2");
                                }
                            }

                            SetCellValueByDataPropertyName(displayRow, fieldName, disPlayValueStr);
                        }
                    }
                    else
                    {
                        // 没有数据时设置所有列为"--"
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            SetCellValueByDataPropertyName(displayRow, fieldName, "--");
                        }
                        SetCellValueByDataPropertyName(displayRow, DbConfig.TOTAL_FLOW, "--");
                    }
                }

                // 添加统计行
                AddStatisticsRowsFromProcessedData(allPeriodData);
            }
            catch(Exception ex)
            {
                throw new Exception($"生成周期报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据周期类型处理数据聚合
        /// </summary>
        private Dictionary<string, Dictionary<string, object>> ProcessDataByPeriodType(DataTable dataTable, ePollutionReportPeriodType periodType)
        {
            var result = new Dictionary<string, Dictionary<string, object>>();

            if(periodType == ePollutionReportPeriodType.Week || periodType == ePollutionReportPeriodType.Month)
            {
                // 周报表和月报表：直接使用日数据，每个时间段取第一条数据
                var timeGroups = GetTimeGroups(dataTable, periodType);
                foreach(var timeGroup in timeGroups)
                {
                    if(timeGroup.Value.Any())
                    {
                        var firstRow = timeGroup.Value.First();
                        var timeData = new Dictionary<string, object>();

                        // 添加因子数据
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            if(firstRow.Table.Columns.Contains(fieldName) && firstRow[fieldName] != DBNull.Value)
                            {
                                timeData[fieldName] = firstRow[fieldName];
                            }
                        }

                        // 添加流量数据
                        if(firstRow.Table.Columns.Contains(DbConfig.TOTAL_FLOW) && firstRow[DbConfig.TOTAL_FLOW] != DBNull.Value)
                        {
                            timeData[DbConfig.TOTAL_FLOW] = firstRow[DbConfig.TOTAL_FLOW];
                        }

                        result[timeGroup.Key] = timeData;
                    }
                }
            }
            else if(periodType == ePollutionReportPeriodType.Quarter || periodType == ePollutionReportPeriodType.Year)
            {
                // 季度报表和年报表：先按月聚合，再按时间段分组
                result = ProcessQuarterYearData(dataTable, periodType);
            }

            return result;
        }

        /// <summary>
        /// 处理季度和年度数据聚合
        /// </summary>
        private Dictionary<string, Dictionary<string, object>> ProcessQuarterYearData(DataTable dataTable, ePollutionReportPeriodType periodType)
        {
            var result = new Dictionary<string, Dictionary<string, object>>();

            // 使用LINQ按月份分组
            var monthlyGroups = dataTable.Rows.Cast<DataRow>()
                .Where(row => DateTime.TryParse(row["datatime"].ToString(), out _))
                .GroupBy(row => DateTime.Parse(row["datatime"].ToString()).Month);

            foreach(var monthGroup in monthlyGroups)
            {
                int month = monthGroup.Key;
                string timeKey = $"{month}月";
                var aggregatedData = new Dictionary<string, object>();

                // 处理各因子数据
                foreach(var queryNode in _queryNodes)
                {
                    string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";

                    if(queryNode == _totalFlowNode)
                    {
                        // 累计流量：取最后一个有效值
                        var cumulativeValue = monthGroup
                            .Where(row => row.Table.Columns.Contains(fieldName) &&
                                         row[fieldName] != DBNull.Value &&
                                         double.TryParse(row[fieldName].ToString(), out _))
                            .Select(row => double.Parse(row[fieldName].ToString()))
                            .LastOrDefault();

                        if(cumulativeValue != 0)
                        {
                            aggregatedData[fieldName] = cumulativeValue;
                        }
                    }
                    else
                    {
                        // 普通因子：计算加权平均值
                        var validRows = monthGroup
                            .Where(row => row.Table.Columns.Contains(fieldName) &&
                                         row[fieldName] != DBNull.Value &&
                                         row.Table.Columns.Contains(DbConfig.TOTAL_FLOW) &&
                                         row[DbConfig.TOTAL_FLOW] != DBNull.Value &&
                                         double.TryParse(row[fieldName].ToString(), out _) &&
                                         double.TryParse(row[DbConfig.TOTAL_FLOW].ToString(), out double flow) && flow > 0);

                        if(validRows.Any())
                        {
                            var weightedSum = validRows.Sum(row =>
                                double.Parse(row[fieldName].ToString()) * double.Parse(row[DbConfig.TOTAL_FLOW].ToString()));
                            var totalFlow = validRows.Sum(row =>
                                double.Parse(row[DbConfig.TOTAL_FLOW].ToString()));

                            if(totalFlow > 0)
                            {
                                aggregatedData[fieldName] = weightedSum / totalFlow;
                            }
                        }
                    }
                }

                // 时段流量：计算总和
                var totalFlowSum = monthGroup
                    .Where(row => row.Table.Columns.Contains(DbConfig.TOTAL_FLOW) &&
                                 row[DbConfig.TOTAL_FLOW] != DBNull.Value &&
                                 double.TryParse(row[DbConfig.TOTAL_FLOW].ToString(), out _))
                    .Sum(row => double.Parse(row[DbConfig.TOTAL_FLOW].ToString()));

                aggregatedData[DbConfig.TOTAL_FLOW] = totalFlowSum;

                result[timeKey] = aggregatedData;
            }

            return result;
        }

        /// <summary>
        /// 添加统计行（处理新的数据结构）
        /// </summary>
        private void AddStatisticsRowsFromProcessedData(List<Dictionary<string, object>> allData)
        {
            try
            {
                // 添加统计行：有效均值、最大值、最小值、总量
                AddStatisticRowFromProcessedData("有效均值", allData, StatisticType.Average);
                AddStatisticRowFromProcessedData("最大值", allData, StatisticType.Max);
                AddStatisticRowFromProcessedData("最小值", allData, StatisticType.Min);
                AddStatisticRowFromProcessedData("总量", allData, StatisticType.Total);
            }
            catch(Exception ex)
            {
                // 统计行添加失败不影响主要数据显示
                Debug.WriteLine($"添加统计行失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 添加单个统计行（处理新的数据结构）
        /// </summary>
        private void AddStatisticRowFromProcessedData(string statisticName, List<Dictionary<string, object>> allData, StatisticType statisticType)
        {
            // 创建统计行
            int rowIndex = dgvData.Rows.Add();
            DataGridViewRow statisticRow = dgvData.Rows[rowIndex];

            // 设置统计行的标题列
            SetCellValueByDataPropertyName(statisticRow, "Time", statisticName);

            // 为每个因子计算统计值
            foreach(var queryNode in _queryNodes)
            {
                if(queryNode != _totalFlowNode)
                {
                    string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";

                    // 从所有数据中获取该因子的有效值
                    var validValues = allData
                        .Where(data => data.ContainsKey(fieldName) && data[fieldName] != null)
                        .Select(data => double.TryParse(data[fieldName].ToString(), out double val) ? (double?)val : null)
                        .Where(v => v.HasValue)
                        .Select(v => v.Value)
                        .ToList();

                    string resultStr = "--";
                    if(validValues.Any())
                    {
                        double result = 0;
                        switch(statisticType)
                        {
                            case StatisticType.Average:
                                // 对于普通因子，计算加权平均
                                result = CalculateWeightedAverage(allData, fieldName);
                                break;
                            case StatisticType.Max:
                                result = validValues.Max();
                                break;
                            case StatisticType.Min:
                                result = validValues.Min();
                                break;
                            case StatisticType.Total:
                                // pH因子不计算总量
                                if(IsPhFactor(queryNode.name))
                                {
                                    result = double.NaN;
                                }
                                else
                                {
                                    // 对于普通因子，计算加权总量
                                    result = CalculateWeightedTotal(allData, fieldName);
                                }
                                break;
                        }

                        resultStr = double.IsNaN(result) ? "--" : result.ToString("F2");
                    }

                    SetCellValueByDataPropertyName(statisticRow, fieldName, resultStr);
                }
            }

            // 小时流量列
            string flowResultStr = "--";
            var validFlowValues = allData
                .Where(data => data.ContainsKey(DbConfig.TOTAL_FLOW) && data[DbConfig.TOTAL_FLOW] != null)
                .Select(data => double.TryParse(data[DbConfig.TOTAL_FLOW].ToString(), out double val) ? (double?)val : null)
                .Where(v => v.HasValue)
                .Select(v => v.Value)
                .ToList();

            if(validFlowValues.Any())
            {
                double flowResult = 0;
                switch(statisticType)
                {
                    case StatisticType.Average:
                        flowResult = validFlowValues.Average();
                        break;
                    case StatisticType.Max:
                        flowResult = validFlowValues.Max();
                        break;
                    case StatisticType.Min:
                        flowResult = validFlowValues.Min();
                        break;
                    case StatisticType.Total:
                        flowResult = validFlowValues.Sum();
                        break;
                }

                if(!double.IsNaN(flowResult))
                {
                    flowResultStr = flowResult.ToString("F2");
                }
            }

            SetCellValueByDataPropertyName(statisticRow, DbConfig.TOTAL_FLOW, flowResultStr);

            // 累计流量列
            string totalFlowResultStr = "--";
            if(_totalFlowNode != null)
            {
                string totalFlowFieldName = $"{DbConfig.PREFIX_F}{_totalFlowNode.id}";
                var validCumulativeValues = allData
                    .Where(data => data.ContainsKey(totalFlowFieldName) && data[totalFlowFieldName] != null)
                    .Select(data => double.TryParse(data[totalFlowFieldName].ToString(), out double val) ? (double?)val : null)
                    .Where(v => v.HasValue)
                    .Select(v => v.Value)
                    .ToList();

                if(validCumulativeValues.Any())
                {
                    switch(statisticType)
                    {
                        case StatisticType.Average:
                        case StatisticType.Max:
                        case StatisticType.Min:
                            totalFlowResultStr = "--";
                            break;
                        case StatisticType.Total:
                            // 累计流量的总量取最后一个有效值
                            totalFlowResultStr = validCumulativeValues.LastOrDefault().ToString("F2");
                            break;
                    }
                }

                SetCellValueByDataPropertyName(statisticRow, totalFlowFieldName, totalFlowResultStr);
            }
        }

        /// <summary>
        /// 计算加权平均值
        /// </summary>
        private double CalculateWeightedAverage(List<Dictionary<string, object>> allData, string fieldName)
        {
            double weightedSum = 0;
            double totalWeight = 0;

            foreach(var data in allData)
            {
                if(data.ContainsKey(fieldName) && data[fieldName] != null &&
                   data.ContainsKey(DbConfig.TOTAL_FLOW) && data[DbConfig.TOTAL_FLOW] != null)
                {
                    if(double.TryParse(data[fieldName].ToString(), out double factorValue) &&
                       double.TryParse(data[DbConfig.TOTAL_FLOW].ToString(), out double flowValue) &&
                       flowValue > 0)
                    {
                        weightedSum += factorValue * flowValue;
                        totalWeight += flowValue;
                    }
                }
            }

            return totalWeight > 0 ? weightedSum / totalWeight : double.NaN;
        }

        /// <summary>
        /// 计算加权总量
        /// </summary>
        private double CalculateWeightedTotal(List<Dictionary<string, object>> allData, string fieldName)
        {
            double weightedSum = 0;

            foreach(var data in allData)
            {
                if(data.ContainsKey(fieldName) && data[fieldName] != null &&
                   data.ContainsKey(DbConfig.TOTAL_FLOW) && data[DbConfig.TOTAL_FLOW] != null)
                {
                    if(double.TryParse(data[fieldName].ToString(), out double factorValue) &&
                       double.TryParse(data[DbConfig.TOTAL_FLOW].ToString(), out double flowValue) &&
                       flowValue > 0)
                    {
                        weightedSum += factorValue * flowValue;
                    }
                }
            }

            return weightedSum;
        }

        /// <summary>
        /// 添加统计行
        /// </summary>
        private void AddStatisticsRows(List<List<DataRow>> allData)
        {
            try
            {
                // 合并所有数据用于统计计算
                var allRows = allData.SelectMany(x => x).ToList();

                // 添加统计行：有效日均值、最大值、最小值、总量
                AddStatisticRow("有效均值", allRows, StatisticType.Average);
                AddStatisticRow("最大值", allRows, StatisticType.Max);
                AddStatisticRow("最小值", allRows, StatisticType.Min);
                AddStatisticRow("总量", allRows, StatisticType.Total);
            }
            catch(Exception ex)
            {
                // 统计行添加失败不影响主要数据显示
                Debug.WriteLine($"添加统计行失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 添加单个统计行
        /// </summary>
        private void AddStatisticRow(string statisticName, List<DataRow> allData, StatisticType statisticType)
        {
            // 创建统计行
            int rowIndex = dgvData.Rows.Add();
            DataGridViewRow statisticRow = dgvData.Rows[rowIndex];

            // 设置统计行的标题列
            SetCellValueByDataPropertyName(statisticRow, "Time", statisticName);

            // 为每个因子计算统计值
            foreach(var queryNode in _queryNodes)
            {
                if(queryNode != _totalFlowNode)
                {
                    string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";

                    // 从所有数据行中获取该因子的有效值
                    var validValues = allData
                        .Where(row => row.Table.Columns.Contains(fieldName) &&
                                     row[fieldName] != DBNull.Value &&
                                     !string.IsNullOrEmpty(row[fieldName].ToString()))
                        .Select(row => double.TryParse(row[fieldName].ToString(), out double val) ? (double?)val : null)
                        .Where(v => v.HasValue)
                        .Select(v => v.Value)
                        .ToList();


                    string resultStr = "--";
                    if(validValues.Any())
                    {
                        double result = 0;
                        switch(statisticType)
                        {
                            case StatisticType.Average:
                                result = validValues.Average();
                                break;
                            case StatisticType.Max:
                                result = validValues.Max();
                                break;
                            case StatisticType.Min:
                                result = validValues.Min();
                                break;
                            case StatisticType.Total:
                                // pH因子不计算总量
                                if(IsPhFactor(queryNode.name))
                                {
                                    result = double.NaN;
                                }
                                else
                                {
                                    result = validValues.Sum();
                                }
                                break;
                        }

                        resultStr = double.IsNaN(result) ? "--" : result.ToString("F2");
                    }

                    SetCellValueByDataPropertyName(statisticRow, fieldName, resultStr);
                }
            }

            // 小时流量列（只在日报表显示）
            string flowResultStr = "--";
            if(_currentPeriodType == ePollutionReportPeriodType.Day)
            {
                var validFlowValues = allData
                    .Where(row => row.Table.Columns.Contains(DbConfig.TOTAL_FLOW) &&
                                 row[DbConfig.TOTAL_FLOW] != DBNull.Value &&
                                 !string.IsNullOrEmpty(row[DbConfig.TOTAL_FLOW].ToString()))
                    .Select(row => double.TryParse(row[DbConfig.TOTAL_FLOW].ToString(), out double val) ? (double?)val : null)
                    .Where(v => v.HasValue)
                    .Select(v => v.Value)
                    .ToList();

                if(validFlowValues.Any() && statisticType != StatisticType.Total)
                {
                    double flowResult = 0;
                    switch(statisticType)
                    {
                        case StatisticType.Average:
                            flowResult = validFlowValues.Average();
                            break;
                        case StatisticType.Max:
                            flowResult = validFlowValues.Max();
                            break;
                        case StatisticType.Min:
                            flowResult = validFlowValues.Min();
                            break;
                        case StatisticType.Total:
                            flowResult = validFlowValues.Sum();
                            break;
                    }
                    flowResultStr = flowResult.ToString("F2");
                }
            }

            SetCellValueByDataPropertyName(statisticRow, DbConfig.TOTAL_FLOW, flowResultStr);

            // 累计流量列
            string totalFlowResultStr = "--";
            if(_totalFlowNode != null)
            {
                string totalFlowFieldName = $"{DbConfig.PREFIX_F}{_totalFlowNode.id}";
                var validCumulativeValues = allData
                    .Where(row => row.Table.Columns.Contains(totalFlowFieldName) &&
                                 row[totalFlowFieldName] != DBNull.Value &&
                                 !string.IsNullOrEmpty(row[totalFlowFieldName].ToString()))
                    .Select(row => double.TryParse(row[totalFlowFieldName].ToString(), out double val) ? (double?)val : null)
                    .Where(v => v.HasValue)
                    .Select(v => v.Value)
                    .ToList();

                if(validCumulativeValues.Any())
                {
                    switch(statisticType)
                    {
                        case StatisticType.Average:
                        case StatisticType.Max:
                        case StatisticType.Min:
                            totalFlowResultStr = "--";
                            break;
                        case StatisticType.Total:
                            // 累计流量的总量取最后一个有效值
                            totalFlowResultStr = validCumulativeValues.LastOrDefault().ToString("F2");
                            break;
                    }
                }

                SetCellValueByDataPropertyName(statisticRow, totalFlowFieldName, totalFlowResultStr);
            }
        }

        /// <summary>
        /// 根据列dataPropertyName赋值
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dataPropertyName"></param>
        /// <param name="value"></param>
        private void SetCellValueByDataPropertyName(DataGridViewRow row, string dataPropertyName, object value)
        {
            foreach(DataGridViewColumn column in dgvData.Columns)
            {
                if(column.DataPropertyName == dataPropertyName)
                {
                    row.Cells[column.Index].Value = value;
                    break;
                }
            }
        }

        /// <summary>
        /// 判断是否为pH因子
        /// </summary>
        private bool IsPhFactor(string factorName)
        {
            return factorName != null && factorName.ToLower().Contains("ph");
        }

        /// <summary>
        /// 根据周期类型获取时间分组
        /// </summary>
        private Dictionary<string, List<DataRow>> GetTimeGroups(DataTable dataTable, ePollutionReportPeriodType periodType)
        {
            var groups = new Dictionary<string, List<DataRow>>();

            foreach(DataRow row in dataTable.Rows)
            {
                DateTime dataTime = DateTime.Parse(row["datatime"].ToString());
                string groupKey = GetTimeGroupKey(dataTime, periodType);

                if(!groups.ContainsKey(groupKey))
                {
                    groups[groupKey] = new List<DataRow>();
                }
                groups[groupKey].Add(row);
            }

            return groups;
        }

        /// <summary>
        /// 获取所有预期的时间段键值（确保即使没有数据也显示所有时间段）
        /// </summary>
        private List<string> GetAllExpectedTimeKeys(ePollutionReportPeriodType periodType)
        {
            var timeKeys = new List<string>();

            switch(periodType)
            {
                case ePollutionReportPeriodType.Week:
                    // 周报表：周一到周日
                    timeKeys.AddRange(new[] { "周一", "周二", "周三", "周四", "周五", "周六", "周日" });
                    break;

                case ePollutionReportPeriodType.Month:
                    // 月报表：根据选择的月份计算实际天数
                    int daysInMonth = DateTime.DaysInMonth(_selectedTime.Year, _selectedTime.Month);
                    for(int day = 1; day <= daysInMonth; day++)
                    {
                        timeKeys.Add($"{day}日");
                    }
                    break;

                case ePollutionReportPeriodType.Quarter:
                    // 季度报表：只显示选择日期所在季度的三个月
                    int quarter = (_selectedTime.Month - 1) / 3 + 1;
                    int startMonth = (quarter - 1) * 3 + 1;
                    for(int month = startMonth; month < startMonth + 3; month++)
                    {
                        timeKeys.Add($"{month}月");
                    }
                    break;

                case ePollutionReportPeriodType.Year:
                    // 年报表：1月到12月
                    for(int month = 1; month <= 12; month++)
                    {
                        timeKeys.Add($"{month}月");
                    }
                    break;

                default:
                    // 其他情况：返回空列表
                    break;
            }

            return timeKeys;
        }

        /// <summary>
        /// 获取时间分组键
        /// </summary>
        private string GetTimeGroupKey(DateTime dateTime, ePollutionReportPeriodType periodType)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Week:
                    // 周报表：返回星期几
                    return GetWeekdayLabel(dateTime.DayOfWeek);
                case ePollutionReportPeriodType.Month:
                    // 月报表：返回几日
                    return $"{dateTime.Day}日";
                case ePollutionReportPeriodType.Quarter:
                    // 季度报表：返回月份
                    return $"{dateTime.Month}月";
                case ePollutionReportPeriodType.Year:
                    // 年报表：返回月份
                    return $"{dateTime.Month}月";
                default:
                    return dateTime.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// 获取星期几的中文标签
        /// </summary>
        private string GetWeekdayLabel(DayOfWeek dayOfWeek)
        {
            switch(dayOfWeek)
            {
                case DayOfWeek.Monday:
                    return "周一";
                case DayOfWeek.Tuesday:
                    return "周二";
                case DayOfWeek.Wednesday:
                    return "周三";
                case DayOfWeek.Thursday:
                    return "周四";
                case DayOfWeek.Friday:
                    return "周五";
                case DayOfWeek.Saturday:
                    return "周六";
                case DayOfWeek.Sunday:
                    return "周日";
                default:
                    return dayOfWeek.ToString();
            }
        }

        #endregion

        #endregion

        #region 枚举定义

        /// <summary>
        /// 污染数据统计报表时间周期枚举
        /// </summary>
        public enum ePollutionReportPeriodType
        {
            /// <summary>
            /// 日报表
            /// </summary>
            [Description("日报表")]
            Day = 0,

            /// <summary>
            /// 周报表
            /// </summary>
            [Description("周报表")]
            Week = 1,

            /// <summary>
            /// 月报表
            /// </summary>
            [Description("月报表")]
            Month = 2,

            /// <summary>
            /// 季报表
            /// </summary>
            [Description("季报表")]
            Quarter = 3,

            /// <summary>
            /// 年报表
            /// </summary>
            [Description("年报表")]
            Year = 4
        }

        /// <summary>
        /// 统计类型枚举
        /// </summary>
        private enum StatisticType
        {
            /// <summary>
            /// 最大值
            /// </summary>
            [Description("最大值")]
            Max,

            /// <summary>
            /// 最小值
            /// </summary>
            [Description("最小值")]
            Min,

            /// <summary>
            /// 平均值
            /// </summary>
            [Description("平均值")]
            Average,

            /// <summary>
            /// 总量
            /// </summary>
            [Description("平均值")]
            Total
        }

        #endregion
    }
}