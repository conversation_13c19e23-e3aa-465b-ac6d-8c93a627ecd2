using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Fpi.DB;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.Util;
using Fpi.Util.Extensions;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Pollution.DB;
using Sunny.UI;
using DbConfig = Fpi.WMS3000.Pollution.DB.DbConfig;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 数据报表
    /// 日、周、月、季、年报表查询
    /// </summary>
    public partial class UC_QueryDataReport : UIUserControl
    {
        #region 属性、字段

        /// <summary>
        /// 左上角显示表名
        /// </summary>
        public string TitleName { get; set; } = "数据统计报表";

        /// <summary>
        /// 对应统计因子
        /// </summary>
        private List<QueryNode> _queryNodes = new List<QueryNode>();

        /// <summary>
        /// 当前选择的时间周期类型
        /// </summary>
        private ePollutionReportPeriodType _currentPeriodType = ePollutionReportPeriodType.Day;

        /// <summary>
        /// 选择的时间点（某天、某月等）
        /// </summary>
        private DateTime _selectedTime;

        /// <summary>
        /// 实际查询起始时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _startTime;

        /// <summary>
        /// 实际查询结束时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _endTime;

        /// <summary>
        /// 当前查询时段，数据导出时标题用
        /// </summary>
        private string _timePeriod;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 累计流量因子
        /// </summary>
        private QueryNode _totalFlowNode;

        /// <summary>
        /// 小时流量列引用
        /// </summary>
        private DataGridViewColumn _hourFlowColumn;

        #endregion

        #region 构造

        public UC_QueryDataReport()
        {
            InitializeComponent();

        }

        public UC_QueryDataReport(string groupId)
        {
            InitializeComponent();

            // 设置报表因子组
            QueryGroup queryGroup = ReportManager.GetInstance().GetQueryGroupByGroupIdOrFirst(groupId);
            if(queryGroup != null)
            {
                foreach(QueryNode queryNode in queryGroup.QueryNodes)
                {
                    this._queryNodes.Add(queryNode);
                }

                TitleName = queryGroup.name;
            }
        }

        #endregion

        #region 事件

        private void UC_QueryDataReport_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                // 初始化界面标题
                InitialTitle();
                // 初始化时间选择器
                InitialDateTimePicker();
                // 更新日期选择器的类型
                UpdateDateSelectorVisibility();
                // 更新当前统计周期标签
                UpdateCurrentPeriodLabel();
                // 设置数据表格头部
                SetDataGridViewHead();
            }
        }

        /// <summary>
        /// 时间周期选择变更事件
        /// </summary>
        private void rbPeriodType_CheckedChanged(object sender, EventArgs e)
        {
            var radioButton = sender as UIRadioButton;
            if(radioButton != null && radioButton.Checked)
            {
                // 根据单选按钮确定周期类型
                if(radioButton.Name == "rbDay")
                    _currentPeriodType = ePollutionReportPeriodType.Day;
                else if(radioButton.Name == "rbWeek")
                    _currentPeriodType = ePollutionReportPeriodType.Week;
                else if(radioButton.Name == "rbMonth")
                    _currentPeriodType = ePollutionReportPeriodType.Month;
                else if(radioButton.Name == "rbQuarter")
                    _currentPeriodType = ePollutionReportPeriodType.Quarter;
                else if(radioButton.Name == "rbYear")
                    _currentPeriodType = ePollutionReportPeriodType.Year;

                // 清空界面数据
                dgvData.ClearRows();
                // 重新计算查询时间范围
                CalculateQueryTimeRange();
                // 更新当前周期标签
                UpdateCurrentPeriodLabel();
                // 更新日期选择器的类型
                UpdateDateSelectorVisibility();
                // 更新小时流量列的可见性
                UpdateHourFlowColumnVisibility();
            }
        }

        /// <summary>
        /// 时间选择变更事件
        /// </summary>
        private void dtpSelectedTime_ValueChanged(object sender, DateTime value)
        {
            // 更新选择的时间点
            _selectedTime = value;
            // 根据选择的时间点和周期类型计算查询时间范围
            CalculateQueryTimeRange();
            // 更新当前周期标签
            UpdateCurrentPeriodLabel();
        }

        /// <summary>
        /// 点击查询按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void btnStartQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;

                _timePeriod = lblCurrentPeriod.Text;

                // 清空界面数据
                dgvData.ClearRows();

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"查询{TitleName}", eOpType.浏览操作, eOpStyle.本地操作));

                // 根据当前周期类型计算查询时间范围
                CalculateQueryTimeRange();

                // 检查查询条件
                Check();

                // 刷新页面显示
                FlushView();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 报告导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if(dgvData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Path.Combine(Application.StartupPath, "query");
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName = Path.Combine(filePath, $"{TitleName}_{_currentPeriodType.GetDescription()}_{_timePeriod}.xlsx");

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvData, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if(_startTime > _endTime)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            // 检查查询因子
            if(_queryNodes == null || _queryNodes.Count == 0)
            {
                throw new Exception("未配置查询因子！");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            try
            {
                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 根据报表类型生成数据
                if(_currentPeriodType == ePollutionReportPeriodType.Day)
                {
                    GenerateDayReport();
                }
                else
                {
                    GeneratePeriodReport();
                }

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        #region 控件操作

        /// <summary>
        /// 设置表头
        /// </summary>
        private void InitialTitle()
        {
            this.Text = TitleName;
        }

        /// <summary>
        /// 初始化时间选择控件
        /// </summary>
        private void InitialDateTimePicker()
        {
            // 初始化选择时间点为今天
            _selectedTime = DateTime.Today;
            // 设置默认时间
            dtpSelectedTime.Value = _selectedTime;
            // 根据当前周期类型计算实际查询时间范围
            CalculateQueryTimeRange();
        }

        /// <summary>
        /// 更新日期选择器的类型
        /// </summary>
        private void UpdateDateSelectorVisibility()
        {
            // 根据当前周期类型设置时间选择控件的显示方式
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：显示日期选择器（年月日）
                    dtpSelectedTime.ShowType = UIDateType.YearMonthDay;
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：显示日期选择器（选择某一天，自动计算所在周）
                    dtpSelectedTime.ShowType = UIDateType.YearMonthDay;
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：显示年月选择器
                    dtpSelectedTime.ShowType = UIDateType.YearMonth;
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：显示年月选择器（选择某个月，自动计算所在季度）
                    dtpSelectedTime.ShowType = UIDateType.YearMonth;
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：显示年选择器
                    dtpSelectedTime.ShowType = UIDateType.Year;
                    break;
            }
        }

        /// <summary>
        /// 更新当前统计周期标签
        /// </summary>
        private void UpdateCurrentPeriodLabel()
        {
            // 根据具体的标签控件来显示周期信息
            lblCurrentPeriod.Text = GetPeriodDisplayText(_currentPeriodType, _selectedTime);
        }

        /// <summary>
        /// 根据选择的时间点和报表周期类型计算查询时间范围
        /// </summary>
        private void CalculateQueryTimeRange()
        {
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：查询选择日期当天的数据
                    _startTime = _selectedTime.Date;
                    _endTime = _startTime.AddDays(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：查询选择日期所在周的数据（周一作为一周开始）
                    var weekStart = GetMondayOfWeek(_selectedTime);
                    _startTime = weekStart.Date;
                    _endTime = weekStart.AddDays(7).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：查询选择日期所在月的数据
                    var monthStart = new DateTime(_selectedTime.Year, _selectedTime.Month, 1);
                    _startTime = monthStart;
                    _endTime = monthStart.AddMonths(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：查询选择日期所在季度的数据
                    var quarterStart = GetQuarterStart(_selectedTime);
                    _startTime = quarterStart;
                    _endTime = quarterStart.AddMonths(3).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：查询选择日期所在年的数据
                    var yearStart = new DateTime(_selectedTime.Year, 1, 1);
                    _startTime = yearStart;
                    _endTime = yearStart.AddYears(1).AddSeconds(-1);
                    break;
            }
        }

        /// <summary>
        /// 获取指定日期所在周的周一日期
        /// </summary>
        private DateTime GetMondayOfWeek(DateTime date)
        {
            // 计算距离周一的天数
            int daysFromMonday = ((int)date.DayOfWeek - 1 + 7) % 7;
            return date.AddDays(-daysFromMonday);
        }

        /// <summary>
        /// 获取季度开始时间
        /// </summary>
        private DateTime GetQuarterStart(DateTime date)
        {
            int quarter = (date.Month - 1) / 3 + 1;
            int startMonth = (quarter - 1) * 3 + 1;
            return new DateTime(date.Year, startMonth, 1);
        }

        /// <summary>
        /// 获取周期显示文本
        /// </summary>
        private string GetPeriodDisplayText(ePollutionReportPeriodType periodType, DateTime selectedTime)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Day:
                    return $"{selectedTime:yyyy年MM月dd日}";
                case ePollutionReportPeriodType.Week:
                    var week = new GregorianCalendar().GetWeekOfYear(selectedTime, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
                    var weekStart = GetMondayOfWeek(selectedTime);
                    var weekEnd = weekStart.AddDays(6);
                    return $"{selectedTime.Year}年第{week}周({weekStart:yyyy-MM-dd}-{weekEnd:yyyy-MM-dd})";
                case ePollutionReportPeriodType.Month:
                    return $"{selectedTime:yyyy年MM月}";
                case ePollutionReportPeriodType.Quarter:
                    int quarter = (selectedTime.Month - 1) / 3 + 1;
                    int startMonth = (quarter - 1) * 3 + 1;
                    int endMonth = startMonth + 2;
                    return $"{selectedTime.Year}年第{quarter}季度({startMonth}月-{endMonth}月)";
                case ePollutionReportPeriodType.Year:
                    return $"{selectedTime.Year}年";
                default:
                    return "";
            }
        }

        /// <summary>
        /// 设置DataGridView表头
        /// </summary>
        private void SetDataGridViewHead()
        {
            try
            {
                // 清除现有列
                dgvData.ClearColumns();

                // 添加时间列
                dgvData.AddColumn(@"时间\参数", "Time", 250);

                // 添加常规统计因子列
                foreach(var queryNode in _queryNodes)
                {
                    // 累计流量最后显示
                    if(queryNode.id != ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId)
                    {
                        string factorName = queryNode.name;
                        string unit = queryNode.UnitId ?? string.Empty;

                        // 添加因子数值列，设置DataPropertyName为数据库字段名
                        // PH不显示单位；未配置单位时不显示单位
                        if(IsPhFactor(queryNode.name) || string.IsNullOrEmpty(unit))
                        {
                            dgvData.AddColumn($"{factorName}", $"{DbConfig.PREFIX_F}{queryNode.id}", 200);
                        }
                        else
                        {
                            dgvData.AddColumn($"{factorName}({unit})", $"{DbConfig.PREFIX_F}{queryNode.id}", 200);
                        }
                    }
                    else
                    {
                        _totalFlowNode = queryNode;
                    }
                }

                // 添加小时流量列
                _hourFlowColumn = dgvData.AddColumn("小时流量(m³)", DbConfig.TOTAL_FLOW, 300);

                // 添加累计流量列
                if(_totalFlowNode != null)
                {
                    string factorName = _totalFlowNode.name;
                    string unit = _totalFlowNode.UnitId ?? "";

                    // 添加因子数值列，设置DataPropertyName为数据库字段名
                    var col = dgvData.AddColumn($"{factorName}({unit})", $"{DbConfig.PREFIX_F}{_totalFlowNode.id}", 300);
                }

                // 设置列宽和样式
                foreach(DataGridViewColumn column in dgvData.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.SortMode = DataGridViewColumnSortMode.NotSortable;
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"设置表头失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新小时流量列的可见性
        /// </summary>
        private void UpdateHourFlowColumnVisibility()
        {
            if(_hourFlowColumn != null)
            {
                _hourFlowColumn.Visible = (_currentPeriodType == ePollutionReportPeriodType.Day);
            }
        }

        /// <summary>
        /// 根据列dataPropertyName赋值
        /// </summary>
        /// <param name="row"></param>
        /// <param name="dataPropertyName"></param>
        /// <param name="value"></param>
        private void SetCellValueByDataPropertyName(DataGridViewRow row, string dataPropertyName, object value)
        {
            foreach(DataGridViewColumn column in dgvData.Columns)
            {
                if(column.DataPropertyName == dataPropertyName)
                {
                    row.Cells[column.Index].Value = value;
                    break;
                }
            }
        }

        #endregion

        #region 数据查询

        /// <summary>
        /// 生成日报表数据
        /// </summary>
        private void GenerateDayReport()
        {
            try
            {
                // 构建查询字段列表
                StringBuilder selectFields = new StringBuilder("datatime, ");
                foreach(var queryNode in _queryNodes)
                {
                    selectFields.Append($"{DbConfig.PREFIX_F}{queryNode.id}, ");
                }
                selectFields.Append(DbConfig.TOTAL_FLOW);

                // 查询小时数据
                string strSql = $"select {selectFields} from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={(int)ePollutionDataType.小时数据} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 转换为统一数据模型
                var allModels = ConvertDataTableToModels(dataTable);

                // 存储所有小时数据用于统计计算
                var allHourData = new List<PollutionDataModel>();

                // 生成24小时时段的报表
                for(int hour = 0; hour < 24; hour++)
                {
                    string timeLabel = $"{hour:D2}～{(hour + 1):D2}时";

                    // 获取该小时的数据
                    var hourModel = allModels.FirstOrDefault(m => m.DataTime.Hour == hour);

                    // 创建显示行
                    int rowIndex = dgvData.Rows.Add();
                    DataGridViewRow displayRow = dgvData.Rows[rowIndex];

                    // 设置时间列
                    SetCellValueByDataPropertyName(displayRow, "Time", timeLabel);

                    if(hourModel != null)
                    {
                        allHourData.Add(hourModel);

                        // 设置因子数值列
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            string disPlayValueStr = "--";

                            if(hourModel.FactorValues.ContainsKey(fieldName) && hourModel.FactorValues[fieldName].HasValue)
                            {
                                disPlayValueStr = hourModel.FactorValues[fieldName].Value.ToString($"F{queryNode.Dec}");
                            }

                            SetCellValueByDataPropertyName(displayRow, fieldName, disPlayValueStr);
                        }

                        // 设置小时流量列
                        string flowValueStr = hourModel.TotalFlow?.ToString("F2") ?? "--";
                        SetCellValueByDataPropertyName(displayRow, DbConfig.TOTAL_FLOW, flowValueStr);
                    }
                    else
                    {
                        // 没有数据时设置所有列为"--"
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            SetCellValueByDataPropertyName(displayRow, fieldName, "--");
                        }
                        SetCellValueByDataPropertyName(displayRow, DbConfig.TOTAL_FLOW, "--");
                    }
                }

                // 添加统计行
                AddStatisticsRowsFromModels(allHourData);
            }
            catch(Exception ex)
            {
                throw new Exception($"生成日报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 生成周期报表数据（周、月、季、年）
        /// </summary>
        private void GeneratePeriodReport()
        {
            try
            {
                // 构建查询字段列表
                StringBuilder selectFields = new StringBuilder("datatime, ");
                foreach(var queryNode in _queryNodes)
                {
                    selectFields.Append($"{DbConfig.PREFIX_F}{queryNode.id}, ");
                }
                selectFields.Append(DbConfig.TOTAL_FLOW);

                // 查询日数据
                int dataType = (int)ePollutionDataType.日数据;
                string strSql = $"select {selectFields} from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={dataType} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 转换为统一数据模型
                var allModels = ConvertDataTableToModels(dataTable);

                // 根据周期类型处理数据聚合
                var processedData = ProcessDataByPeriodTypeFromModels(allModels, _currentPeriodType);
                var allPeriodData = new List<PollutionDataModel>();

                // 获取所有预期的时间段（确保即使没有数据也显示）
                var allExpectedTimeKeys = GetAllExpectedTimeKeys(_currentPeriodType);

                foreach(string timeKey in allExpectedTimeKeys)
                {
                    // 创建显示行
                    int rowIndex = dgvData.Rows.Add();
                    DataGridViewRow displayRow = dgvData.Rows[rowIndex];

                    // 设置时间列
                    SetCellValueByDataPropertyName(displayRow, "Time", timeKey);

                    // 检查是否有该时间段的数据
                    if(processedData.ContainsKey(timeKey))
                    {
                        var timeModel = processedData[timeKey];
                        allPeriodData.Add(timeModel);

                        // 添加因子数值
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            string disPlayValueStr = "--";

                            if(timeModel.FactorValues.ContainsKey(fieldName) && timeModel.FactorValues[fieldName].HasValue)
                            {
                                disPlayValueStr = timeModel.FactorValues[fieldName].Value.ToString($"F{queryNode.Dec}");
                            }

                            SetCellValueByDataPropertyName(displayRow, fieldName, disPlayValueStr);
                        }
                    }
                    else
                    {
                        // 没有数据时设置所有列为"--"
                        foreach(var queryNode in _queryNodes)
                        {
                            string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                            SetCellValueByDataPropertyName(displayRow, fieldName, "--");
                        }
                    }
                }

                // 添加统计行
                AddStatisticsRowsFromModels(allPeriodData);
            }
            catch(Exception ex)
            {
                throw new Exception($"生成周期报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据周期类型处理数据聚合
        /// </summary>
        private Dictionary<string, PollutionDataModel> ProcessDataByPeriodTypeFromModels(List<PollutionDataModel> allModels, ePollutionReportPeriodType periodType)
        {
            var result = new Dictionary<string, PollutionDataModel>();

            if(periodType == ePollutionReportPeriodType.Week || periodType == ePollutionReportPeriodType.Month)
            {
                // 周报表和月报表：直接使用日数据，每个时间段取第一条数据
                var timeGroups = GetTimeGroupsFromModels(allModels, periodType);
                foreach(var timeGroup in timeGroups)
                {
                    if(timeGroup.Value.Any())
                    {
                        var firstModel = timeGroup.Value.First();
                        firstModel.TimeLabel = timeGroup.Key;
                        result[timeGroup.Key] = firstModel;
                    }
                }
            }
            else if(periodType == ePollutionReportPeriodType.Quarter || periodType == ePollutionReportPeriodType.Year)
            {
                // 季度报表和年报表：先按月聚合，再按时间段分组
                result = ProcessQuarterYearDataFromModels(allModels, periodType);
            }

            return result;
        }

        /// <summary>
        /// 处理季度和年度数据聚合
        /// </summary>
        private Dictionary<string, PollutionDataModel> ProcessQuarterYearDataFromModels(List<PollutionDataModel> allModels, ePollutionReportPeriodType periodType)
        {
            var result = new Dictionary<string, PollutionDataModel>();

            // 使用LINQ按月份分组
            var monthlyGroups = allModels
                .GroupBy(model => model.DataTime.Month);

            foreach(var monthGroup in monthlyGroups)
            {
                int month = monthGroup.Key;
                string timeKey = $"{month}月";
                var aggregatedModel = new PollutionDataModel
                {
                    DataTime = monthGroup.First().DataTime,
                    TimeLabel = timeKey
                };

                // 处理各因子数据
                foreach(var queryNode in _queryNodes)
                {
                    string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";

                    if(queryNode == _totalFlowNode)
                    {
                        // 累计流量：取最后一个有效值
                        var lastValidModel = monthGroup
                            .Where(model => model.FactorValues.ContainsKey(fieldName) && model.FactorValues[fieldName].HasValue)
                            .LastOrDefault();

                        if(lastValidModel != null)
                        {
                            aggregatedModel.FactorValues[fieldName] = lastValidModel.FactorValues[fieldName];
                        }
                    }
                    else
                    {
                        // 普通因子：计算加权平均值
                        var validModels = monthGroup
                            .Where(model => model.FactorValues.ContainsKey(fieldName) &&
                                          model.FactorValues[fieldName].HasValue &&
                                          model.TotalFlow.HasValue && model.TotalFlow.Value > 0);

                        if(validModels.Any())
                        {
                            var weightedSum = validModels.Sum(model =>
                                model.FactorValues[fieldName].Value * model.TotalFlow.Value);
                            var totalFlow = validModels.Sum(model => model.TotalFlow.Value);

                            if(totalFlow > 0)
                            {
                                aggregatedModel.FactorValues[fieldName] = weightedSum / totalFlow;
                            }
                        }
                    }
                }

                // 时段流量：计算总和
                var totalFlowSum = monthGroup
                    .Where(model => model.TotalFlow.HasValue)
                    .Sum(model => model.TotalFlow.Value);

                aggregatedModel.TotalFlow = totalFlowSum;

                result[timeKey] = aggregatedModel;
            }

            return result;
        }

        /// <summary>
        /// 添加统计行
        /// </summary>
        private void AddStatisticsRowsFromModels(List<PollutionDataModel> allData)
        {
            AddStatisticRowFromModels("有效平均值", allData, StatisticType.Average);
            AddStatisticRowFromModels("最大值", allData, StatisticType.Max);
            AddStatisticRowFromModels("最小值", allData, StatisticType.Min);
            AddStatisticRowFromModels("总量", allData, StatisticType.Total);
        }

        /// <summary>
        /// 添加单个统计行
        /// </summary>
        private void AddStatisticRowFromModels(string statisticName, List<PollutionDataModel> allData, StatisticType statisticType)
        {
            // 创建统计行
            int rowIndex = dgvData.Rows.Add();
            DataGridViewRow statisticRow = dgvData.Rows[rowIndex];

            // 设置统计行的标题列
            SetCellValueByDataPropertyName(statisticRow, "Time", statisticName);

            // 计算各因子的统计值
            foreach(var queryNode in _queryNodes)
            {
                string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";
                string resultStr = "--";

                // 获取有效数据
                var validValues = allData
                    .Where(model => model.FactorValues.ContainsKey(fieldName) && model.FactorValues[fieldName].HasValue)
                    .Select(model => model.FactorValues[fieldName].Value)
                    .ToList();

                if(validValues.Any())
                {
                    double result = double.NaN;

                    // 检查是否为累计流量因子
                    if(queryNode == _totalFlowNode)
                    {
                        // 累计流量因子的特殊处理
                        switch(statisticType)
                        {
                            case StatisticType.Average:
                            case StatisticType.Max:
                            case StatisticType.Min:
                                // 累计流量因子的平均值、最大值、最小值不显示
                                resultStr = "--";
                                break;
                            case StatisticType.Total:
                                // 累计流量因子的总量：显示最后一条有效数据的累计流量值
                                var lastValidModel = allData.LastOrDefault(m =>
                                    m.FactorValues.ContainsKey(fieldName) && m.FactorValues[fieldName].HasValue);
                                if(lastValidModel != null)
                                {
                                    result = lastValidModel.FactorValues[fieldName].Value;
                                }
                                break;
                        }
                    }
                    else
                    {
                        // 普通因子的处理
                        switch(statisticType)
                        {
                            case StatisticType.Average:
                                if(IsPhFactor(queryNode.name))
                                {
                                    // pH值使用算术平均
                                    result = validValues.Average();
                                }
                                else
                                {
                                    // 其他因子使用加权平均
                                    result = CalculateWeightedAverageFromModels(allData, fieldName);
                                }
                                break;
                            case StatisticType.Max:
                                result = validValues.Max();
                                break;
                            case StatisticType.Min:
                                result = validValues.Min();
                                break;
                            case StatisticType.Total:
                                if(IsPhFactor(queryNode.name))
                                {
                                    // pH值不计算总量
                                    resultStr = "--";
                                }
                                else
                                {
                                    // 其他因子使用加权总量
                                    result = CalculateWeightedTotalFromModels(allData, fieldName);
                                }
                                break;
                        }
                    }

                    if(!double.IsNaN(result))
                    {
                        resultStr = result.ToString($"F{queryNode.Dec}");
                    }
                }

                SetCellValueByDataPropertyName(statisticRow, fieldName, resultStr);
            }

            // 计算时段流量统计值
            string totalFlowResultStr = "--";
            var validFlowValues = allData.Where(model => model.TotalFlow.HasValue).Select(model => model.TotalFlow.Value).ToList();

            if(validFlowValues.Any())
            {
                double flowResult = double.NaN;
                switch(statisticType)
                {
                    case StatisticType.Average:
                        flowResult = validFlowValues.Average();
                        break;
                    case StatisticType.Max:
                        flowResult = validFlowValues.Max();
                        break;
                    case StatisticType.Min:
                        flowResult = validFlowValues.Min();
                        break;
                    case StatisticType.Total:
                        flowResult = validFlowValues.Sum();
                        break;
                }

                if(!double.IsNaN(flowResult))
                {
                    totalFlowResultStr = flowResult.ToString("F2");
                }
            }

            SetCellValueByDataPropertyName(statisticRow, DbConfig.TOTAL_FLOW, totalFlowResultStr);
        }

        /// <summary>
        /// 计算加权平均值
        /// </summary>
        private double CalculateWeightedAverageFromModels(List<PollutionDataModel> allData, string fieldName)
        {
            // 筛选有效数据：因子值和流量值都存在且流量值大于0
            var validModels = allData.Where(model =>
                model.FactorValues.ContainsKey(fieldName) &&
                model.FactorValues[fieldName].HasValue &&
                model.TotalFlow.HasValue &&
                model.TotalFlow.Value > 0);

            if(!validModels.Any())
                return double.NaN;

            // 计算加权平均：(各数据点的因子值 × 对应流量值的总和) / (流量值的总和)
            var weightedSum = validModels.Sum(model =>
                model.FactorValues[fieldName].Value * model.TotalFlow.Value);
            var totalWeight = validModels.Sum(model => model.TotalFlow.Value);

            return totalWeight > 0 ? weightedSum / totalWeight : double.NaN;
        }

        /// <summary>
        /// 计算加权总量
        /// </summary>
        private double CalculateWeightedTotalFromModels(List<PollutionDataModel> allData, string fieldName)
        {
            // 筛选有效数据并计算加权总量：各数据点的因子值 × 对应流量值的总和
            return allData
                .Where(model =>
                    model.FactorValues.ContainsKey(fieldName) &&
                    model.FactorValues[fieldName].HasValue &&
                    model.TotalFlow.HasValue &&
                    model.TotalFlow.Value > 0)
                .Sum(model => model.FactorValues[fieldName].Value * model.TotalFlow.Value);
        }

        #region 辅助方法

        /// <summary>
        /// 判断是否为pH因子
        /// </summary>
        private bool IsPhFactor(string factorName)
        {
            return factorName != null && factorName.ToLower().Contains("ph");
        }

        /// <summary>
        /// 根据周期类型获取时间分组
        /// </summary>
        private Dictionary<string, List<PollutionDataModel>> GetTimeGroupsFromModels(List<PollutionDataModel> allModels, ePollutionReportPeriodType periodType)
        {
            var groups = new Dictionary<string, List<PollutionDataModel>>();

            foreach(var model in allModels)
            {
                string groupKey = GetTimeGroupKey(model.DataTime, periodType);

                if(!groups.ContainsKey(groupKey))
                {
                    groups[groupKey] = new List<PollutionDataModel>();
                }
                groups[groupKey].Add(model);
            }

            return groups;
        }

        /// <summary>
        /// 获取所有预期的时间段键值（确保即使没有数据也显示所有时间段）
        /// </summary>
        private List<string> GetAllExpectedTimeKeys(ePollutionReportPeriodType periodType)
        {
            var timeKeys = new List<string>();

            switch(periodType)
            {
                case ePollutionReportPeriodType.Week:
                    // 周报表：周一到周日
                    timeKeys.AddRange(new[] { "周一", "周二", "周三", "周四", "周五", "周六", "周日" });
                    break;

                case ePollutionReportPeriodType.Month:
                    // 月报表：根据选择的月份计算实际天数
                    int daysInMonth = DateTime.DaysInMonth(_selectedTime.Year, _selectedTime.Month);
                    for(int day = 1; day <= daysInMonth; day++)
                    {
                        timeKeys.Add($"{day}日");
                    }
                    break;

                case ePollutionReportPeriodType.Quarter:
                    // 季度报表：只显示选择日期所在季度的三个月
                    int quarter = (_selectedTime.Month - 1) / 3 + 1;
                    int startMonth = (quarter - 1) * 3 + 1;
                    for(int month = startMonth; month < startMonth + 3; month++)
                    {
                        timeKeys.Add($"{month}月");
                    }
                    break;

                case ePollutionReportPeriodType.Year:
                    // 年报表：1月到12月
                    for(int month = 1; month <= 12; month++)
                    {
                        timeKeys.Add($"{month}月");
                    }
                    break;

                default:
                    // 其他情况：返回空列表
                    break;
            }

            return timeKeys;
        }

        /// <summary>
        /// 获取时间分组键
        /// </summary>
        private string GetTimeGroupKey(DateTime dateTime, ePollutionReportPeriodType periodType)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Week:
                    // 周报表：返回星期几
                    return GetWeekdayLabel(dateTime.DayOfWeek);
                case ePollutionReportPeriodType.Month:
                    // 月报表：返回几日
                    return $"{dateTime.Day}日";
                case ePollutionReportPeriodType.Quarter:
                    // 季度报表：返回月份
                    return $"{dateTime.Month}月";
                case ePollutionReportPeriodType.Year:
                    // 年报表：返回月份
                    return $"{dateTime.Month}月";
                default:
                    return dateTime.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// 获取星期几的中文标签
        /// </summary>
        private string GetWeekdayLabel(DayOfWeek dayOfWeek)
        {
            switch(dayOfWeek)
            {
                case DayOfWeek.Monday:
                    return "周一";
                case DayOfWeek.Tuesday:
                    return "周二";
                case DayOfWeek.Wednesday:
                    return "周三";
                case DayOfWeek.Thursday:
                    return "周四";
                case DayOfWeek.Friday:
                    return "周五";
                case DayOfWeek.Saturday:
                    return "周六";
                case DayOfWeek.Sunday:
                    return "周日";
                default:
                    return dayOfWeek.ToString();
            }
        }

        #endregion

        #endregion

        #endregion

        #region 数据模型

        /// <summary>
        /// 污染数据统一数据模型
        /// </summary>
        private class PollutionDataModel
        {
            /// <summary>
            /// 数据时间
            /// </summary>
            public DateTime DataTime { get; set; }

            /// <summary>
            /// 各因子数值字典（键为因子字段名，如"Fe01203"）
            /// </summary>
            public Dictionary<string, double?> FactorValues { get; set; } = new Dictionary<string, double?>();

            /// <summary>
            /// 时段流量值
            /// </summary>
            public double? TotalFlow { get; set; }

            /// <summary>
            /// 时间显示标签（如"01～02时"、"周一"、"1月"等）
            /// </summary>
            public string TimeLabel { get; set; }
        }

        /// <summary>
        /// 将DataTable转换为统一的数据模型列表
        /// </summary>
        private List<PollutionDataModel> ConvertDataTableToModels(DataTable dataTable)
        {
            var models = new List<PollutionDataModel>();

            foreach(DataRow row in dataTable.Rows)
            {
                var model = new PollutionDataModel();

                // 解析数据时间
                if(DateTime.TryParse(row["datatime"].ToString(), out DateTime dataTime))
                {
                    model.DataTime = dataTime;
                }

                // 解析各因子数值
                foreach(var queryNode in _queryNodes)
                {
                    string fieldName = $"{DbConfig.PREFIX_F}{queryNode.id}";

                    if(row.Table.Columns.Contains(fieldName) &&
                       row[fieldName] != DBNull.Value &&
                       double.TryParse(row[fieldName].ToString(), out double factorValue))
                    {
                        model.FactorValues[fieldName] = factorValue;
                    }
                    else
                    {
                        model.FactorValues[fieldName] = null;
                    }
                }

                // 解析时段流量
                if(row.Table.Columns.Contains(DbConfig.TOTAL_FLOW) &&
                   row[DbConfig.TOTAL_FLOW] != DBNull.Value &&
                   double.TryParse(row[DbConfig.TOTAL_FLOW].ToString(), out double totalFlow))
                {
                    model.TotalFlow = totalFlow;
                }

                models.Add(model);
            }

            return models;
        }

        #endregion

        #region 枚举定义

        /// <summary>
        /// 污染数据统计报表时间周期枚举
        /// </summary>
        public enum ePollutionReportPeriodType
        {
            /// <summary>
            /// 日报表
            /// </summary>
            [Description("日报表")]
            Day = 0,

            /// <summary>
            /// 周报表
            /// </summary>
            [Description("周报表")]
            Week = 1,

            /// <summary>
            /// 月报表
            /// </summary>
            [Description("月报表")]
            Month = 2,

            /// <summary>
            /// 季报表
            /// </summary>
            [Description("季报表")]
            Quarter = 3,

            /// <summary>
            /// 年报表
            /// </summary>
            [Description("年报表")]
            Year = 4
        }

        /// <summary>
        /// 统计类型枚举
        /// </summary>
        private enum StatisticType
        {
            /// <summary>
            /// 最大值
            /// </summary>
            [Description("最大值")]
            Max,

            /// <summary>
            /// 最小值
            /// </summary>
            [Description("最小值")]
            Min,

            /// <summary>
            /// 平均值
            /// </summary>
            [Description("平均值")]
            Average,

            /// <summary>
            /// 总量
            /// </summary>
            [Description("平均值")]
            Total
        }

        #endregion
    }
}