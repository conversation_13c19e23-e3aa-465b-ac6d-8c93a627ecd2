using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using Fpi.DB;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.Util;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Pollution.DB;
using Sunny.UI;
using DbConfig = Fpi.WMS3000.Pollution.DB.DbConfig;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 污染数据统计报表时间周期枚举
    /// </summary>
    public enum ePollutionReportPeriodType
    {
        /// <summary>
        /// 日报表
        /// </summary>
        [Description("日")]
        Day = 0,

        /// <summary>
        /// 周报表
        /// </summary>
        [Description("周")]
        Week = 1,

        /// <summary>
        /// 月报表
        /// </summary>
        [Description("月")]
        Month = 2,

        /// <summary>
        /// 季报表
        /// </summary>
        [Description("季")]
        Quarter = 3,

        /// <summary>
        /// 年报表
        /// </summary>
        [Description("年")]
        Year = 4
    }

    /// <summary>
    /// 数据报表
    /// 日、周、月、季、年报表查询
    /// </summary>
    public partial class UC_QueryDataReport : UIUserControl
    {
        #region 属性、字段

        /// <summary>
        /// 左上角显示表名
        /// </summary>
        public string TitleName { get; set; } = "数据统计报表";

        /// <summary>
        /// 对应统计因子
        /// </summary>
        private List<QueryNode> _queryNodes = new List<QueryNode>();

        /// <summary>
        /// 当前选择的时间周期类型
        /// </summary>
        private ePollutionReportPeriodType _currentPeriodType = ePollutionReportPeriodType.Day;

        /// <summary>
        /// 选择的时间点（某天、某月等）
        /// </summary>
        private DateTime _selectedTime;

        /// <summary>
        /// 实际查询起始时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _startTime;

        /// <summary>
        /// 实际查询结束时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _endTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        #endregion

        #region 构造

        public UC_QueryDataReport()
        {
            InitializeComponent();

        }

        public UC_QueryDataReport(string groupId)
        {
            InitializeComponent();

            // 设置报表因子组
            QueryGroup queryGroup = ReportManager.GetInstance().GetQueryGroupByGroupIdOrFirst(groupId);
            if(queryGroup != null)
            {
                foreach(QueryNode queryNode in queryGroup.QueryNodes)
                {
                    this._queryNodes.Add(queryNode);
                }

                TitleName = queryGroup.name;
            }
        }

        #endregion

        #region 事件

        private void UC_QueryDataReport_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialTitle();
                InitialDateTimePicker();
                UpdateDateSelectorVisibility();
                UpdateCurrentPeriodLabel();
                SetDataGridViewHead();
            }
        }

        /// <summary>
        /// 时间周期选择变更事件
        /// </summary>
        private void rbPeriodType_CheckedChanged(object sender, EventArgs e)
        {
            var radioButton = sender as UIRadioButton;
            if(radioButton != null && radioButton.Checked)
            {
                // 根据单选按钮确定周期类型
                if(radioButton.Name == "rbDay")
                    _currentPeriodType = ePollutionReportPeriodType.Day;
                else if(radioButton.Name == "rbWeek")
                    _currentPeriodType = ePollutionReportPeriodType.Week;
                else if(radioButton.Name == "rbMonth")
                    _currentPeriodType = ePollutionReportPeriodType.Month;
                else if(radioButton.Name == "rbQuarter")
                    _currentPeriodType = ePollutionReportPeriodType.Quarter;
                else if(radioButton.Name == "rbYear")
                    _currentPeriodType = ePollutionReportPeriodType.Year;

                // 重新计算查询时间范围
                CalculateQueryTimeRange();

                UpdateDateSelectorVisibility();
                UpdateCurrentPeriodLabel();
            }
        }


        /// <summary>
        /// 时间选择变更事件
        /// </summary>
        private void dtpSelectedTime_ValueChanged(object sender, DateTime value)
        {
            // 更新选择的时间点
            _selectedTime = value;
            CalculateQueryTimeRange();
            UpdateCurrentPeriodLabel();
        }

        /// <summary>
        /// 点击查询按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void btnStartQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                // 清空界面数据
                dgvData.Rows.Clear();

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"查询{TitleName}", eOpType.浏览操作, eOpStyle.本地操作));

                // 根据当前周期类型计算查询时间范围
                CalculateQueryTimeRange();

                // 检查查询条件
                Check();

                // 刷新页面显示
                FlushView();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 报告导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if(dgvData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Path.Combine(Application.StartupPath, "query");
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName = Path.Combine(filePath, $"{TitleName}_{_selectedTime:yyyy-MM-dd HH-mm-ss}.xlsx");

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvData, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if(_startTime > _endTime)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            // 检查查询因子
            if(_queryNodes == null || _queryNodes.Count == 0)
            {
                throw new Exception("未配置查询因子！");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            try
            {
                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");
                // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                Thread.Sleep(50);

                // 清空原数据
                dgvData.Rows.Clear();

                // 根据报表类型生成数据
                if(_currentPeriodType == ePollutionReportPeriodType.Day)
                {
                    GenerateDayReport();
                }
                else
                {
                    GeneratePeriodReport();
                }

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        #region 初始化控件

        /// <summary>
        /// 设置表头
        /// </summary>
        private void InitialTitle()
        {
            this.Text = TitleName;
        }

        /// <summary>
        /// 初始化时间选择控件
        /// </summary>
        private void InitialDateTimePicker()
        {
            // 初始化选择时间点为今天
            _selectedTime = DateTime.Today;
            // 设置默认时间
            dtpSelectedTime.Value = _selectedTime;
            // 根据当前周期类型计算实际查询时间范围
            CalculateQueryTimeRange();
        }

        /// <summary>
        /// 更新日期选择器的可见性和类型
        /// </summary>
        private void UpdateDateSelectorVisibility()
        {
            // 根据当前周期类型设置时间选择控件的显示方式
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：显示日期选择器（年月日）
                    dtpSelectedTime.ShowType = UIDateType.YearMonthDay;
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：显示日期选择器（选择某一天，自动计算所在周）
                    dtpSelectedTime.ShowType = UIDateType.YearMonthDay;
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：显示年月选择器
                    dtpSelectedTime.ShowType = UIDateType.YearMonth;
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：显示年月选择器（选择某个月，自动计算所在季度）
                    dtpSelectedTime.ShowType = UIDateType.YearMonth;
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：显示年选择器
                    dtpSelectedTime.ShowType = UIDateType.Year;
                    break;
            }
        }

        /// <summary>
        /// 更新当前统计周期标签
        /// </summary>
        private void UpdateCurrentPeriodLabel()
        {
            // 根据具体的标签控件来显示周期信息
            lblCurrentPeriod.Text = GetPeriodDisplayText(_currentPeriodType, _selectedTime);
        }

        /// <summary>
        /// 根据选择的时间点和报表周期类型计算查询时间范围
        /// </summary>
        private void CalculateQueryTimeRange()
        {
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：查询选择日期当天的数据
                    _startTime = _selectedTime.Date;
                    _endTime = _startTime.AddDays(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：查询选择日期所在周的数据
                    var weekStart = _selectedTime.AddDays(-(int)_selectedTime.DayOfWeek);
                    _startTime = weekStart.Date;
                    _endTime = weekStart.AddDays(7).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：查询选择日期所在月的数据
                    var monthStart = new DateTime(_selectedTime.Year, _selectedTime.Month, 1);
                    _startTime = monthStart;
                    _endTime = monthStart.AddMonths(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：查询选择日期所在季度的数据
                    var quarterStart = GetQuarterStart(_selectedTime);
                    _startTime = quarterStart;
                    _endTime = quarterStart.AddMonths(3).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：查询选择日期所在年的数据
                    var yearStart = new DateTime(_selectedTime.Year, 1, 1);
                    _startTime = yearStart;
                    _endTime = yearStart.AddYears(1).AddSeconds(-1);
                    break;
            }
        }

        /// <summary>
        /// 根据报表类型获取对应的数据类型
        /// </summary>
        private int GetDataTypeByPeriod(ePollutionReportPeriodType periodType)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Day:
                    return (int)ePollutionDataType.小时数据; // 日报表查询小时数据
                default:
                    return (int)ePollutionDataType.日数据; // 其他报表查询日数据
            }
        }

        /// <summary>
        /// 获取季度开始时间
        /// </summary>
        private DateTime GetQuarterStart(DateTime date)
        {
            int quarter = (date.Month - 1) / 3 + 1;
            int startMonth = (quarter - 1) * 3 + 1;
            return new DateTime(date.Year, startMonth, 1);
        }

        /// <summary>
        /// 获取周期显示文本
        /// </summary>
        private string GetPeriodDisplayText(ePollutionReportPeriodType periodType, DateTime selectedTime)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Day:
                    return $"{selectedTime:yyyy年MM月dd日}";
                case ePollutionReportPeriodType.Week:
                    var week = new GregorianCalendar().GetWeekOfYear(selectedTime, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
                    return $"{selectedTime.Year}年第{week}周";
                case ePollutionReportPeriodType.Month:
                    return $"{selectedTime:yyyy年MM月}";
                case ePollutionReportPeriodType.Quarter:
                    int quarter = (selectedTime.Month - 1) / 3 + 1;
                    return $"{selectedTime.Year}年第{quarter}季度";
                case ePollutionReportPeriodType.Year:
                    return $"{selectedTime.Year}年";
                default:
                    return "";
            }
        }

        /// <summary>
        /// 生成日报表数据
        /// </summary>
        private void GenerateDayReport()
        {
            try
            {
                // 查询小时数据
                string strSql = $"select * from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={(int)ePollutionDataType.小时数据} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 存储所有小时数据用于统计计算
                var allHourData = new List<List<DataRow>>();

                // 生成24小时时段的报表
                for(int hour = 0; hour < 24; hour++)
                {
                    DateTime hourTime = _startTime.Date.AddHours(hour);
                    string timeLabel = $"{hour:D2}～{(hour + 1):D2}时";

                    // 获取该小时的数据
                    var hourData = dataTable.AsEnumerable()
                        .Where(row => DateTime.Parse(row["datatime"].ToString()).Hour == hour)
                        .ToList();

                    allHourData.Add(hourData);

                    // 创建行数据
                    List<object> rowValues = new List<object> { timeLabel };

                    // 添加因子数值（不是统计值）
                    foreach(var queryNode in _queryNodes)
                    {
                        var factorData = hourData.Where(row => row["factorcode"].ToString() == queryNode.id).FirstOrDefault();

                        if(factorData != null && double.TryParse(factorData["factorvalue"].ToString(), out double value))
                        {
                            rowValues.Add(value.ToString("F2"));
                        }
                        else
                        {
                            rowValues.Add("--");
                        }
                    }

                    // 添加小时流量数据
                    var flowData = hourData.Where(row => row["factorcode"].ToString() == DbConfig.TOTAL_FLOW).FirstOrDefault();
                    if(flowData != null && double.TryParse(flowData["factorvalue"].ToString(), out double flowValue))
                    {
                        rowValues.Add(flowValue.ToString("F2"));
                    }
                    else
                    {
                        rowValues.Add("--");
                    }

                    // 添加累计流量数据
                    var cumulativeFlowData = hourData.Where(row => row["factorcode"].ToString() == "e1204").FirstOrDefault();
                    if(cumulativeFlowData != null && double.TryParse(cumulativeFlowData["factorvalue"].ToString(), out double cumulativeValue))
                    {
                        rowValues.Add(cumulativeValue.ToString("F2"));
                    }
                    else
                    {
                        rowValues.Add("--");
                    }

                    // 添加到DataGridView
                    dgvData.Rows.Add(rowValues.ToArray());
                }

                // 添加统计行
                AddStatisticsRows(allHourData);
            }
            catch(Exception ex)
            {
                throw new Exception($"生成日报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 生成周期报表数据（周、月、季、年）
        /// </summary>
        private void GeneratePeriodReport()
        {
            try
            {
                // 查询日数据
                int dataType = (int)ePollutionDataType.日数据;
                string strSql = $"select * from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={dataType} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 按时间分组统计
                var timeGroups = GetTimeGroups(dataTable, _currentPeriodType);
                var allPeriodData = new List<List<DataRow>>();

                foreach(var timeGroup in timeGroups)
                {
                    allPeriodData.Add(timeGroup.Value);

                    List<object> rowValues = new List<object> { timeGroup.Key };

                    // 添加因子数值（取平均值作为代表值）
                    foreach(var queryNode in _queryNodes)
                    {
                        var factorData = timeGroup.Value.Where(row => row["factorcode"].ToString() == queryNode.id).ToList();

                        if(factorData.Any())
                        {
                            var validValues = factorData
                                .Where(row => row["factorvalue"] != DBNull.Value && !string.IsNullOrEmpty(row["factorvalue"].ToString()))
                                .Select(row => double.TryParse(row["factorvalue"].ToString(), out double val) ? (double?)val : null)
                                .Where(v => v.HasValue)
                                .Select(v => v.Value)
                                .ToList();

                            if(validValues.Any())
                            {
                                rowValues.Add(validValues.Average().ToString("F2"));
                            }
                            else
                            {
                                rowValues.Add("--");
                            }
                        }
                        else
                        {
                            rowValues.Add("--");
                        }
                    }

                    // 小时流量列在非日报表中不显示，但需要占位
                    // 这里不添加，因为列已经设置为不可见

                    // 添加累计流量数据（取最后一个有效值）
                    var cumulativeFlowData = timeGroup.Value.Where(row => row["factorcode"].ToString() == "e1204").ToList();
                    if(cumulativeFlowData.Any())
                    {
                        var lastValidValue = cumulativeFlowData
                            .Where(row => row["factorvalue"] != DBNull.Value && !string.IsNullOrEmpty(row["factorvalue"].ToString()))
                            .LastOrDefault();

                        if(lastValidValue != null && double.TryParse(lastValidValue["factorvalue"].ToString(), out double cumulativeValue))
                        {
                            rowValues.Add(cumulativeValue.ToString("F2"));
                        }
                        else
                        {
                            rowValues.Add("--");
                        }
                    }
                    else
                    {
                        rowValues.Add("--");
                    }

                    // 添加到DataGridView
                    dgvData.Rows.Add(rowValues.ToArray());
                }

                // 添加统计行
                AddStatisticsRows(allPeriodData);
            }
            catch(Exception ex)
            {
                throw new Exception($"生成周期报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 统计数据结构
        /// </summary>
        private struct StatisticsResult
        {
            public double Max { get; set; }
            public double Min { get; set; }
            public double Average { get; set; }
            public double Total { get; set; }
        }

        /// <summary>
        /// 计算统计值
        /// </summary>
        private StatisticsResult CalculateStatistics(List<DataRow> factorData, string factorName)
        {
            var result = new StatisticsResult();

            if(factorData == null || factorData.Count == 0)
            {
                return result;
            }

            var validValues = factorData
                .Where(row => row["factorvalue"] != DBNull.Value && !string.IsNullOrEmpty(row["factorvalue"].ToString()))
                .Select(row =>
                {
                    if(double.TryParse(row["factorvalue"].ToString(), out double value))
                        return (double?)value;
                    return null;
                })
                .Where(v => v.HasValue)
                .Select(v => v.Value)
                .ToList();

            if(validValues.Count > 0)
            {
                result.Max = validValues.Max();
                result.Min = validValues.Min();
                result.Average = validValues.Average();

                // 累计流量因子的总量取最后一个有效值
                if(IsCumulativeFlowFactor(factorName))
                {
                    result.Total = validValues.LastOrDefault();
                }
                else if(!IsPhFactor(factorName))
                {
                    result.Total = validValues.Sum();
                }
            }

            return result;
        }

        /// <summary>
        /// 判断是否为pH因子
        /// </summary>
        private bool IsPhFactor(string factorName)
        {
            return factorName != null && factorName.ToLower().Contains("ph");
        }

        /// <summary>
        /// 判断是否为累计流量因子
        /// </summary>
        private bool IsCumulativeFlowFactor(string factorName)
        {
            return factorName != null && factorName.Contains("累计流量");
        }

        /// <summary>
        /// 根据周期类型获取时间分组
        /// </summary>
        private Dictionary<string, List<DataRow>> GetTimeGroups(DataTable dataTable, ePollutionReportPeriodType periodType)
        {
            var groups = new Dictionary<string, List<DataRow>>();

            foreach(DataRow row in dataTable.Rows)
            {
                DateTime dataTime = DateTime.Parse(row["datatime"].ToString());
                string groupKey = GetTimeGroupKey(dataTime, periodType);

                if(!groups.ContainsKey(groupKey))
                {
                    groups[groupKey] = new List<DataRow>();
                }
                groups[groupKey].Add(row);
            }

            return groups;
        }

        /// <summary>
        /// 获取时间分组键
        /// </summary>
        private string GetTimeGroupKey(DateTime dateTime, ePollutionReportPeriodType periodType)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Week:
                    var weekStart = dateTime.AddDays(-(int)dateTime.DayOfWeek);
                    return $"{weekStart:yyyy年MM月dd日} 至 {weekStart.AddDays(6):yyyy年MM月dd日}";
                case ePollutionReportPeriodType.Month:
                    return $"{dateTime:yyyy年MM月}";
                case ePollutionReportPeriodType.Quarter:
                    int quarter = (dateTime.Month - 1) / 3 + 1;
                    return $"{dateTime.Year}年第{quarter}季度";
                case ePollutionReportPeriodType.Year:
                    return $"{dateTime.Year}年";
                default:
                    return dateTime.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// 设置DataGridView表头
        /// </summary>
        private void SetDataGridViewHead()
        {
            try
            {
                // 如果列已存在，只需要调整可见性，不重新创建
                if(dgvData.Columns.Count > 0)
                {
                    UpdateColumnVisibility();
                    return;
                }

                // 首次创建列结构
                CreateDataGridViewColumns();

                // 设置列的可见性
                UpdateColumnVisibility();
            }
            catch(Exception ex)
            {
                throw new Exception($"设置表头失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建DataGridView列结构（只在首次调用时执行）
        /// </summary>
        private void CreateDataGridViewColumns()
        {
            // 清除现有列
            dgvData.Columns.Clear();

            // 添加时间列
            dgvData.Columns.Add("Time", "时间");

            // 添加统计因子列（只显示因子数值，不显示统计信息）
            foreach(var queryNode in _queryNodes)
            {
                string factorName = queryNode.name;
                string unit = queryNode.UnitId ?? "";

                // 只添加因子数值列
                dgvData.Columns.Add($"Factor_{queryNode.id}", $"{factorName}({unit})");
            }

            // 添加小时流量列（倒数第二列）
            dgvData.Columns.Add("HourFlow", "小时流量(m³)");

            // 添加累计流量列（最后一列）
            dgvData.Columns.Add("CumulativeFlow", "累计流量(m³)");

            // 设置列宽和样式
            foreach(DataGridViewColumn column in dgvData.Columns)
            {
                column.Width = 120;
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }

            // 时间列稍宽一些
            if(dgvData.Columns.Count > 0)
            {
                dgvData.Columns[0].Width = 150;
            }
        }

        /// <summary>
        /// 更新列的可见性
        /// </summary>
        private void UpdateColumnVisibility()
        {
            // 小时流量列只在日报表模式下显示
            if(dgvData.Columns.Contains("HourFlow"))
            {
                dgvData.Columns["HourFlow"].Visible = (_currentPeriodType == ePollutionReportPeriodType.Day);
            }

            // 累计流量列在所有模式下都显示
            if(dgvData.Columns.Contains("CumulativeFlow"))
            {
                dgvData.Columns["CumulativeFlow"].Visible = true;
            }
        }

        /// <summary>
        /// 添加统计行到表格底部
        /// </summary>
        private void AddStatisticsRows(List<List<DataRow>> allData)
        {
            try
            {
                // 合并所有数据用于统计计算
                var allRows = allData.SelectMany(x => x).ToList();

                // 添加统计行：有效日均值、最大值、最小值、总量
                AddStatisticRow("有效日均值", allRows, StatisticType.Average);
                AddStatisticRow("最大值", allRows, StatisticType.Max);
                AddStatisticRow("最小值", allRows, StatisticType.Min);
                AddStatisticRow("总量", allRows, StatisticType.Total);
            }
            catch(Exception ex)
            {
                // 统计行添加失败不影响主要数据显示
                System.Diagnostics.Debug.WriteLine($"添加统计行失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 统计类型枚举
        /// </summary>
        private enum StatisticType
        {
            Average,
            Max,
            Min,
            Total
        }

        /// <summary>
        /// 添加单个统计行
        /// </summary>
        private void AddStatisticRow(string statisticName, List<DataRow> allData, StatisticType statisticType)
        {
            List<object> rowValues = new List<object> { statisticName };

            // 为每个因子计算统计值
            foreach(var queryNode in _queryNodes)
            {
                var factorData = allData.Where(row => row["factorcode"].ToString() == queryNode.id).ToList();

                if(factorData.Any())
                {
                    var validValues = factorData
                        .Where(row => row["factorvalue"] != DBNull.Value && !string.IsNullOrEmpty(row["factorvalue"].ToString()))
                        .Select(row => double.TryParse(row["factorvalue"].ToString(), out double val) ? (double?)val : null)
                        .Where(v => v.HasValue)
                        .Select(v => v.Value)
                        .ToList();

                    if(validValues.Any())
                    {
                        double result = 0;
                        switch(statisticType)
                        {
                            case StatisticType.Average:
                                result = validValues.Average();
                                break;
                            case StatisticType.Max:
                                result = validValues.Max();
                                break;
                            case StatisticType.Min:
                                result = validValues.Min();
                                break;
                            case StatisticType.Total:
                                // pH因子不计算总量
                                if(IsPhFactor(queryNode.name))
                                {
                                    rowValues.Add("--");
                                    continue;
                                }
                                // 累计流量因子的总量取最后一个有效值
                                else if(IsCumulativeFlowFactor(queryNode.name))
                                {
                                    result = validValues.LastOrDefault();
                                }
                                else
                                {
                                    result = validValues.Sum();
                                }
                                break;
                        }
                        rowValues.Add(result.ToString("F2"));
                    }
                    else
                    {
                        rowValues.Add("--");
                    }
                }
                else
                {
                    rowValues.Add("--");
                }
            }

            // 小时流量列（只在日报表显示）
            if(_currentPeriodType == ePollutionReportPeriodType.Day)
            {
                var flowData = allData.Where(row => row["factorcode"].ToString() == DbConfig.TOTAL_FLOW).ToList();
                if(flowData.Any() && statisticType != StatisticType.Total)
                {
                    var validFlowValues = flowData
                        .Where(row => row["factorvalue"] != DBNull.Value && !string.IsNullOrEmpty(row["factorvalue"].ToString()))
                        .Select(row => double.TryParse(row["factorvalue"].ToString(), out double val) ? (double?)val : null)
                        .Where(v => v.HasValue)
                        .Select(v => v.Value)
                        .ToList();

                    if(validFlowValues.Any())
                    {
                        double flowResult = 0;
                        switch(statisticType)
                        {
                            case StatisticType.Average:
                                flowResult = validFlowValues.Average();
                                break;
                            case StatisticType.Max:
                                flowResult = validFlowValues.Max();
                                break;
                            case StatisticType.Min:
                                flowResult = validFlowValues.Min();
                                break;
                            case StatisticType.Total:
                                flowResult = validFlowValues.Sum();
                                break;
                        }
                        rowValues.Add(flowResult.ToString("F2"));
                    }
                    else
                    {
                        rowValues.Add("--");
                    }
                }
                else
                {
                    rowValues.Add("--");
                }
            }

            // 累计流量列
            var cumulativeFlowData = allData.Where(row => row["factorcode"].ToString() == "e1204").ToList();
            if(cumulativeFlowData.Any())
            {
                var validCumulativeValues = cumulativeFlowData
                    .Where(row => row["factorvalue"] != DBNull.Value && !string.IsNullOrEmpty(row["factorvalue"].ToString()))
                    .Select(row => double.TryParse(row["factorvalue"].ToString(), out double val) ? (double?)val : null)
                    .Where(v => v.HasValue)
                    .Select(v => v.Value)
                    .ToList();

                if(validCumulativeValues.Any())
                {
                    double cumulativeResult = 0;
                    switch(statisticType)
                    {
                        case StatisticType.Average:
                            cumulativeResult = validCumulativeValues.Average();
                            break;
                        case StatisticType.Max:
                            cumulativeResult = validCumulativeValues.Max();
                            break;
                        case StatisticType.Min:
                            cumulativeResult = validCumulativeValues.Min();
                            break;
                        case StatisticType.Total:
                            // 累计流量的总量取最后一个有效值
                            cumulativeResult = validCumulativeValues.LastOrDefault();
                            break;
                    }
                    rowValues.Add(cumulativeResult.ToString("F2"));
                }
                else
                {
                    rowValues.Add("--");
                }
            }
            else
            {
                rowValues.Add("--");
            }

            // 添加统计行到表格
            dgvData.Rows.Add(rowValues.ToArray());
        }

        #endregion

        #endregion
    }
}