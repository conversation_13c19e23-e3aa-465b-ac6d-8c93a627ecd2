﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Fpi.Json;
using Fpi.Util.Reflection;
using Fpi.WMS3000.SystemConfig.SmartPatrol;
using Newtonsoft.Json;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 图像巡检管理类
    /// 巡检触发，过程控制，结果保存
    /// </summary>
    public class ImagePatrolManager : BaseJsonNode
    {
        #region 字段属性

        #region 配置存储

        /// <summary>
        /// 当前启用的巡检项类型列表
        /// </summary>
        [Description("当前启用的巡检项类型列表")]
        public List<string> UsedPatrolUnitTypeList = new List<string>();

        #endregion

        /// <summary>
        /// 巡检单元列表
        /// </summary>
        [Description("巡检单元列表")]
        [JsonIgnore]
        public List<ImageUnitSmartPatrolBase> PatrolUnitList = new List<ImageUnitSmartPatrolBase>();

        /// <summary>
        /// 当前正在运行的单元的序号
        /// </summary>
        [Description("当前正在运行的单元的序号")]
        [JsonIgnore]
        public int CurrnetUnitIndex;

        /// <summary>
        /// 最新巡检结果
        /// </summary>
        [Description("最新巡检结果")]
        public ImagePatrolResult LatestImagePatrolResult;

        /// <summary>
        /// 巡检状态
        /// </summary>
        [Description("巡检状态")]
        [JsonIgnore]
        public ePatrolState PatrolState;

        /// <summary>
        /// 任务取消Token
        /// </summary>
        [JsonIgnore]
        public CancellationTokenSource CancelToken;

        #endregion

        #region 单例

        private ImagePatrolManager()
        {
            // 巡检执行时打印日志
            PatrolLogEvent += ImagePatrolLogHelper.WritePatrolLog;

            loadJson();

            // 构建巡检项列表
            RebuildPatrolUnitList();
        }

        [JsonIgnore]
        private static readonly object lockObj = new object();

        [JsonIgnore]
        private static ImagePatrolManager _instance = null;

        public static ImagePatrolManager GetInstance()
        {
            lock(lockObj)
            {
                if(_instance == null)
                {
                    _instance = new ImagePatrolManager();
                }
            }

            return _instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 巡检状态锁
        /// </summary>
        [JsonIgnore]
        private static readonly object SyncObj = new object();

        /// <summary>
        /// 开始巡检流程（异步）
        /// </summary>
        public Task StartDoPatrol(eImagePatrolTriggerType patrolTriggerType)
        {
            lock(SyncObj)
            {
                // 当前巡检中，拒绝执行新任务
                if(PatrolState == ePatrolState.巡检中)
                {
                    return null;
                }
            }

            // 创建CancellationTokenSource对象。
            CancelToken = new CancellationTokenSource();

            // 异步执行巡检流程
            return Task.Run(() =>
            {
                try
                {
                    // 新建巡检结果信息
                    LatestImagePatrolResult = new ImagePatrolResult(patrolTriggerType);

                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检中;
                    }

                    // 巡检开始
                    OnPatrolStartEvent();

                    // 遍历执行巡检
                    CurrnetUnitIndex = 0;
                    foreach(var model in PatrolUnitList)
                    {
                        // 判断流程有没有被手动取消
                        if(CancelToken.IsCancellationRequested)
                        {
                            throw new Exception();
                        }

                        // 巡检进度更新
                        CurrnetUnitIndex++;

                        // 巡检开始通知
                        OnSingleUnitPatrolStart(model, CurrnetUnitIndex);

                        // 执行巡检
                        model.StartPatrol();

                        // 模块巡检结果汇总到总结果区
                        if(model.PatrolResult != null)
                        {
                            // 巡检结果情况赋值
                            model.PatrolResult.PatrolState = model.PatrolState;
                            LatestImagePatrolResult.AddModelResult(model.PatrolResult);
                        }

                        // 巡检结束通知
                        OnSingleUnitPatrolEnd(model, CurrnetUnitIndex);
                    }

                    // 保存巡检结果信息到数据库
                    LatestImagePatrolResult.SaveToDb();

                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检完成;
                    }
                }
                catch(OperationCanceledException e)
                {
                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检异常;
                    }
                    ImagePatrolLogHelper.WritePatrolLog($"图像巡检执行出错：触发中断巡检流程！");
                }
                catch(Exception e)
                {
                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检异常;
                    }
                    ImagePatrolLogHelper.WritePatrolLog($"图像巡检执行出错：{e.Message}");
                }
                finally
                {
                    // 巡检结束
                    OnPatrolEndEvent();
                }
            });
        }

        /// <summary>
        /// 中断巡检流程
        /// </summary>
        public void StopDoPatrol()
        {
            if(PatrolState == ePatrolState.巡检中)
            {
                CancelToken?.Cancel();
            }
        }

        /// <summary>
        /// 构建巡检单元列表
        /// </summary>
        public void RebuildPatrolUnitList()
        {
            PatrolUnitList.Clear();

            foreach(string type in UsedPatrolUnitTypeList.Distinct())
            {
                try
                {
                    if(!string.IsNullOrEmpty(type))
                    {
                        PatrolUnitList.Add((ImageUnitSmartPatrolBase)ReflectionHelper.CreateInstance(type));
                    }
                }
                catch
                {
                }
            }
        }

        #endregion

        #region Event

        #region 整体

        /// <summary>
        /// 巡检开始
        /// </summary>
        public event Action PatrolStartEvent;

        /// <summary>
        /// 巡检结束
        /// </summary>
        public event Action PatrolEndEvent;

        /// <summary>
        /// 巡检日志
        /// </summary>
        public event Action<string> PatrolLogEvent;

        /// <summary>
        /// 巡检开始
        /// </summary>
        /// <param name="model"></param>
        private void OnPatrolStartEvent()
        {
            try
            {
                if(PatrolStartEvent != null)
                {
                    PatrolStartEvent();
                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 巡检结束
        /// </summary>
        /// <param name="model"></param>
        private void OnPatrolEndEvent()
        {
            try
            {
                if(PatrolEndEvent != null)
                {
                    PatrolEndEvent();
                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 巡检日志
        /// </summary>
        /// <param name="model"></param>
        public void OnPatrolLogEvent(string log)
        {
            try
            {
                if(PatrolLogEvent != null)
                {
                    PatrolLogEvent(log);
                }
            }
            catch
            {
            }
        }

        #endregion

        #region 单元

        /// <summary>
        /// 某一单元巡检开始
        /// </summary>
        public event Action<ImageUnitSmartPatrolBase, int> SingleUnitPatrolStartEvent;

        /// <summary>
        /// 某一单元巡检结束
        /// </summary>
        public event Action<ImageUnitSmartPatrolBase, int> SingleUnitPatrolEndEvent;

        /// <summary>
        /// 某一单元巡检开始
        /// </summary>
        /// <param name="model"></param>
        private void OnSingleUnitPatrolStart(ImageUnitSmartPatrolBase model, int index)
        {
            try
            {
                if(SingleUnitPatrolStartEvent != null)
                {
                    SingleUnitPatrolStartEvent(model, index);
                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 某一单元巡检结束
        /// </summary>
        /// <param name="model"></param>
        private void OnSingleUnitPatrolEnd(ImageUnitSmartPatrolBase model, int index)
        {
            try
            {
                if(SingleUnitPatrolEndEvent != null)
                {
                    SingleUnitPatrolEndEvent(model, index);
                }
            }
            catch
            {
            }
        }

        #endregion

        /// <summary>
        /// 清空流程状态事件通知
        /// </summary>
        public void ClearEvent()
        {
            if(PatrolStartEvent != null)
            {
                Delegate[] delegAry = PatrolStartEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action)@delegate;
                    PatrolStartEvent -= deleg;
                }
            }

            if(PatrolEndEvent != null)
            {
                Delegate[] delegAry = PatrolEndEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action)@delegate;
                    PatrolEndEvent -= deleg;
                }
            }

            if(PatrolLogEvent != null)
            {
                Delegate[] delegAry = PatrolLogEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action<string>)@delegate;
                    PatrolLogEvent -= deleg;
                }
            }

            if(SingleUnitPatrolStartEvent != null)
            {
                Delegate[] delegAry = SingleUnitPatrolStartEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action<ImageUnitSmartPatrolBase, int>)@delegate;
                    SingleUnitPatrolStartEvent -= deleg;
                }
            }

            if(SingleUnitPatrolEndEvent != null)
            {
                Delegate[] delegAry = SingleUnitPatrolEndEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action<ImageUnitSmartPatrolBase, int>)@delegate;
                    SingleUnitPatrolEndEvent -= deleg;
                }
            }
        }

        #endregion
    }
}