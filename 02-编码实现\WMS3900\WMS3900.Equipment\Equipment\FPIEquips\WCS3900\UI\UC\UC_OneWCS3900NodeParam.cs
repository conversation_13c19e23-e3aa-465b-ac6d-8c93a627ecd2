﻿using System;
using System.Collections.Generic;
using Fpi.WMS3000.Equipment.WCS3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// 单个因子公共寄存器信息区
    /// </summary>
    public partial class UC_OneWCS3900NodeParam : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应设备
        /// </summary>
        private WCS3900Equip _device;

        /// <summary>
        /// 因子类型
        /// </summary>
        private eWCSNodeType _eWCSNodeType;

        /// <summary>
        /// 器件状态显示控件
        /// </summary>
        private List<UC_OneWCS3900ElementState> _elementStateList = new List<UC_OneWCS3900ElementState>();

        /// <summary>
        /// 液位状态显示控件
        /// </summary>
        private List<UC_OneWCS3900LevelState> _levelStateList = new List<UC_OneWCS3900LevelState>();

        /// <summary>
        /// 试剂信息显示控件
        /// </summary>
        private List<UC_OneWMS3900ReagentInfo> _reagentInfoList = new List<UC_OneWMS3900ReagentInfo>();

        private const string ErrorInfo = "— — —";

        #endregion

        #region 构造

        public UC_OneWCS3900NodeParam()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(WCS3900Equip device, eWCSNodeType eWCSNodeType)
        {
            //gbStateInfo.Text = $"{EnumOperate.GetEnumDesc(eWCSNodeType)}公共区信息";
            _device = device;
            _eWCSNodeType = eWCSNodeType;
            InitUI();
            RefreshUI();
        }

        internal void RefreshUI()
        {
            if(_device != null)
            {
                OneCommonParam oneCommon = _device.CommonParam.CommonParams[_eWCSNodeType];
                lblTotalStepCount.Text = oneCommon.TotalStepCount == -1 ? ErrorInfo : oneCommon.TotalStepCount.ToString();
                lblCurrentStep.Text = oneCommon.CurrentStep == -1 ? ErrorInfo : oneCommon.CurrentStep.ToString();
                if(oneCommon.TotalStepCount != -1)
                {
                    proBarFlow.Maximum = oneCommon.TotalStepCount;
                }
                if(oneCommon.CurrentStep != -1)
                {
                    proBarFlow.Value = oneCommon.CurrentStep;
                }

                lblElectrodeChangeTime.Text = oneCommon.ElectrodeReplaceDays == DateTime.MinValue ?
                   ErrorInfo : oneCommon.ElectrodeReplaceDays.ToString("yyyy-MM-dd");
                lblElectrodeLongstTime.Text = oneCommon.ElectrodeLogestUseTime == DateTime.MinValue ?
                    ErrorInfo : oneCommon.ElectrodeLogestUseTime.ToString("yyyy-MM-dd");

                foreach(var item in _reagentInfoList)
                {
                    item.RefreshUI();
                }

                foreach(var uc in _elementStateList)
                {
                    uc.RefreshUI();
                }
                foreach(var uc in _levelStateList)
                {
                    uc.RefreshUI();
                }
            }
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        public void InitUI()
        {
            if(_device != null)
            {
                pnlElementControl.Controls.Clear();

                foreach(eWCS3900ElementType elementType in Enum.GetValues(typeof(eWCS3900ElementType)))
                {
                    var uc = new UC_OneWCS3900ElementState(_device, _eWCSNodeType, elementType);
                    uc.Width = 500;
                    uc.Margin = new System.Windows.Forms.Padding(10, 0, 0, 0);
                    _elementStateList.Add(uc);
                    pnlElementControl.Controls.Add(uc);
                }

                pnlLevelState.Controls.Clear();

                foreach(eWCS3900LevelType levelType in Enum.GetValues(typeof(eWCS3900LevelType)))
                {
                    var uc = new UC_OneWCS3900LevelState(_device, _eWCSNodeType, levelType);
                    uc.Width = 450;
                    uc.Margin = new System.Windows.Forms.Padding(10, 0, 0, 0);
                    _levelStateList.Add(uc);
                    pnlLevelState.Controls.Add(uc);
                }

                _reagentInfoList.Clear();

                _reagentInfoList.Add(uC_ReagentInfo1);

                if(_eWCSNodeType == eWCSNodeType.w01009)
                {
                    uC_ReagentInfo2.Visible = false;
                    uC_ReagentInfo3.Visible = false;
                }
                else
                {
                    _reagentInfoList.Add(uC_ReagentInfo2);
                    _reagentInfoList.Add(uC_ReagentInfo3);
                }

                for(int i = 0; i < _reagentInfoList.Count; i++)
                {
                    // 溶解氧只绑定后两个试剂类型
                    if(_eWCSNodeType == eWCSNodeType.w01009)
                    {
                        _reagentInfoList[i].BindDataToDevice(_eWCSNodeType, (eStandardSolutionType)(i + 4), _device);
                    }
                    else
                    {
                        _reagentInfoList[i].BindDataToDevice(_eWCSNodeType, (eStandardSolutionType)(i + 1), _device);
                    }
                }
            }
        }
        #endregion
    }
}