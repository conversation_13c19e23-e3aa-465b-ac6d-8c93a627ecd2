﻿using Fpi.Camera;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 视频监控单元巡检执行类
    /// </summary>
    public class SPUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public SPUnitSmartPatrol()
        {
            UnitId = "VideoMonitoring";
            UnitName = "视频监控单元";
            Description = "水站版";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            SPUnitPatrolResult result = new SPUnitPatrolResult();

            result.WaterPointCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.WaterPointCamera);
            result.StationOutsideCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.StationOutsideCamera);
            result.StationInsideCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.StationInsideCamera);

            if(GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData != null)
            {
                result.StationOutsideIntrusion = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapIntrusion;
                result.UndressedSuit = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.UndressedSuit;
                result.Smoking = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.Smoking;
                result.MaterialStacking = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.MaterialStacking;
            }
            result.SandSinkCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.SandSinkCamera);
            result.PipeCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.PipeCamera);
            result.FiveParamFlowPoolCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamFlowPoolCamera);
            result.FiveParamBucketCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamBucketCamera);
            result.CodMnNH4EquipCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4EquipCamera);
            result.CodMnNH4QCDCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4QCDCamera);
            result.TPTNEquipCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.TPTNEquipCamera);
            result.TPTNQCDCameraComState = GetCameraConnectState(ExterEquipConfigManager.GetInstance().CameraSelect.TPTNQCDCamera);

            return result;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取摄像机通信状态
        /// </summary>
        /// <param name="camera"></param>
        /// <returns></returns>
        private eModuleWorkingState GetCameraConnectState(BaseNETCamera camera)
        {
            return camera != null && camera.IsConnect() ? eModuleWorkingState.正常 : eModuleWorkingState.异常;
        }

        #endregion
    }
}