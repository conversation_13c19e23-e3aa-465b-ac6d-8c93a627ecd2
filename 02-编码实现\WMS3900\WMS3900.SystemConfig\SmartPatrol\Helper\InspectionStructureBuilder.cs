﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Config;
using HZH_Controls;
using Newtonsoft.Json;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Helper
{
    /// <summary>
    /// 巡检报表数据构造帮助类
    /// </summary>
    public static class InspectionStructureBuilder
    {
        #region 公共方法

        /// <summary>
        /// 构建报表显示所用三层结构数据
        /// </summary>
        /// <returns>构建好的巡检层级结构</returns>
        public static InspectionHierarchy BuildPatrolReportData(SmartPatrolResult smartPatrolResult)
        {
            var hierarchy = new InspectionHierarchy();

            // 遍历各巡检单元结果
            foreach(var model in smartPatrolResult.ModelResultList)
            {
                // 创建第一层单元层
                var layer = new OneUnitLayer
                {
                    UnitId = model.UnitId,
                    UnitName = model.UnitName
                };

                Type type = model.GetType();
                PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance).Where(a => a.GetCustomAttribute<JsonIgnoreAttribute>() == null).ToArray();

                // 遍历巡检单元的各属性
                foreach(PropertyInfo property in properties)
                {
                    // 获取Description特性
                    var description = property.GetCustomAttribute<DescriptionAttribute>()?.Description ?? property.Name;

                    // 特定类型属性，不显示
                    // 巡检单元编号、名称、巡检结果、巡检得分
                    if(property.Name == "UnitId" || property.Name == "UnitName" || property.Name == "PatrolResult" || property.Name == "PatrolScore")
                    {
                        continue;
                    }

                    try
                    {
                        // 获取属性值
                        object value = property.GetValue(model);

                        // 处理列表属性
                        if(value != null && IsList(value))
                        {
                            if(value is not IEnumerable enumerable)
                            {
                                break;
                            }

                            // 若是 List<string>集合
                            if(value is List<string> strs)
                            {
                                TwoPropertyNode secondNode = new TwoPropertyNode
                                {
                                    PropertyName = "-"
                                };
                                List<string> values = new List<string>();
                                secondNode.LeafProperties = new List<ThreeLeafProperty>()
                                {
                                    new ThreeLeafProperty()
                                    {
                                        Name = description,
                                        Value = string.Join(Environment.NewLine,strs)
                                    }
                                };
                                layer.SecondLayer.Add(secondNode);
                            }
                            else
                            {
                                // 遍历列表，对其中每个值做处理
                                foreach(var item in enumerable)
                                {
                                    // 构建第二层节点
                                    TwoPropertyNode secondNode = new TwoPropertyNode();
                                    if(item is SubModelResultBase subModel)
                                    {
                                        secondNode.PropertyName = subModel.ModelName;
                                        secondNode.StatusDescription = subModel.PatrolResult.ToString();
                                    }
                                    else if(item is ReagentUsageState reagent)
                                    {
                                        secondNode.PropertyName = reagent.ReagentName;
                                        secondNode.StatusDescription = "-";
                                    }
                                    else
                                    {
                                        secondNode.PropertyName = property.Name;
                                        secondNode.StatusDescription = "-";
                                        secondNode.PropertyType = property.PropertyType.ToString();
                                    }
                                    secondNode.LeafProperties = GetLeafPropertiesFromComplexType(item);

                                    layer.SecondLayer.Add(secondNode);
                                }

                            }
                        }
                        // 处理复杂对象
                        else if(value != null && IsComplexType(property.PropertyType))
                        {
                            // 构建第二层节点
                            TwoPropertyNode secondNode = new TwoPropertyNode()
                            {      // 取其属性作为第三层节点。此时默认其属性都是简单对象
                                LeafProperties = GetLeafPropertiesFromComplexType(value)
                            };
                            if(value is SubModelResultBase subModel)
                            {
                                secondNode.PropertyName = subModel.ModelName;
                                secondNode.StatusDescription = subModel.PatrolResult.ToString();
                            }
                            else
                            {
                                secondNode.PropertyName = description;
                                secondNode.StatusDescription = "-";
                            }
                            layer.SecondLayer.Add(secondNode);
                        }
                        // 处理简单对象
                        else
                        {
                            TwoPropertyNode secondNode = new();
                            if(model is FZUnitPatrolResult or SJUnitPatrolResult)
                            {
                                // 构建第二层节点
                                secondNode.PropertyName = description;
                                secondNode.StatusDescription = "-";
                                // 简单对象不持有子属性。创建一个三级节点承载对象值
                                secondNode.LeafProperties = new List<ThreeLeafProperty>() { new ThreeLeafProperty() { Name = "-", Value = GetLeafPropertyString(value) } };
                            }
                            else
                            {
                                // 构建第二层节点
                                secondNode.PropertyName = "-";
                                // 简单对象不持有子属性。创建一个三级节点承载对象值
                                secondNode.LeafProperties = new List<ThreeLeafProperty>() { new ThreeLeafProperty() { Name = description, Value = GetLeafPropertyString(value) } };
                            }
                            layer.SecondLayer.Add(secondNode);
                        }

                        layer.SecondLayer = MergeByName(layer.SecondLayer, model);
                    }
                    catch
                    {
                    }
                }

                hierarchy.FirstLayer.Add(layer);
            }
            return hierarchy;
        }

        /// <summary>
        /// 获取巡检异常结果
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static string GetPatrolErrorResult(SmartPatrolResult smartPatrolResult)
        {
            var output = new StringBuilder();
            // 遍历各巡检单元
            foreach(var model in smartPatrolResult.ModelResultList)
            {
                // 获取单个模块巡检异常结果
                var resultStr = GetSingleUnitErrorResultStr(model);
                // 如果有异常结果，则添加到输出
                if(!string.IsNullOrEmpty(resultStr))
                {
                    output.AppendLine(resultStr);
                    // 不同单元间换行
                    output.AppendLine();
                }
            }
            return output.ToString().TrimEnd();
        }

        /// <summary>
        /// 获取单个模块巡检异常结果
        /// </summary>
        /// <param name="obj">模块数据对象</param>
        /// <param name="unitName">模块/子模块名称</param>
        /// <param name="isSubT">是否是顶级节点</param>
        public static string GetSingleUnitErrorResultStr(SingleUnitPatrolResultBase singleUnitPatrolResult)
        {
            if(singleUnitPatrolResult == null)
            {
                return string.Empty;
            }

            var instanceResult = GetSingleUnitErrorResult(singleUnitPatrolResult);

            var output = new StringBuilder();

            if(instanceResult != null && instanceResult.Properties.Count > 0)
            {
                output.AppendLine(FormatInstanceOutput(instanceResult));
            }
            return output.ToString().TrimEnd();
        }

        #endregion

        #region 私有方法

        #region 巡检报表显示

        /// <summary>
        /// 通过虚拟二层节点名称合并虚拟第二层节点
        /// </summary>
        /// <param name="nodes"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private static List<TwoPropertyNode> MergeByName(List<TwoPropertyNode> nodes, SingleUnitPatrolResultBase model)
        {
            // 1. 筛选所有 Name 为 "-" 的项
            var minusNodes = nodes.Where(x => x.PropertyName == "-").ToList();
            var otherNodes = nodes.Where(x => x.PropertyName != "-").ToList();

            // 2. 如果存在 Name 为 "-" 的项，合并它们
            var mergedMinusNode = new TwoPropertyNode
            {
                PropertyName = "-",
                StatusDescription = "-",
                LeafProperties = minusNodes
                    .SelectMany(n => n.LeafProperties)                 // 扁平化所有 Items
                    .Distinct()                               // 去重（如果对象支持比较）
                    .ToList()
            };

            // 3. 构造最终结果：保留非 "-" 项 + 合并后的 "-" 项（如果有的话）
            var result = otherNodes.ToList();
            if(minusNodes.Any())
            {
                result.Add(mergedMinusNode);
            }

            // 合并后若只有一子项，将基类状态赋给子项状态
            if(result.Count == 1 && model is not FZUnitPatrolResult)
            {
                result.FirstOrDefault().StatusDescription = model.PatrolResult.ToString();
            }
            return result;
        }

        /// <summary>
        /// 从复杂对象中取子属性作为三级节点
        /// 只取基本属性
        /// </summary>
        /// <returns></returns>
        private static List<ThreeLeafProperty> GetLeafPropertiesFromComplexType(object obj)
        {
            // 三级节点
            List<ThreeLeafProperty> resultList = new List<ThreeLeafProperty>();

            if(obj == null)
            {
                return resultList;
            }

            Type type = obj.GetType();
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance).Where(a => a.GetCustomAttribute<JsonIgnoreAttribute>() == null).ToArray();

            // 遍历复杂对象的各属性
            foreach(PropertyInfo property in properties)
            {
                try
                {
                    // 特定类型属性，不显示
                    // 子单元名称、巡检结果
                    // 试剂信息中试剂名
                    if(property.Name == "ModelName" || property.Name == "PatrolResult" || property.Name == "ReagentName")
                    {
                        continue;
                    }

                    // 获取Description特性描述
                    var description = property.GetCustomAttribute<DescriptionAttribute>()?.Description ?? property.Name;

                    // 获取属性值
                    object value = property.GetValue(obj);

                    // 处理列表属性
                    if(value != null && IsList(value))
                    {
                        // 如果列表子项为string等简单对象，则视作一条结果
                        List<string> strList = new List<string>();

                        if(value is not IEnumerable enumerable)
                        {
                            break;
                        }

                        foreach(var item in enumerable)
                        {
                            // 只处理三级列表中简单类型对象
                            if(item != null && !IsComplexType(item.GetType()))
                            {
                                var info = GetLeafPropertyString(item);
                                strList.Add(info);
                            }
                        }

                        ThreeLeafProperty leafProperty = new ThreeLeafProperty
                        {
                            Name = description,
                            Value = string.Join(Environment.NewLine, strList),
                            Description = string.Join(Environment.NewLine, strList)
                        };
                        resultList.Add(leafProperty);
                    }
                    // 处理复杂属性
                    else if(IsComplexType(property.PropertyType))
                    {
                        string sb = "";
                        if(value is KeyDeviceUsageState usageState)
                        {
                            sb = usageState.GetResultStr();
                        }
                        else if(value is ElectrodeUsageStatistics electrode)
                        {
                            sb = electrode.GetResultStr();
                        }
                        else if(value is ElectrodeKeyParams keyParams)
                        {
                            sb = keyParams.GetResultStr();
                        }
                        else
                        {
                            sb = value.ToString();
                        }

                        ThreeLeafProperty leafProperty = new ThreeLeafProperty
                        {
                            Name = description,
                            Value = sb.Trim()
                        };
                        resultList.Add(leafProperty);
                    }
                    // 处理简单对象
                    else if(value != null && !IsComplexType(property.PropertyType))
                    {
                        ThreeLeafProperty leafProperty = new ThreeLeafProperty();
                        leafProperty.Name = description;
                        leafProperty.Value = GetLeafPropertyString(value);
                        leafProperty.Description = value.ToString();
                        resultList.Add(leafProperty);
                    }
                }
                catch
                {
                }
            }

            return resultList;
        }

        /// <summary>
        /// 判断一个对象是否为列表类型
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        private static bool IsList(object obj)
        {
            if(obj == null)
            {
                return false;
            }

            return obj is IEnumerable and not string;
        }

        /// <summary>
        /// 判断一个类型是否为复杂类型（非基元类型）
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        private static bool IsComplexType(Type type)
        {
            return !type.IsPrimitive &&
                   type != typeof(string) &&
                   type != typeof(byte) &&
                   type != typeof(byte?) &&
                   type != typeof(short) &&
                   type != typeof(short?) &&
                   type != typeof(ushort) &&
                   type != typeof(ushort?) &&
                   type != typeof(int) &&
                   type != typeof(int?) &&
                   type != typeof(uint) &&
                   type != typeof(uint?) &&
                   type != typeof(long) &&
                   type != typeof(long?) &&
                   type != typeof(ulong) &&
                   type != typeof(ulong?) &&
                   type != typeof(float) &&
                   type != typeof(float?) &&
                   type != typeof(double) &&
                   type != typeof(double?) &&
                   type != typeof(decimal) &&
                   type != typeof(decimal?) &&
                   type != typeof(bool) &&
                   type != typeof(bool?) &&
                   !type.IsEnum &&
                   type != typeof(DateTime) &&
                    type != typeof(DateTime?) &&
                   type != typeof(TimeSpan) &&
                   type != typeof(TimeSpan?) &&
                   type != typeof(Guid) &&
                   type != typeof(Guid?);
        }

        /// <summary>
        /// 格式化输出
        /// 描述信息，应该格式化打印，把错误的默认值去除，如nan等
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        private static string GetLeafPropertyString(object obj)
        {
            if(obj == null)
            {
                return "-";
            }

            string resultStr = string.Empty;
            // 根据数据类型进行格式化
            if(obj is double doubleValue)
            {
                if(double.IsNaN(doubleValue))
                {
                    resultStr = "-";
                }
                else
                {
                    resultStr = doubleValue.ToString(PatrolDataDisplayHelper.FloatFormat);
                }
            }
            else if(obj is float floatValue)
            {
                if(float.IsNaN(floatValue))
                {
                    resultStr = "-";
                }
                else
                {
                    resultStr = floatValue.ToString(PatrolDataDisplayHelper.FloatFormat);
                }
            }
            else if(obj is DateTime dt)
            {
                if(DateTime.MinValue == dt)
                {
                    resultStr = "-";
                }
                else
                {
                    resultStr = dt.ToString(PatrolDataDisplayHelper.TimeFormat);
                }
            }
            else if(obj is bool kk)
            {
                resultStr = kk ? "是" : "否";
            }
            else
            {
                resultStr = obj.ToString();
            }
            // 去除默认值显示（默认值即为无效值）
            if(resultStr == "-1")
            {
                resultStr = "-";
            }

            return resultStr;
        }

        #endregion

        #region 巡检异常结果

        /// <summary>
        /// 获取单个模块巡检异常结果
        /// </summary>
        /// <param name="singleUnitPatrolResult">单个模块巡检结果</param>
        private static InstanceResult GetSingleUnitErrorResult(SingleUnitPatrolResultBase singleUnitPatrolResult)
        {
            var result = new InstanceResult(singleUnitPatrolResult.UnitName);

            if(singleUnitPatrolResult != null)
            {
                foreach(var property in singleUnitPatrolResult.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
                {
                    try
                    {
                        var value = property.GetValue(singleUnitPatrolResult);
                        // 获取Description特性
                        var description = property.GetCustomAttribute<DescriptionAttribute>()?.Description ?? property.Name;

                        // 处理枚举属性，不为0的认为是非法数据
                        if(property.PropertyType.IsEnum && (int)value != 0)
                        {
                            result.AddProperty(property.Name, description, value);
                        }
                        // 处理SubT类型子属性（仅一层嵌套）
                        else if(IsComplexType(property.PropertyType))
                        {
                            var subResult = GetSubModelErrorResult(value, description);

                            if(subResult.Properties.Count > 0)
                            {
                                result.AddSubInstance(subResult);
                            }
                        }
                    }
                    catch { /* 忽略访问异常 */ }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取单元下的子模块巡检异常结果
        /// </summary>
        /// <param name="obj">子模块巡检结果</param>
        /// <param name="unitName">子模块名称</param>
        private static InstanceResult GetSubModelErrorResult(object obj, string unitName)
        {
            var result = new InstanceResult(unitName);

            if(obj != null)
            {
                foreach(var property in obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
                {
                    try
                    {
                        var value = property.GetValue(obj);

                        // 处理枚举属性，不为0的认为是非法数据
                        if(property.PropertyType.IsEnum && (int)value != 0)
                        {
                            // 跳过空调状态。非0时不认为是异常
                            if(value is eACState)
                            {
                                continue;
                            }

                            // 获取Description特性
                            var description = property.GetCustomAttribute<DescriptionAttribute>()?.Description ?? property.Name;

                            result.AddProperty(property.Name, description, value);
                        }
                    }
                    catch { /* 忽略访问异常 */ }
                }
            }
            return result;
        }

        /// <summary>
        /// 检查节点枚举属性
        /// </summary>
        /// <param name="obj">模块数据对象</param>
        /// <param name="unitName">模块/子模块名称</param>
        /// <param name="isSubT">是否是顶级节点</param>
        private static InstanceResult CheckTopLevelProperties(object obj, string unitName, bool isSubT = false)
        {
            var result = new InstanceResult(unitName);

            if(obj != null)
            {
                foreach(var property in obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
                {
                    try
                    {
                        var value = property.GetValue(obj);

                        // 处理枚举属性
                        if(property.PropertyType.IsEnum && (int)value != 0)
                        {
                            // 获取Description特性
                            var description = property.GetCustomAttribute<DescriptionAttribute>()?.Description ?? property.Name;

                            result.AddProperty(property.Name, description, value);
                        }
                        // 处理SubT类型子属性（仅一层嵌套）
                        else if(!isSubT && IsComplexType(property.PropertyType))
                        {
                            var subResult = CheckTopLevelProperties(value, property.Name, true);

                            if(subResult.Properties.Count > 0)
                            {
                                result.AddSubInstance(subResult);
                            }
                        }
                    }
                    catch { /* 忽略访问异常 */ }
                }
            }
            return result;
        }

        /// <summary>
        /// 格式化输出单个模块巡检异常结果
        /// </summary>
        /// <param name="result"></param>
        /// <returns></returns>
        private static string FormatInstanceOutput(InstanceResult result)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"{result.InstanceName}");
            sb.AppendLine("巡检异常详情：");

            // 主类属性
            foreach(var prop in result.Properties)
            {
                sb.AppendLine($"{prop.Description}: {prop.Value}");
            }

            // 子类属性
            foreach(var sub in result.SubInstances)
            {
                sb.AppendLine($"{sub.InstanceName}");
                foreach(var prop in sub.Properties)
                {
                    sb.AppendLine($"{prop.Description}: {prop.Value}");
                }
            }

            return sb.ToString().TrimEnd();
        }

        #endregion

        #endregion
    }
}

#region 巡检报表显示

/// <summary>
/// 顶层巡检结构模型
/// </summary>
public class InspectionHierarchy
{
    /// <summary>
    /// 第一层：所有巡检单元的顶层列表
    /// </summary>
    public List<OneUnitLayer> FirstLayer { get; set; } = new List<OneUnitLayer>();
}

/// <summary>
/// 第一层单元层模型
/// </summary>
public class OneUnitLayer
{
    /// <summary>
    /// 巡检单元唯一标识
    /// </summary>
    public string UnitId { get; set; }

    /// <summary>
    /// 巡检单元名称
    /// </summary>
    public string UnitName { get; set; }

    /// <summary>
    /// 第二层：复杂属性节点列表（如嵌套对象和集合）
    /// </summary>
    public List<TwoPropertyNode> SecondLayer { get; set; } = new List<TwoPropertyNode>();
}

/// <summary>
/// 第二层属性节点模型
/// </summary>
public class TwoPropertyNode
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; }

    /// <summary>
    /// 属性类型名称（如"Location"或"List"）
    /// </summary>
    public string PropertyType { get; set; }

    /// <summary>
    /// 该属性的状态描述（如"正常"/"警告"）
    /// </summary>
    public string StatusDescription { get; set; }

    /// <summary>
    /// 第三层：该节点包含的简单属性列表
    /// </summary>
    public List<ThreeLeafProperty> LeafProperties { get; set; } = new List<ThreeLeafProperty>();
}

/// <summary>
/// 第三层叶子属性模型
/// </summary>
public class ThreeLeafProperty
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 属性描述（如"传感器序列号"）
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 属性值（基本类型或字符串化对象）
    /// </summary>
    public object Value { get; set; }
}

#endregion

#region 巡检异常结果

/// <summary>
/// 单个模块巡检异常结果
/// </summary>
internal class InstanceResult
{
    /// <summary>
    /// 模块名称
    /// </summary>
    public string InstanceName { get; }

    /// <summary>
    /// 属性列表
    /// </summary>
    public List<PropertyResult> Properties { get; } = new();

    /// <summary>
    /// 子模块列表
    /// </summary>
    public List<InstanceResult> SubInstances { get; } = new();

    /// <summary>
    /// 构造
    /// </summary>
    /// <param name="name"></param>
    public InstanceResult(string name)
    {
        InstanceName = name;
    }

    /// <summary>
    /// 主模块添加属性信息
    /// </summary>
    /// <param name="name"></param>
    /// <param name="desc"></param>
    /// <param name="value"></param>
    public void AddProperty(string name, string desc, object value)
    {
        Properties.Add(new PropertyResult(name, desc, value));
    }

    /// <summary>
    /// 添加子模块
    /// </summary>
    /// <param name="sub"></param>
    public void AddSubInstance(InstanceResult sub)
    {
        SubInstances.Add(sub);
    }
}

/// <summary>
/// 属性结果
/// </summary>
internal class PropertyResult
{
    /// <summary>
    /// 属性名
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 属性描述信息
    /// </summary>
    public string Description { get; }

    /// <summary>
    /// 属性值
    /// </summary>
    public object Value { get; }

    public PropertyResult(string name, string desc, object value)
    {
        Name = name;
        Description = desc;
        Value = value;
    }
}

#endregion