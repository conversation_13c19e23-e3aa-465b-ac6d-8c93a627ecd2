﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using Fpi.Alarm;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.WCS3900;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 五参数仪表巡检结果
    /// </summary>
    public class WCSEquipResult : SubModelResultBase
    {
        #region 字段属性

        #region 仪表状态

        /// <summary>
        /// 通讯状态
        /// </summary>
        [Description("通讯状态")]
        public eModuleWorkingState ComState { get; set; }

        /// <summary>
        /// 系统状态
        /// </summary>
        [Description("系统状态")]
        public eWCS3900DeviceState SystemState { get; set; }

        /// <summary>
        /// 报警状态
        /// </summary>
        [Description("报警状态")]
        public eModuleWorkingState AlarmState { get; set; }

        /// <summary>
        /// 当前报警列表
        /// </summary>
        [Description("当前报警列表")]
        public List<string> CurrentAlarmList { get; set; } = new List<string>();

        /// <summary>
        /// 器件自检结果
        /// 异常时显示（自检类型，自检时间，是否正常）
        /// 暂不实现
        /// </summary>
        [Description("器件自检结果")]
        public eModuleWorkingState SelfCheckResult { get; set; }

        /// <summary>
        /// 流通池脏污状态
        /// </summary>
        [Description("流通池脏污状态")]
        public eSmutState FlowPoolState { get; set; }

        #endregion

        #region 器件使用信息

        /// <summary>
        /// PH电极使用信息
        /// </summary>
        [Description("PH电极使用信息")]
        public ElectrodeUsageStatistics PHElectrodeUsageState { get; set; } = new ElectrodeUsageStatistics("PH电极");

        /// <summary>
        /// 电导率电极使用信息
        /// </summary>
        [Description("电导率电极使用信息")]
        public ElectrodeUsageStatistics ConduElectrodeUsageState { get; set; } = new ElectrodeUsageStatistics("电导率电极");

        /// <summary>
        /// 溶解氧电极使用信息
        /// </summary>
        [Description("溶解氧电极使用信息")]
        public ElectrodeUsageStatistics OxyElectrodeUsageState { get; set; } = new ElectrodeUsageStatistics("溶解氧电极");

        /// <summary>
        /// 浊度电极使用信息
        /// </summary>
        [Description("浊度电极使用信息")]
        public ElectrodeUsageStatistics TurbElectrodeUsageState { get; set; } = new ElectrodeUsageStatistics("浊度电极");

        #endregion

        #region 关键参数

        /// <summary>
        /// PH关键参数
        /// </summary>
        [Description("PH关键参数")]
        public ElectrodeKeyParams PHKeyParams { get; set; } = new ElectrodeKeyParams("PH");

        /// <summary>
        /// 电导率关键参数
        /// </summary>
        [Description("电导率关键参数")]
        public ElectrodeKeyParams ConduKeyParams { get; set; } = new ElectrodeKeyParams("电导率");

        /// <summary>
        /// 溶解氧关键参数
        /// </summary>
        [Description("溶解氧关键参数")]
        public ElectrodeKeyParams OxyKeyParams { get; set; } = new ElectrodeKeyParams("溶解氧");

        /// <summary>
        /// 浊度关键参数
        /// </summary>
        [Description("浊度关键参数")]
        public ElectrodeKeyParams TurbKeyParams { get; set; } = new ElectrodeKeyParams("浊度");

        #endregion

        #endregion

        #region 构造

        public WCSEquipResult()
        {
            ModelName = "五参数仪表";
        }

        public WCSEquipResult(WCS3900Equip wcs3900Equip)
        {
            if(wcs3900Equip == null)
            {
                return;
            }

            ModelName = "五参数仪表";

            #region 仪表状态

            ComState = wcs3900Equip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            SystemState = wcs3900Equip.DeviceStateParams.DeviceState;
            AlarmState = wcs3900Equip.IsAlarmExceptComError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            if(AlarmState == eModuleWorkingState.异常)
            {
                CurrentAlarmList = AlarmManager.GetInstance().GetCurrentAlarms().
                    Where(x => x.AlarmSource.id == wcs3900Equip.AlarmSourceId && x.AlarmCode.id != wcs3900Equip.ComErrorAlarmCodeId).
                    Select(x => x.AlarmCode.description).
                    Distinct().
                    ToList();
            }

            // 流通池脏污状态
            {
                var imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult?.GetModelResultByType<FiveParamFlowPoolPatrolResult>();

                if(imagePatrolResult != null)
                {
                    FlowPoolState = imagePatrolResult.FiveParamFlowPoolState;
                }
            }

            #endregion

            #region 器件使用信息

            if(wcs3900Equip.CommonParam.CommonParams.ContainsKey(eWCSNodeType.w01001))
            {
                var info = wcs3900Equip.CommonParam.CommonParams[eWCSNodeType.w01001];
                PHElectrodeUsageState.ElectrodeReplaceDays = info.ElectrodeReplaceDays;
                PHElectrodeUsageState.ElectrodeLogestUseTime = info.ElectrodeLogestUseTime;
            }

            if(wcs3900Equip.CommonParam.CommonParams.ContainsKey(eWCSNodeType.w01014))
            {
                var info = wcs3900Equip.CommonParam.CommonParams[eWCSNodeType.w01014];
                ConduElectrodeUsageState.ElectrodeReplaceDays = info.ElectrodeReplaceDays;
                ConduElectrodeUsageState.ElectrodeLogestUseTime = info.ElectrodeLogestUseTime;
            }

            if(wcs3900Equip.CommonParam.CommonParams.ContainsKey(eWCSNodeType.w01009))
            {
                var info = wcs3900Equip.CommonParam.CommonParams[eWCSNodeType.w01009];
                OxyElectrodeUsageState.ElectrodeReplaceDays = info.ElectrodeReplaceDays;
                OxyElectrodeUsageState.ElectrodeLogestUseTime = info.ElectrodeLogestUseTime;
            }

            if(wcs3900Equip.CommonParam.CommonParams.ContainsKey(eWCSNodeType.w01003))
            {
                var info = wcs3900Equip.CommonParam.CommonParams[eWCSNodeType.w01003];
                TurbElectrodeUsageState.ElectrodeReplaceDays = info.ElectrodeReplaceDays;
                TurbElectrodeUsageState.ElectrodeLogestUseTime = info.ElectrodeLogestUseTime;
            }

            #endregion

            #region 关键参数

            PHKeyParams.CalibrateFlowTime = wcs3900Equip.PHMeasureParam.CalibrateFlowTime;
            PHKeyParams.CalibrateResult = wcs3900Equip.PHMeasureParam.CalibrateResult;

            ConduKeyParams.CalibrateFlowTime = wcs3900Equip.ConduMeasureParam.CalibrateFlowTime;
            ConduKeyParams.CalibrateResult = wcs3900Equip.ConduMeasureParam.CalibrateResult;

            OxyKeyParams.CalibrateFlowTime = wcs3900Equip.OxyMeasureParam.CalibrateFlowTime;
            OxyKeyParams.CalibrateResult = wcs3900Equip.OxyMeasureParam.CalibrateResult;

            TurbKeyParams.CalibrateFlowTime = wcs3900Equip.TurbMeasureParam.CalibrateFlowTime;
            TurbKeyParams.CalibrateResult = wcs3900Equip.TurbMeasureParam.CalibrateResult;

            #endregion

            // 结果状态判断
            if(ComState != eModuleWorkingState.正常 || SystemState != eWCS3900DeviceState.系统正常 || AlarmState != eModuleWorkingState.正常 || FlowPoolState != eSmutState.正常)
            {
                PatrolResult = ePatrolResult.异常;
            }
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override string GetResultStr()
        {
            // 报警详情
            string alarmDetail = string.Join(",", CurrentAlarmList);

            StringBuilder resultStr = new StringBuilder();

            resultStr.AppendLine(ModelName)
                .AppendLine($"巡检结果：{PatrolResult}")
                .AppendLine($"1.仪表状态")
                .AppendLine($"通讯状态：{ComState}")
                .AppendLine($"系统状态：{SystemState}")
                .AppendLine($"报警状态：{AlarmState}");
            if(!string.IsNullOrEmpty(alarmDetail))
            {
                resultStr.AppendLine(alarmDetail);
            }
            resultStr.AppendLine($"流通池脏污状态：{FlowPoolState}")
                .AppendLine($"2.器件使用信息：")
                .Append(PHElectrodeUsageState.GetResultStr())
                .Append(ConduElectrodeUsageState.GetResultStr())
                .Append(OxyElectrodeUsageState.GetResultStr())
                .Append(TurbElectrodeUsageState.GetResultStr())
                .AppendLine($"3.关键参数：")
                .Append(PHKeyParams.GetResultStr())
                .Append(ConduKeyParams.GetResultStr())
                .Append(OxyKeyParams.GetResultStr())
                .Append(TurbKeyParams.GetResultStr());

            return resultStr.ToString();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 打印异常巡检结果
        /// </summary>
        /// <returns></returns>
        public string GetErrorResultStr()
        {
            StringBuilder resultStr = new StringBuilder();

            if(ComState != eModuleWorkingState.正常)
            {
                resultStr.Append("通讯状态异常，");
            }
            if(SystemState != eWCS3900DeviceState.系统正常)
            {
                resultStr.Append($"系统状态为{SystemState.ToString()}，");
            }
            if(AlarmState != eModuleWorkingState.正常)
            {
                // 报警详情
                string alarmDetail = string.Join("、", CurrentAlarmList);

                resultStr.Append($"报警详情:{alarmDetail}，");
            }
            if(FlowPoolState != eSmutState.正常)
            {
                resultStr.Append("流通池脏污，");
            }

            return resultStr.ToString().TrimEnd('，');
        }

        #endregion
    }

    /// <summary>
    /// 单个参数关键参数
    /// </summary>
    public class ElectrodeKeyParams
    {
        #region 字段属性

        /// <summary>
        /// 参数名称
        /// </summary>
        [Description("参数名称")]
        public string ElectrodeName { get; set; }

        /// <summary>
        /// 标定流程时间
        /// </summary>
        [Description("标定流程时间")]
        public DateTime CalibrateFlowTime { get; set; }

        /// <summary>
        /// 标定是否合格
        /// </summary>
        [Description("标定是否合格")]
        public eQualifyResult CalibrateResult { get; set; }

        #endregion

        #region 构造

        public ElectrodeKeyParams()
        {
        }

        public ElectrodeKeyParams(string name)
        {
            ElectrodeName = name;
        }

        #endregion

        #region 公共方法

        public string GetResultStr()
        {
            StringBuilder resultStr = new StringBuilder();
            resultStr.AppendLine($"参数名称：{ElectrodeName}")
                .AppendLine($"标定流程时间：{CalibrateFlowTime.ToDisplayTime()}")
                .AppendLine($"标定是否合格：{CalibrateResult}");

            return resultStr.ToString();
        }

        #endregion
    }
}