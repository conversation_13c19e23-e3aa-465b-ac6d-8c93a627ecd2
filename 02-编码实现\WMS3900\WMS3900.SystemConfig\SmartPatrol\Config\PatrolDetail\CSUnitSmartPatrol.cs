﻿using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 采水单元巡检执行类
    /// </summary>
    public class CSUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public CSUnitSmartPatrol()
        {
            UnitId = "WaterCollection";
            UnitName = "采水单元";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            CSUnitPatrolResult result = new CSUnitPatrolResult();

            // 外部采水设施
            if(GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData != null)
            {
                result.ExterCSFacResult.WaterState = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapWaterColor;
                result.ExterCSFacResult.InvasionState = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapIntrusion;
                result.ExterCSFacResult.FloatState = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapFloat;
                result.ExterCSFacResult.MigrationState = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapMigration;
            }
            result.ExterCSFacResult.DepthState = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterDepthState == eModuleWorkingState.正常 ? eModuleWorkingState.正常 : eModuleWorkingState.异常;
            result.ExterCSFacResult.WaterDepth = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterDepth;
            // 子模块结果状态判断
            if(result.ExterCSFacResult.WaterState != 0 || result.ExterCSFacResult.InvasionState != 0 || result.ExterCSFacResult.FloatState != 0 || result.ExterCSFacResult.MigrationState != 0 || result.ExterCSFacResult.DepthState != 0)
            {
                result.ExterCSFacResult.PatrolResult = ePatrolResult.异常;
            }

            // 采水泵
            result.PumpModelResult.Pump1State = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State;
            result.PumpModelResult.Pump2State = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State;
            result.PumpModelResult.CollectionState = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState;
            result.PumpModelResult.WaterPressState = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState;
            result.PumpModelResult.WaterPipeState = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState;
            // 子模块结果状态判断
            if(result.PumpModelResult.Pump1State != 0 || result.PumpModelResult.Pump2State != 0 || result.PumpModelResult.CollectionState != 0 || result.PumpModelResult.WaterPressState != 0 || result.PumpModelResult.WaterPipeState != 0)
            {
                result.ExterCSFacResult.PatrolResult = ePatrolResult.异常;
            }

            if(UsageStatisticsManager.GetInstance().GetDeviceUsageInfoById(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump1ValveNodeId) is DeviceUsageInfo info1)
            {
                result.PumpModelResult.Pump1TotalWorkTime = info1.PumpWorkTime.TotalHours;
                result.PumpModelResult.Pump1TotalUsedDays = (int)info1.PumpUsedTime.TotalDays;
            }
            if(UsageStatisticsManager.GetInstance().GetDeviceUsageInfoById(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump2ValveNodeId) is DeviceUsageInfo info2)
            {
                result.PumpModelResult.Pump2TotalWorkTime = info2.PumpWorkTime.TotalHours;
                result.PumpModelResult.Pump2TotalUsedDays = (int)info2.PumpUsedTime.TotalDays;
            }

            return result;
        }

        #endregion
    }
}