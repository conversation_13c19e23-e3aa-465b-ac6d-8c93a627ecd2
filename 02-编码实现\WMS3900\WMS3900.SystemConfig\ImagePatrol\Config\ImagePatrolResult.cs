﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Fpi.DB.Manager;
using Fpi.Json;
using Fpi.WMS3000.DB;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 老版单条图像巡检结果
    /// 数据库转换完成后就不再保留
    /// </summary>
    public class ImagePatrolResult : BaseJsonNode
    {
        #region 字段属性

        /// <summary>
        /// 巡检开始时间
        /// </summary>
        [Description("巡检开始时间")]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 触发方式
        [Description("触发方式")]
        /// </summary>
        public eImagePatrolTriggerType PatrolTriggerType { get; set; }

        /// <summary>
        /// 巡检结果列表
        /// </summary>
        [Description("巡检结果列表")]
        public List<ImageUnitPatrolResultBase> ModelResultList { get; set; } = new List<ImageUnitPatrolResultBase>();

        #endregion

        #region 构造

        public ImagePatrolResult()
        {
            StartTime = DateTime.Now;
        }

        public ImagePatrolResult(eImagePatrolTriggerType patrolTriggerType)
        {
            PatrolTriggerType = patrolTriggerType;
            StartTime = DateTime.Now;
        }

        #endregion

        #region 公共方法

        #region 数据保存

        [NonSerialized]
        private static readonly object lockObj = new object();

        /// <summary>
        /// 写图像巡检结果数据到数据库
        /// </summary>
        /// <param name="nodeType"></param>
        /// <param name="dataTime"></param>
        /// <param name="checkType"></param>
        /// <param name="keyparams"></param>
        internal void SaveToDb()
        {
            try
            {
                FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.IMAGEPATROL_RESULT_TABLE);
                if(table == null)
                {
                    throw new Exception("图像巡检结果数据表不存在！");
                }

                lock(lockObj)
                {
                    FpiRow row = new FpiRow();
                    row.SetFieldValue("datatime", this.StartTime);
                    row.SetFieldValue("patrolresult", FpiJsonHelper.ModelToJson(this));
                    table.AddRecord(row);
                }
            }
            catch(Exception e)
            {

                throw new Exception($"保存图像巡检结果数据出错：{e.Message}");
            }
        }

        #endregion

        #region 添加、查找

        /// <summary>
        /// 模块巡检结果汇总到总结果区
        /// </summary>
        /// <param name="patrolResult"></param>
        internal void AddModelResult(ImageUnitPatrolResultBase patrolResult)
        {
            if(patrolResult != null)
            {
                var itemsToRemove = ModelResultList.Where(x => x.GetType() == patrolResult.GetType()).ToList();
                foreach(var oldItem in itemsToRemove)
                {
                    ModelResultList.Remove(oldItem);
                }

                // 添加新的项
                ModelResultList.Add(patrolResult);
            }
        }

        /// <summary>
        /// 根据类型查找模块巡检结果
        /// </summary>
        /// <param name="patrolResult"></param>
        public T GetModelResultByType<T>() where T : class
        {
            return ModelResultList.FirstOrDefault(modelResult => modelResult is T) as T;
        }

        /// <summary>
        /// 根据UnitId查找模块巡检结果
        /// </summary>
        /// <param name="patrolResult"></param>
        public T GetModelResultByUnitId<T>(string unitId) where T : class
        {
            return ModelResultList.FirstOrDefault(modelResult => modelResult.UnitId == unitId && modelResult is T) as T;
        }

        /// <summary>
        /// 根据UnitId查找模块巡检结果
        /// </summary>
        /// <param name="patrolResult"></param>
        public ImageUnitPatrolResultBase GetModelResultByUnitId(string unitId)
        {
            return ModelResultList.FirstOrDefault(modelResult => modelResult.UnitId == unitId);
        }

        #endregion

        #endregion
    }
}