﻿namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    partial class UC_QueryDataReport
    {
        /// <summary> 
        /// 必需的设计器变量。

        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。

        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。

        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.dgvData = new Sunny.UI.UIDataGridView();
            this.pnlTop = new Sunny.UI.UIPanel();
            this.rbYear = new Sunny.UI.UIRadioButton();
            this.dtpSelectedTime = new Sunny.UI.UIDatePicker();
            this.rbQuarter = new Sunny.UI.UIRadioButton();
            this.lblCurrentPeriod = new Sunny.UI.UILabel();
            this.rbMonth = new Sunny.UI.UIRadioButton();
            this.btnExport = new Sunny.UI.UIButton();
            this.rbWeek = new Sunny.UI.UIRadioButton();
            this.btnQuery = new Sunny.UI.UIButton();
            this.rbDay = new Sunny.UI.UIRadioButton();
            this.labTitle = new Sunny.UI.UILabel();
            ((System.ComponentModel.ISupportInitialize)(this.dgvData)).BeginInit();
            this.pnlTop.SuspendLayout();
            this.SuspendLayout();
            // 
            // saveFileDialog
            // 
            this.saveFileDialog.DefaultExt = "xlsx";
            this.saveFileDialog.Filter = "Excel文件|*.xlsx|所有文件|*.*";
            this.saveFileDialog.Title = "导出数据报表";
            // 
            // dgvData
            // 
            this.dgvData.AllowUserToAddRows = false;
            this.dgvData.AllowUserToDeleteRows = false;
            this.dgvData.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvData.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvData.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvData.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(216)))), ((int)(((byte)(225)))), ((int)(((byte)(238)))));
            this.dgvData.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvData.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText;
            this.dgvData.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgvData.ColumnHeadersHeight = 32;
            this.dgvData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvData.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgvData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvData.EnableHeadersVisualStyles = false;
            this.dgvData.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvData.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dgvData.Location = new System.Drawing.Point(0, 53);
            this.dgvData.Name = "dgvData";
            this.dgvData.ReadOnly = true;
            this.dgvData.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvData.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgvData.RowHeadersVisible = false;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle5.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle5.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.dgvData.RowsDefaultCellStyle = dataGridViewCellStyle5;
            this.dgvData.RowTemplate.Height = 29;
            this.dgvData.ScrollBarColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dgvData.ScrollBarRectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dgvData.ScrollBarStyleInherited = false;
            this.dgvData.SelectedIndex = -1;
            this.dgvData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvData.Size = new System.Drawing.Size(1363, 692);
            this.dgvData.TabIndex = 2;
            // 
            // pnlTop
            // 
            this.pnlTop.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(216)))), ((int)(((byte)(225)))), ((int)(((byte)(238)))));
            this.pnlTop.BackgroundImage = global::Fpi.WMS3000.Pollution.Properties.Resources.数据查询标题;
            this.pnlTop.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.pnlTop.Controls.Add(this.rbYear);
            this.pnlTop.Controls.Add(this.dtpSelectedTime);
            this.pnlTop.Controls.Add(this.rbQuarter);
            this.pnlTop.Controls.Add(this.lblCurrentPeriod);
            this.pnlTop.Controls.Add(this.rbMonth);
            this.pnlTop.Controls.Add(this.btnExport);
            this.pnlTop.Controls.Add(this.rbWeek);
            this.pnlTop.Controls.Add(this.btnQuery);
            this.pnlTop.Controls.Add(this.rbDay);
            this.pnlTop.Controls.Add(this.labTitle);
            this.pnlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTop.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(216)))), ((int)(((byte)(225)))), ((int)(((byte)(238)))));
            this.pnlTop.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.pnlTop.Location = new System.Drawing.Point(0, 0);
            this.pnlTop.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTop.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlTop.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.pnlTop.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom;
            this.pnlTop.Size = new System.Drawing.Size(1363, 53);
            this.pnlTop.TabIndex = 0;
            this.pnlTop.Text = null;
            this.pnlTop.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rbYear
            // 
            this.rbYear.AutoSize = true;
            this.rbYear.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbYear.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.rbYear.Location = new System.Drawing.Point(696, 13);
            this.rbYear.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbYear.Name = "rbYear";
            this.rbYear.Size = new System.Drawing.Size(81, 26);
            this.rbYear.Style = Sunny.UI.UIStyle.Custom;
            this.rbYear.TabIndex = 4;
            this.rbYear.Text = "年报表";
            this.rbYear.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // dtpSelectedTime
            // 
            this.dtpSelectedTime.DateFormat = "yyyy年MM月dd日";
            this.dtpSelectedTime.DateYearFormat = "yyyy年";
            this.dtpSelectedTime.DateYearMonthFormat = "yyyy年MM月";
            this.dtpSelectedTime.FillColor = System.Drawing.Color.White;
            this.dtpSelectedTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpSelectedTime.Location = new System.Drawing.Point(796, 11);
            this.dtpSelectedTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpSelectedTime.MaxLength = 11;
            this.dtpSelectedTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpSelectedTime.Name = "dtpSelectedTime";
            this.dtpSelectedTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpSelectedTime.Size = new System.Drawing.Size(160, 30);
            this.dtpSelectedTime.SymbolDropDown = 61555;
            this.dtpSelectedTime.SymbolNormal = 61555;
            this.dtpSelectedTime.SymbolSize = 24;
            this.dtpSelectedTime.TabIndex = 0;
            this.dtpSelectedTime.Text = "2024年07月04日";
            this.dtpSelectedTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpSelectedTime.Value = new System.DateTime(2024, 7, 4, 0, 0, 0, 0);
            this.dtpSelectedTime.Watermark = "";
            this.dtpSelectedTime.ValueChanged += new Sunny.UI.UIDatePicker.OnDateTimeChanged(this.dtpSelectedTime_ValueChanged);
            // 
            // rbQuarter
            // 
            this.rbQuarter.AutoSize = true;
            this.rbQuarter.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbQuarter.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.rbQuarter.Location = new System.Drawing.Point(615, 13);
            this.rbQuarter.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbQuarter.Name = "rbQuarter";
            this.rbQuarter.Size = new System.Drawing.Size(81, 26);
            this.rbQuarter.Style = Sunny.UI.UIStyle.Custom;
            this.rbQuarter.TabIndex = 3;
            this.rbQuarter.Text = "季报表";
            this.rbQuarter.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // lblCurrentPeriod
            // 
            this.lblCurrentPeriod.AutoSize = true;
            this.lblCurrentPeriod.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblCurrentPeriod.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblCurrentPeriod.Location = new System.Drawing.Point(971, 16);
            this.lblCurrentPeriod.Name = "lblCurrentPeriod";
            this.lblCurrentPeriod.Size = new System.Drawing.Size(106, 21);
            this.lblCurrentPeriod.Style = Sunny.UI.UIStyle.Custom;
            this.lblCurrentPeriod.TabIndex = 1;
            this.lblCurrentPeriod.Text = "当前统计周期";
            this.lblCurrentPeriod.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // rbMonth
            // 
            this.rbMonth.AutoSize = true;
            this.rbMonth.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbMonth.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.rbMonth.Location = new System.Drawing.Point(534, 13);
            this.rbMonth.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbMonth.Name = "rbMonth";
            this.rbMonth.Size = new System.Drawing.Size(81, 26);
            this.rbMonth.Style = Sunny.UI.UIStyle.Custom;
            this.rbMonth.TabIndex = 2;
            this.rbMonth.Text = "月报表";
            this.rbMonth.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // btnExport
            // 
            this.btnExport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExport.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnExport.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnExport.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnExport.Location = new System.Drawing.Point(1258, 10);
            this.btnExport.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnExport.Name = "btnExport";
            this.btnExport.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnExport.Size = new System.Drawing.Size(80, 32);
            this.btnExport.Style = Sunny.UI.UIStyle.Custom;
            this.btnExport.TabIndex = 1;
            this.btnExport.Text = "导出";
            this.btnExport.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // rbWeek
            // 
            this.rbWeek.AutoSize = true;
            this.rbWeek.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbWeek.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.rbWeek.Location = new System.Drawing.Point(453, 13);
            this.rbWeek.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbWeek.Name = "rbWeek";
            this.rbWeek.Size = new System.Drawing.Size(81, 26);
            this.rbWeek.Style = Sunny.UI.UIStyle.Custom;
            this.rbWeek.TabIndex = 1;
            this.rbWeek.Text = "周报表";
            this.rbWeek.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // btnQuery
            // 
            this.btnQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnQuery.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnQuery.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnQuery.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnQuery.Location = new System.Drawing.Point(1155, 10);
            this.btnQuery.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnQuery.Size = new System.Drawing.Size(80, 32);
            this.btnQuery.Style = Sunny.UI.UIStyle.Custom;
            this.btnQuery.TabIndex = 0;
            this.btnQuery.Text = "查询";
            this.btnQuery.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnQuery.Click += new System.EventHandler(this.btnStartQuery_Click);
            // 
            // rbDay
            // 
            this.rbDay.AutoSize = true;
            this.rbDay.Checked = true;
            this.rbDay.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbDay.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.rbDay.Location = new System.Drawing.Point(372, 13);
            this.rbDay.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbDay.Name = "rbDay";
            this.rbDay.Size = new System.Drawing.Size(81, 26);
            this.rbDay.Style = Sunny.UI.UIStyle.Custom;
            this.rbDay.TabIndex = 0;
            this.rbDay.Text = "日报表";
            this.rbDay.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // labTitle
            // 
            this.labTitle.AutoSize = true;
            this.labTitle.BackColor = System.Drawing.Color.Transparent;
            this.labTitle.Font = new System.Drawing.Font("微软雅黑", 15F);
            this.labTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.labTitle.Location = new System.Drawing.Point(33, 12);
            this.labTitle.Name = "labTitle";
            this.labTitle.Size = new System.Drawing.Size(132, 27);
            this.labTitle.TabIndex = 10;
            this.labTitle.Text = "污染数据报表";
            this.labTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // UC_QueryDataReport
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.dgvData);
            this.Controls.Add(this.pnlTop);
            this.Name = "UC_QueryDataReport";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1363, 745);
            this.Load += new System.EventHandler(this.UC_QueryDataReport_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dgvData)).EndInit();
            this.pnlTop.ResumeLayout(false);
            this.pnlTop.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
        private Sunny.UI.UIPanel pnlTop;
        private Sunny.UI.UIRadioButton rbYear;
        private Sunny.UI.UIRadioButton rbQuarter;
        private Sunny.UI.UIRadioButton rbMonth;
        private Sunny.UI.UIRadioButton rbWeek;
        private Sunny.UI.UIRadioButton rbDay;
        private Sunny.UI.UILabel lblCurrentPeriod;
        private Sunny.UI.UIDatePicker dtpSelectedTime;
        private Sunny.UI.UIButton btnExport;
        private Sunny.UI.UIButton btnQuery;
        private Sunny.UI.UILabel labTitle;
        protected Sunny.UI.UIDataGridView dgvData;
    }
}
