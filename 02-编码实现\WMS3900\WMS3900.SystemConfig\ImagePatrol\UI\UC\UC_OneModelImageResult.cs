﻿using System;
using System.Drawing;
using System.IO;
using Fpi.Util.Extensions;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.UI
{
    /// <summary>
    /// 单个视频模块图像巡检结果
    /// 控件高度，在360基础上，每个数据项加25
    /// </summary>
    public partial class UC_OneModelImageResult : UIUserControl
    {
        #region 构造

        public UC_OneModelImageResult(ImageUnitPatrolResultBase modelResult)
        {
            InitializeComponent();
            // 加载配置信息
            LoadInfo(modelResult);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 放大
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMagnify_Click(object sender, EventArgs e)
        {
            new FrmOneImageShow(picMain.Image?.Clone() as Image, $"{gbMain.Text}-图像").ShowDialog();
        }

        #endregion

        #region 私有方法

        private void LoadInfo(ImageUnitPatrolResultBase modelResult)
        {
            // 设置标题
            gbMain.Text = modelResult.UnitName;

            // 加载图片
            var path = FileExtension.GetAbsolutePath(modelResult.ImagePath);
            if(File.Exists(path))
            {
                picMain.BringToFront();
                picMain.Image = ImageEx.FromFile(path);
            }
            else
            {
                lblErrorInfo.BringToFront();
                if(string.IsNullOrEmpty(modelResult.ImagePath))
                {
                    lblErrorInfo.Text = $"图像文件不存在";
                }
                else
                {
                    lblErrorInfo.Text = $"图像文件不存在，文件存储路径：{path}";
                }
            }

            if(modelResult.PatrolState == ePatrolState.巡检完成)
            {
                // 设置信息
                var infoList = modelResult.GetResultStr();
                // 填充内容
                txtInfo.Text = string.Join(Environment.NewLine, infoList);
                // 动态调整控件高度
                this.Height = 220 + infoList.Count * 25;
            }
            else
            {
                // 填充内容
                txtInfo.Text = modelResult.PatrolState.ToString();
            }
        }

        #endregion
    }
}