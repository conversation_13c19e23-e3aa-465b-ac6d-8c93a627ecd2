﻿using System;
using System.Diagnostics;
using System.Threading;
using Fpi.Devices;
using Fpi.Operations;
using Fpi.Operations.Interfaces;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.SystemConfig;
using Fpi.WMS3000.SystemOperation.Helper;

namespace Fpi.WMS3000.Pollution.SystemOperation.OperationTemplate
{
    /// <summary>
    /// 采样器混采单次采水操作模板
    /// </summary>
    public class Op_WaterCollection : CustomOperation
    {
        #region 字段属性

        /// <summary>
        /// 是否初始化
        /// </summary>
        private bool _isInited;

        /// <summary>
        /// 水泵运行电流
        /// </summary>
        private double _pumpCurrent;

        /// <summary>
        /// 采水压力
        /// </summary>
        private double _waterPressure;

        /// <summary>
        /// 系统所用采样器
        /// </summary>
        private Device _sampleEquip;

        /// <summary>
        /// 本次采水所用采水桶
        /// </summary>
        private eSamplingBucket _currentBucketSet;

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return "采样器混采单次采水操作";
        }

        public override object CustomDo(string instrumentId, bool manual, object inputData, IOperationListener operationListener)
        {
            try
            {
                if(!_isInited)
                {
                    InitProperty();
                }

                _pumpCurrent = double.NaN;
                _waterPressure = double.NaN;

                // 确定该使用哪个泵
                bool usePump1 = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpUsingMode == ePumpUsingMode.只用泵一 ||
                    (ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpUsingMode == ePumpUsingMode.双泵交替 &&
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.LastUsedPump == ePumpUsed.泵二);

                // 使用泵一
                if(usePump1)
                {
                    // 泵一工作正常
                    if(Pump1Work(instrumentId))
                    {
                        SystemLogHelper.WriteOperationLog("泵一工作正常,采水完成");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.正常;
                    }
                    //  泵一工作异常，但是设置了双泵交替
                    else if(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpUsingMode == ePumpUsingMode.双泵交替)
                    {
                        SystemLogHelper.WriteOperationLog("泵一工作异常,切换到泵二");
                        //  泵一工作异常，泵二工作正常
                        if(Pump2Work(instrumentId))
                        {
                            SystemLogHelper.WriteOperationLog("泵二工作正常,采水完成");
                            GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.上水超时;
                        }
                        //  泵一工作异常，泵二工作异常
                        else
                        {
                            SystemLogHelper.WriteOperationLog("泵二工作异常,采水异常，采水流程中止");
                            GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.无水报警;
                        }
                    }
                    //  泵一工作异常，没有设置双泵交替
                    else
                    {
                        SystemLogHelper.WriteOperationLog("泵一工作异常,采水异常，采水流程中止");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.无水报警;
                    }
                }
                // 使用泵二
                else
                {
                    // 泵二工作正常
                    if(Pump2Work(instrumentId))
                    {
                        SystemLogHelper.WriteOperationLog("泵二工作正常,采水完成");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.正常;
                    }
                    //  泵二工作异常，但是设置了双泵交替
                    else if(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpUsingMode == ePumpUsingMode.双泵交替)
                    {
                        SystemLogHelper.WriteOperationLog("泵二工作异常,切换到泵一");
                        //  泵二工作异常，泵一工作正常
                        if(Pump1Work(instrumentId))
                        {
                            SystemLogHelper.WriteOperationLog("泵一工作正常,采水完成");
                            GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.上水超时;
                        }
                        //  泵二工作异常，泵一工作异常
                        else
                        {
                            SystemLogHelper.WriteOperationLog("泵一工作异常,采水异常，采水流程中止");
                            GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.无水报警;
                        }
                    }
                    //  泵二工作异常，没有设置双泵交替
                    else
                    {
                        SystemLogHelper.WriteOperationLog("泵二工作异常,采水异常，采水流程中止");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState = eWaterCollectionState.无水报警;
                    }
                }

                // 采水报警状态判断
                OpHelper.CheckWaterCollectionAlarmState();

                // 采水异常标志因子传递值
                var stateNode = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterAnomalyMarkNode;
                var flag = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState == eWaterCollectionState.无水报警;
                stateNode.SetValue(flag);
                SystemLogHelper.WriteOperationLog($"{stateNode.name}值设置为{flag}");

                SystemLogHelper.WriteOperationLog(string.Format("[{0}]操作完成!", GetOperationName()));
            }
            catch(Exception ex)
            {
                SystemLogHelper.WriteSystemErrorLog(string.Format("[{0}]操作异常: {1}!", GetOperationName(), ex.Message));
                SystemLogHelper.WriteOperationLog(string.Format("[{0}]操作异常: {1}!", GetOperationName(), ex.Message));
            }
            finally
            {
                // 序列化数据缓存
                GlobalDataCache.GetInstance().Save();
            }

            // 记录采水日志信息
            try
            {
                DateTime sampleTime = SystemHelper.SampleTime;
                if(sampleTime == DateTime.MinValue)
                {
                    sampleTime = DateTime.Now;
                }

                var turbidityLimit = double.NaN;
                if(ExterEquipConfigManager.GetInstance().WaterPretreatmentConfigInfo.SedimentationMode == eSedimentationMode.动态沉降)
                {
                    turbidityLimit = ExterEquipConfigManager.GetInstance().WaterPretreatmentConfigInfo.TargetTurbValue;
                }

                int currentPump = (int)GlobalDataCache.GetInstance().WaterCollectionModuleData.LastUsedPump;

                int waterPoint = 0;
                int floatingDebris = 0;
                int waterColor = 0;
                if(GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData != null)
                {
                    waterPoint = (int)GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapMigration;
                    floatingDebris = (int)GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapFloat;
                    waterColor = ((int)GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapWaterColor);
                }

                SaveDataHelper.WritePollutionWaterCollectionDataToDb(
                    sampleTime,
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleStartTime,
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleStopTime,
                    (int)GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleWaitingTime,
                    waterPoint,
                    floatingDebris,
                    currentPump,
                    (int)GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionAlarmState,
                    waterColor,
                    _pumpCurrent,
                    _waterPressure,
                    (int)_currentBucketSet
                    );
            }
            catch
            {
            }

            return null;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查参数设置是否合法
        /// </summary>
        private void InitProperty()
        {
            ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Check();

            if(ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice is not IMixedSampleDeviceOperation)
            {
                throw new Exception($"请配置标准混采采样器！");
            }

            _sampleEquip = ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice;

            _isInited = true;
        }

        /// <summary>
        /// 泵一工作
        /// </summary>
        /// <returns></returns>
        private bool Pump1Work(string instrumentId)
        {
            try
            {
                // 泵使用记录
                GlobalDataCache.GetInstance().WaterCollectionModuleData.LastUsedPump = ePumpUsed.泵一;
                // 采水开始时间
                GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleStartTime = DateTime.Now;
                // 采水泵工作状态
                GlobalDataCache.GetInstance().WaterCollectionModuleData.CollectionPumpWorkState = ePumpWorkState.工作;

                // 启动泵
                OpHelper.StateNodeSwitch(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump1Node, true);

                Stopwatch sp = new Stopwatch();
                sp.Start();

                // 监测水泵运行电流，10s后判断水泵状态
                this.Sleep(instrumentId, 10 * 1000);

                _pumpCurrent = GlobalDataCache.GetInstance().WaterCollectionModuleData.PumpCurrent;
                var currentLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentLowerLimit;
                var currentUpperLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentUpperLimit;
                // 电流小于下限，水泵开路
                if(_pumpCurrent < currentLowerLimit)
                {
                    SystemLogHelper.WriteOperationLog($"泵一运行电流{_pumpCurrent:F2}小于下限{currentLowerLimit:F2}，水泵开路");
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State = eWaterPumpState.开路;
                    return false;
                }
                // 电流大于上限，水泵过载
                else if(_pumpCurrent > currentUpperLimit)
                {
                    SystemLogHelper.WriteOperationLog($"泵一运行电流{_pumpCurrent:F2}大于上限{currentUpperLimit:F2}，水泵过载");
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State = eWaterPumpState.过载;
                    return false;
                }
                // 等待采水压力判断
                else
                {
                    SystemLogHelper.WriteOperationLog($"泵一运行电流{_pumpCurrent:F2}正常，执行压力判断");

                    // 等待一段时长
                    int pressureJudgeTime = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PressureJudgeTime - 10;
                    if(pressureJudgeTime > 0)
                    {
                        Thread.Sleep(pressureJudgeTime * 1000);
                    }

                    _waterPressure = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressure;
                    var pluggingLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipePluggingLowerLimit;
                    var normalLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPressureNormalLowerLimit;
                    var blastingLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipeBlastingLowerLimit;
                    // 采水压力不足，小于堵塞压力下限
                    if(_waterPressure < pluggingLowerLimit)
                    {
                        SystemLogHelper.WriteOperationLog($"泵一采水压力为{_waterPressure:F2}，小于堵塞压力下限{pluggingLowerLimit:F2}，水泵空转，采水管可能堵塞");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState = eWaterPipeState.可能堵塞;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State = eWaterPumpState.空转;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.异常;
                        return false;
                    }
                    // 采水压力不足，大于堵塞压力下限，小于正常压力下限
                    else if(_waterPressure < normalLowerLimit)
                    {
                        SystemLogHelper.WriteOperationLog($"泵一采水压力为{_waterPressure:F2}，小于正常压力下限{normalLowerLimit:F2}，水泵空转");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State = eWaterPumpState.空转;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.异常;
                        return false;
                    }
                    // 采水压力过大，大于爆管压力下限
                    else if(_waterPressure > blastingLowerLimit)
                    {
                        SystemLogHelper.WriteOperationLog($"泵一采水压力为{_waterPressure:F2}，大于爆管压力下限{blastingLowerLimit:F2}，采水管可能爆管");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState = eWaterPipeState.可能爆管;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State = eWaterPumpState.正常;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.异常;
                        return false;
                    }
                    // 采水压力正常,大于正常压力下限，小于爆管压力下限
                    else
                    {
                        SystemLogHelper.WriteOperationLog($"泵一采水压力为{_waterPressure:F2}，水压正常");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState = eWaterPipeState.正常;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State = eWaterPumpState.正常;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.正常;
                    }
                }

                // 等待管路进水，供采样器采水
                while(sp.Elapsed.TotalSeconds < ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SamplerSampleWaitingTime)
                {
                    this.Sleep(instrumentId, 1000);
                }

                // 获取当前小时应使用的采样桶
                _currentBucketSet = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.CurrentSampleBucketSet;
                eMixedSampleDeviceOperType operType = _currentBucketSet == eSamplingBucket.A桶 ? eMixedSampleDeviceOperType.A桶采水 : eMixedSampleDeviceOperType.B桶采水;

                SystemLogHelper.WriteOperationLog($"触发采样器往{_currentBucketSet}采水");
                // 触发采样器往指定桶采水
                ((IMixedSampleDeviceOperation)_sampleEquip).StartOper(operType);

                // 等待进水完成
                while(sp.Elapsed.TotalSeconds < ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SampleWaitingTime)
                {
                    this.Sleep(instrumentId, 1000);
                }

                // 进水完成
                SystemLogHelper.WriteOperationLog($"进水完成");

                return true;
            }
            catch(Exception)
            {
                return false;
            }
            finally
            {
                // 采水结束时间
                GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleStopTime = DateTime.Now;
                // 采水泵工作状态
                GlobalDataCache.GetInstance().WaterCollectionModuleData.CollectionPumpWorkState = ePumpWorkState.空闲;

                // 若水泵正常运行，状态置为空闲
                if(GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State == eWaterPumpState.正常)
                {
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State = eWaterPumpState.空闲;
                }

                // 关闭泵
                OpHelper.StateNodeSwitch(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump1Node, false);
            }
        }

        /// <summary>
        /// 泵二工作
        /// </summary>
        /// <returns></returns>
        private bool Pump2Work(string instrumentId)
        {
            try
            {
                // 泵使用记录
                GlobalDataCache.GetInstance().WaterCollectionModuleData.LastUsedPump = ePumpUsed.泵二;
                // 采水开始时间
                GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleStartTime = DateTime.Now;
                // 采水泵工作状态
                GlobalDataCache.GetInstance().WaterCollectionModuleData.CollectionPumpWorkState = ePumpWorkState.工作;

                // 启动泵
                OpHelper.StateNodeSwitch(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump2Node, true);

                Stopwatch sp = new Stopwatch();
                sp.Start();

                // 监测水泵运行电流，10s后判断水泵状态
                this.Sleep(instrumentId, 10 * 1000);

                _pumpCurrent = GlobalDataCache.GetInstance().WaterCollectionModuleData.PumpCurrent;
                var currentLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentLowerLimit;
                var currentUpperLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PumpCurrentUpperLimit;
                // 电流小于下限，水泵开路
                if(_pumpCurrent < currentLowerLimit)
                {
                    SystemLogHelper.WriteOperationLog($"泵二运行电流{_pumpCurrent:F2}小于下限{currentLowerLimit:F2}，水泵开路");
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State = eWaterPumpState.开路;
                    return false;
                }
                // 电流大于上限，水泵过载
                else if(_pumpCurrent > currentUpperLimit)
                {
                    SystemLogHelper.WriteOperationLog($"泵二运行电流{_pumpCurrent:F2}大于上限{currentUpperLimit:F2}，水泵过载");
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State = eWaterPumpState.过载;
                    return false;
                }
                // 等待采水压力判断
                else
                {
                    SystemLogHelper.WriteOperationLog($"泵二运行电流{_pumpCurrent:F2}正常，执行压力判断");

                    // 等待一段时长
                    int pressureJudgeTime = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.PressureJudgeTime - 10;
                    if(pressureJudgeTime > 0)
                    {
                        Thread.Sleep(pressureJudgeTime * 1000);
                    }

                    _waterPressure = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressure;
                    var pluggingLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipePluggingLowerLimit;
                    var normalLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPressureNormalLowerLimit;
                    var blastingLowerLimit = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterPipeBlastingLowerLimit;
                    // 采水压力不足，小于堵塞压力下限
                    if(_waterPressure < pluggingLowerLimit)
                    {
                        SystemLogHelper.WriteOperationLog($"泵二采水压力为{_waterPressure:F2}，小于堵塞压力下限{pluggingLowerLimit:F2}，水泵空转，采水管可能堵塞");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState = eWaterPipeState.可能爆管;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State = eWaterPumpState.空转;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.异常;
                        return false;
                    }
                    // 采水压力不足，大于堵塞压力下限，小于正常压力下限
                    else if(_waterPressure < normalLowerLimit)
                    {
                        SystemLogHelper.WriteOperationLog($"泵二采水压力为{_waterPressure:F2}，小于正常压力下限{normalLowerLimit:F2}，水泵空转");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State = eWaterPumpState.空转;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.异常;
                        return false;
                    }
                    // 采水压力过大，大于爆管压力下限
                    else if(_waterPressure > blastingLowerLimit)
                    {
                        SystemLogHelper.WriteOperationLog($"泵二采水压力为{_waterPressure:F2}，大于爆管压力下限{blastingLowerLimit:F2}，采水管可能爆管");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState = eWaterPipeState.可能堵塞;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State = eWaterPumpState.正常;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.异常;
                        return false;
                    }
                    // 采水压力正常,大于正常压力下限，小于爆管压力下限
                    else
                    {
                        SystemLogHelper.WriteOperationLog($"泵二采水压力为{_waterPressure:F2}，水压正常");
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState = eWaterPipeState.正常;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State = eWaterPumpState.正常;
                        GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState = eModuleWorkingState.正常;
                    }
                }

                // 等待管路进水，供采样器采水
                while(sp.Elapsed.TotalSeconds < ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SamplerSampleWaitingTime)
                {
                    this.Sleep(instrumentId, 1000);
                }

                // 获取当前小时应使用的采样桶
                _currentBucketSet = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.CurrentSampleBucketSet;
                eMixedSampleDeviceOperType operType = _currentBucketSet == eSamplingBucket.A桶 ? eMixedSampleDeviceOperType.A桶采水 : eMixedSampleDeviceOperType.B桶采水;

                SystemLogHelper.WriteOperationLog($"触发采样器往{_currentBucketSet}采水");
                // 触发采样器往指定桶采水
                ((IMixedSampleDeviceOperation)_sampleEquip).StartOper(operType);

                // 等待进水完成
                while(sp.Elapsed.TotalSeconds < ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.SampleWaitingTime)
                {
                    this.Sleep(instrumentId, 1000);
                }

                // 进水完成
                SystemLogHelper.WriteOperationLog($"进水完成");

                return true;
            }
            catch(Exception)
            {
                return false;
            }
            finally
            {
                // 采水结束时间
                GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleStopTime = DateTime.Now;
                // 采水泵工作状态
                GlobalDataCache.GetInstance().WaterCollectionModuleData.CollectionPumpWorkState = ePumpWorkState.空闲;

                // 若水泵正常运行，状态置为空闲
                if(GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State == eWaterPumpState.正常)
                {
                    GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State = eWaterPumpState.空闲;
                }

                // 关闭泵
                OpHelper.StateNodeSwitch(ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.Pump2Node, false);
            }
        }

        #endregion
    }
}