# UC_DataReport 污染数据统计报表功能

## 功能概述

UC_DataReport.cs 实现了一个完整的污染数据统计报表功能，支持日、周、月、季、年五种统计周期的数据报表生成。

## 主要特性

### 1. 时间周期支持
- **日报表**: 24小时时段统计，查询小时数据
- **周报表**: 按周统计，查询日数据
- **月报表**: 按月统计，查询日数据  
- **季报表**: 按季度统计，查询日数据（新增功能）
- **年报表**: 按年统计，查询日数据

### 2. 统计计算规则
- **最大值**: 时间段内的最大值
- **最小值**: 时间段内的最小值
- **加权平均值**: 时间段内的平均值
- **总量**: 时间段内的总和

### 3. 特殊因子处理
- **pH因子**: 不统计总量（名称包含"ph"的因子）
- **累计流量因子**: 不统计最大、最小、平均值，总量取最后一个有效时段的数据值

### 4. 日报表特殊格式
- **行标题**: 24小时时段（00-01时、01-02时...23-24时）
- **列标题**: 统计因子 + 额外的"小时流量"因子（单位m³）
- **数据来源**: 小时数据（datatype = 小时数据）

## 核心类和枚举

### ePollutionReportPeriodType 枚举
```csharp
public enum ePollutionReportPeriodType
{
    Day = 0,      // 日报表
    Week = 1,     // 周报表  
    Month = 2,    // 月报表
    Quarter = 3,  // 季报表（新增）
    Year = 4      // 年报表
}
```

### StatisticsResult 结构
```csharp
private struct StatisticsResult
{
    public double Max { get; set; }
    public double Min { get; set; }
    public double Average { get; set; }
    public double Total { get; set; }
}
```

## 主要方法

### 报表生成方法
- `GenerateDayReport()`: 生成日报表数据
- `GeneratePeriodReport()`: 生成周期报表数据（周/月/季/年）

### 时间处理方法
- `SetQueryTimeRange()`: 根据周期类型设置查询时间范围
- `GetQuarterStart()`: 获取季度开始时间
- `GetTimeGroups()`: 按时间分组数据

### 统计计算方法
- `CalculateStatistics()`: 计算统计值（最大、最小、平均、总量）
- `IsPhFactor()`: 判断是否为pH因子
- `IsCumulativeFlowFactor()`: 判断是否为累计流量因子

### 界面控制方法
- `SetDataGridViewHead()`: 设置表格列头
- `UpdateDateSelectorVisibility()`: 更新日期选择器可见性
- `UpdateCurrentPeriodLabel()`: 更新当前周期标签

## 数据源配置

### 数据表
- **表名**: `DbConfig.POLLUTION_MEASURE_DATA_TABLE` ("pollution_measure_data")
- **时间字段**: datatime
- **数据类型字段**: datatype
- **因子代码字段**: factorcode  
- **因子值字段**: factorvalue

### 数据类型
- **小时数据**: `DbConfig.ePollutionDataType.小时数据` (用于日报表)
- **日数据**: `DbConfig.ePollutionDataType.日数据` (用于其他报表)

### 特殊因子
- **总流量**: `DbConfig.TOTAL_FLOW` ("total_flow")
- **因子前缀**: `DbConfig.PREFIX_F` ("F")
- **状态后缀**: `DbConfig.POSTFIX` ("_flag")

## 使用示例

### 创建报表控件
```csharp
// 使用指定的查询组创建报表
UC_DataReport reportControl = new UC_DataReport("污染源监测组");

// 设置报表标题
reportControl.TitleName = "污染数据统计报表";
```

### 查询日报表
```csharp
// 设置为日报表模式
_currentPeriodType = ePollutionReportPeriodType.Day;
_startTime = DateTime.Today;
_endTime = DateTime.Today.AddDays(1).AddSeconds(-1);

// 执行查询
btnStartQuery_Click(null, null);
```

### 查询月报表
```csharp
// 设置为月报表模式
_currentPeriodType = ePollutionReportPeriodType.Month;
_startTime = new DateTime(2024, 6, 1);
_endTime = new DateTime(2024, 6, 30, 23, 59, 59);

// 执行查询
btnStartQuery_Click(null, null);
```

## 界面控件要求

### 必需控件
- **DataGridView**: `dgvData` - 显示报表数据
- **时间周期选择**: 单选按钮组（rbDay, rbWeek, rbMonth, rbQuarter, rbYear）
- **时间选择器**: 根据周期类型显示不同的时间选择控件
- **查询按钮**: 触发报表生成
- **导出按钮**: 支持数据导出功能

### 可选控件
- **标题标签**: 显示报表标题
- **周期标签**: 显示当前选择的时间周期
- **进度提示**: 查询过程中的等待提示

## 扩展功能

### 季度查询支持
新增的季度查询功能支持：
- Q1: 1-3月
- Q2: 4-6月  
- Q3: 7-9月
- Q4: 10-12月

### 数据验证
- 时间范围有效性检查
- 查询因子配置检查
- 数据库连接状态检查
- 查询结果数量验证

### 错误处理
- 数据库查询异常处理
- 数据解析错误处理
- 界面操作异常处理
- 用户友好的错误提示

## 性能优化建议

1. **批量查询**: 使用单次SQL查询获取所有需要的数据
2. **数据缓存**: 对查询结果进行适当缓存
3. **分页显示**: 对大量数据实现分页显示
4. **异步处理**: 使用异步方法处理长时间查询
5. **索引优化**: 确保数据库表有适当的索引

## 测试文件

- `UC_DataReport_Test.cs`: 功能测试类
- `UC_DataReport_Usage_Example.cs`: 使用示例类

## 注意事项

1. 需要确保数据库连接配置正确
2. 查询因子配置需要预先设置
3. 界面控件需要正确绑定事件
4. 时间格式需要与数据库格式一致
5. 异常处理需要完善以提供良好的用户体验
