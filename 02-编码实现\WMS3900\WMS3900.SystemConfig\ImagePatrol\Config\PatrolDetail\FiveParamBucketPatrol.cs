﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 五参数水桶液位检测执行类
    /// </summary>
    public class FiveParamBucketPatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public FiveParamBucketPatrol()
        {
            UnitId = "FiveParamBucket";
            UnitName = "五参数水桶液位检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(ref ImageUnitPatrolResultBase patrolResult)
        {
            patrolResult = new FiveParamBucketPatrolResult();

            if(ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamBucketCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            // 拍照
            ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamBucketCamera.ScreenShot(out string picPath);

            // 算法分析
            AlgorithmHelper.CheckFiveParamBucketState(new Mat(picPath), out bool _, out bool wasteTankBucketWarn, out bool waterBucketWarn);

            ((FiveParamBucketPatrolResult)patrolResult).ImagePath = FileExtension.GetRelativePath(picPath);
            ((FiveParamBucketPatrolResult)patrolResult).FiveParamWasteTankBucketState = wasteTankBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
            ((FiveParamBucketPatrolResult)patrolResult).FiveParamWaterBucketState = waterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
        }

        #endregion
    }
}