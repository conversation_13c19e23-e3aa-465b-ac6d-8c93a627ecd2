﻿using System;
using System.ComponentModel;
using Fpi.WMS3000.SystemConfig.SmartPatrol;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 一个单元图像巡检基类
    /// 定义巡检方法
    /// </summary>
    public class ImageUnitSmartPatrolBase
    {
        #region 字段属性

        /// <summary>
        /// 单元编码
        /// </summary>
        [Description("单元编码")]
        public string UnitId;

        /// <summary>
        /// 单元名称
        /// </summary>
        [Description("单元名称")]
        public string UnitName;

        /// <summary>
        /// 描述信息
        /// </summary>
        [Description("描述信息")]
        public string Description;

        /// <summary>
        /// 巡检状态
        /// </summary>
        [Description("巡检状态")]
        public ePatrolState PatrolState;

        /// <summary>
        /// 巡检结果
        /// </summary>
        [Description("巡检结果")]
        public ImageUnitPatrolResultBase PatrolResult;

        #endregion

        #region 公共方法（待重写）

        /// <summary>
        /// 执行检测任务
        /// </summary>
        /// <returns></returns>
        public virtual void ExecutePatrol(ref ImageUnitPatrolResultBase patrolResult)
        {
        }

        #endregion

        #region 公共方法（待重写）

        /// <summary>
        /// 执行检测任务
        /// </summary>
        /// <returns></returns>
        public virtual void StartPatrol()
        {
            try
            {
                ImagePatrolManager.GetInstance().OnPatrolLogEvent($"[{UnitName}]开始检测...");
                PatrolState = ePatrolState.巡检中;

                ExecutePatrol(ref PatrolResult);

                PatrolState = ePatrolState.巡检完成;
                ImagePatrolManager.GetInstance().OnPatrolLogEvent($"[{UnitName}]检测完成。");
            }
            catch(Exception e)
            {
                ImagePatrolManager.GetInstance().OnPatrolLogEvent($"[{UnitName}]检测执行出错：{e.Message}");
                PatrolState = ePatrolState.巡检异常;
            }
        }

        #endregion
    }
}