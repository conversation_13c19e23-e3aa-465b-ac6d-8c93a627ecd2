﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 配水预处理沉砂池脏污检测执行类
    /// </summary>
    public class SandSinkPatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public SandSinkPatrol()
        {
            UnitId = "SandSink";
            UnitName = "配水预处理沉砂池脏污检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(ref ImageUnitPatrolResultBase patrolResult)
        {
            patrolResult = new SandSinkPatrolResult();

            if(ExterEquipConfigManager.GetInstance().CameraSelect.SandSinkCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            // 拍照
            ExterEquipConfigManager.GetInstance().CameraSelect.SandSinkCamera.ScreenShot(out string picPath);

            // 算法分析
            var state = AlgorithmHelper.CheckSandSinkSmutState(new Mat(picPath)) ? eSmutState.脏污 : eSmutState.正常;

            patrolResult.ImagePath = FileExtension.GetRelativePath(picPath);
            ((SandSinkPatrolResult)patrolResult).SandSinkSmutState = state;
        }

        #endregion
    }
}