﻿using System;
using System.Diagnostics;
using Fpi.Data.Config;
using Fpi.Devices;
using Fpi.Operations;
using Fpi.Operations.Interfaces;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.SystemConfig;
using Fpi.WMS3000.SystemOperation.Helper;

namespace Fpi.WMS3000.SystemOperation.OperationTemplate
{
    /// <summary>
    /// 全部测量配水操作模板
    /// </summary>
    public class Op_WaterDistribution : CustomOperation
    {
        #region 字段属性

        /// <summary>
        /// 是否初始化
        /// </summary>
        private bool _isInited;

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return "全部测量配水操作";
        }

        public override object CustomDo(string instrumentId, bool manual, object inputData, IOperationListener operationListener)
        {
            // 配水泵
            StateNode distributePump = null;
            // 配水时长
            int distributeTime = 0;

            try
            {
                if(!_isInited)
                {
                    InitProperty();
                }

                // 预处理浊度因子
                var turbNode = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.WaterTurbNode;

                // 判断二级预处理方式
                var currentTurbValue = (float)turbNode.GetValue();
                var turbLimit = ExterEquipConfigManager.GetInstance().WaterDistributeConfigInfo.TurbLimitValue;
                var secondaryMethod = eSecondaryPretreatmentMode.过滤;
                if(!float.IsNaN(currentTurbValue) && currentTurbValue > turbLimit)
                {
                    secondaryMethod = eSecondaryPretreatmentMode.旁路过滤;
                }

                // 更新缓存信息
                GlobalDataCache.GetInstance().WaterDistributionModuleData.SecondaryMethod = secondaryMethod;

                if(secondaryMethod == eSecondaryPretreatmentMode.过滤)
                {
                    SystemLogHelper.WriteOperationLog($"当前浊度为{currentTurbValue:F4}，低于浊度阈值{turbLimit:F4},开始粗滤配水");
                    distributePump = ExterEquipConfigManager.GetInstance().WaterDistributeConfigInfo.DiaphragmPumpNode;
                    distributeTime = ExterEquipConfigManager.GetInstance().WaterDistributeConfigInfo.CoarseFilterTime;
                }
                else
                {
                    SystemLogHelper.WriteOperationLog($"当前浊度为{currentTurbValue:F4}，低于浊度阈值{turbLimit:F4},开始细滤配水");
                    distributePump = ExterEquipConfigManager.GetInstance().WaterDistributeConfigInfo.PeristalticPumpNode;
                    distributeTime = ExterEquipConfigManager.GetInstance().WaterDistributeConfigInfo.FineFilterTime;
                }

                // 配水开始时间
                GlobalDataCache.GetInstance().WaterDistributionModuleData.DistributeStartTime = DateTime.Now;
                // 配水泵工作状态
                GlobalDataCache.GetInstance().WaterDistributionModuleData.DistributePumpWorkState = ePumpWorkState.空闲;

                // 启动配水泵
                OpHelper.StateNodeSwitch(distributePump, true);

                Stopwatch sp = new Stopwatch();
                sp.Start();

                // 配水指定时长
                while(sp.Elapsed.TotalSeconds < distributeTime)
                {
                    this.Sleep(instrumentId, 1000);
                }
                SystemLogHelper.WriteOperationLog("配水完成");

                // 判断指控装置样品杯状态，若有低液位报警，则配水状态异常
                bool waterCupLevelNormal = true;
                var resultStr = "配水状态正常";

                foreach(var device in DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.AUXILI))
                {
                    if(device is QCD3900Equip qcdDevice)
                    {
                        // 判断样品杯液位状态是否为无液
                        if(!qcdDevice.DeviceStateParams.WaterCupLevel)
                        {
                            waterCupLevelNormal = false;
                            resultStr = $"配水状态异常，{qcdDevice}水样杯液位不足。";
                            break;
                        }
                    }
                }

                // 修改配水状态
                if(waterCupLevelNormal)
                {
                    GlobalDataCache.GetInstance().WaterDistributionModuleData.WaterDistributeState = eWaterDistributeState.正常;
                }
                else
                {
                    GlobalDataCache.GetInstance().WaterDistributionModuleData.WaterDistributeState = eWaterDistributeState.质控杯水样不足;
                }

                SystemLogHelper.WriteOperationLog($"[{GetOperationName()}]操作完成，{resultStr}。");
            }
            catch(Exception ex)
            {
                SystemLogHelper.WriteSystemErrorLog($"[{GetOperationName()}]操作异常: {ex.Message}!");
                SystemLogHelper.WriteOperationLog($"[{GetOperationName()}]操作异常: {ex.Message}!");
            }
            finally
            {
                // 配水结束时间
                GlobalDataCache.GetInstance().WaterDistributionModuleData.DistributeStopTime = DateTime.Now;
                // 配水泵工作状态
                GlobalDataCache.GetInstance().WaterDistributionModuleData.DistributePumpWorkState = ePumpWorkState.空闲;

                // 关闭配水泵
                OpHelper.StateNodeSwitch(distributePump, false);

                // 序列化数据缓存
                GlobalDataCache.GetInstance().Save();
            }

            // 记录配水日志信息
            try
            {
                DateTime sampleTime = SystemHelper.SampleTime;
                if(sampleTime == DateTime.MinValue)
                {
                    sampleTime = DateTime.Now;
                }

                SaveDataHelper.WriteWaterDistributeDataToDb(
                    sampleTime,
                    GlobalDataCache.GetInstance().WaterDistributionModuleData.DistributeStartTime,
                    GlobalDataCache.GetInstance().WaterDistributionModuleData.DistributeStopTime,
                    (int)GlobalDataCache.GetInstance().WaterDistributionModuleData.DistributeWaitingTime,
                    (int)GlobalDataCache.GetInstance().WaterDistributionModuleData.SecondaryMethod,
                    (int)GlobalDataCache.GetInstance().WaterDistributionModuleData.WaterDistributeState);
            }
            catch
            {
            }

            return null;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查参数设置是否合法
        /// </summary>
        private void InitProperty()
        {
            ExterEquipConfigManager.GetInstance().WaterDistributeConfigInfo.Check();

            _isInited = true;
        }

        #endregion
    }
}