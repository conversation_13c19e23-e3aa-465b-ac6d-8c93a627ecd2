﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows.Forms;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.Interface;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_DeviceCurrentLogQuery : UIUserControl, IRefreshUI
    {
        #region 字段属性

        #region 自定义属性

        /// <summary>
        /// 是否显示刷新按钮
        /// </summary>
        [Category("FPI自定义属性"), Description("是否显示刷新按钮"), Localizable(true)]
        public bool CanRefresh { get; set; } = true;

        #endregion

        /// <summary>
        /// 日志记录
        /// </summary>
        private Queue<DeviceLogInfo> _logInfos;

        #endregion

        #region 构造

        public UC_DeviceCurrentLogQuery()
        {
            InitializeComponent();

            foreach(DataGridViewColumn column in this.dgvLogInfo.Columns)
            {
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }
        }

        #endregion

        #region 事件

        /// <summary>
        /// 刷新按钮可见性赋值
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void UC_DeviceCurrentLogQuery_Paint(object sender, PaintEventArgs e)
        {
            btnRefresh.Visible = CanRefresh;
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        #endregion

        #region 公共方法

        internal void SetRelatedLogs(Queue<DeviceLogInfo> logInfos)
        {
            _logInfos = logInfos;
            RefreshUI();
        }

        #endregion

        #region IRefreshUI

        public void RefreshUI()
        {
            if(_logInfos != null)
            {
                dgvLogInfo.Rows.Clear();

                int rowIndex = 1;
                foreach(var logInfo in _logInfos.ToList().OrderByDescending(x => x.LogTime))
                {
                    int index = dgvLogInfo.Rows.Add();
                    DataGridViewRow dr = dgvLogInfo.Rows[index];
                    dr.Tag = logInfo;
                    dr.Cells[0].Value = rowIndex++;
                    dr.Cells[1].Value = logInfo.LogTime.ToString(DbConfig.DATETIME_FORMAT);
                    dr.Cells[2].Value = logInfo.LogType.ToString();
                    dr.Cells[3].Value = logInfo.LogInfo;
                    dr.Cells[3].ToolTipText = logInfo.LogInfo;
                    dr.Cells[3].Style.Alignment = DataGridViewContentAlignment.MiddleLeft;
                }
            }
        }

        #endregion
    }
}