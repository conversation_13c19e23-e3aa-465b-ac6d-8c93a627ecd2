﻿using System.ComponentModel;
using Fpi.Json;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 公共数据缓存
    /// 数据需要序列化存储
    /// 软件关闭时保存，并且固定时间间隔也存储（或者流程结束存储）
    /// 启动时读取最新一次缓存
    /// </summary>
    public class GlobalDataCache : BaseJsonNode
    {
        #region 属性

        /// <summary>
        /// 采水单元数据
        /// </summary>
        [Description("采水单元数据")]
        public WaterCollectionModuleState WaterCollectionModuleData { get; } = new WaterCollectionModuleState();

        /// <summary>
        /// 预处理单元数据
        /// </summary>
        [Description("预处理单元数据")]
        public WaterPretreatmentModuleState WaterPretreatmentModuleData { get; } = new WaterPretreatmentModuleState();

        /// <summary>
        /// 配水单元数据
        /// </summary>
        [Description("配水单元数据")]
        public WaterDistributionModuleState WaterDistributionModuleData { get; } = new WaterDistributionModuleState();

        /// <summary>
        /// 站房环境单元数据
        /// </summary>
        [Description("站房环境单元数据")]
        public StationModuleState StationModuleData { get; } = new StationModuleState();

        /// <summary>
        /// 视频算法单元数据
        /// </summary>
        [Description("视频算法单元数据")]
        public VideoAlgorithmModuleState VideoAlgorithmModuleStateData { get; } = new VideoAlgorithmModuleState();

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static GlobalDataCache _instance = null;
        public static GlobalDataCache GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new GlobalDataCache();
                }
            }
            return _instance;
        }

        private GlobalDataCache()
        {
            loadJson();
        }

        #endregion
    }
}