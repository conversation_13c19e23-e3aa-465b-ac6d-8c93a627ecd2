﻿using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;
using Sunny.UI;

namespace Fpi.WMS3000.UI.ExterDevices
{
    /// <summary>
    /// 视频监控模块
    /// </summary>
    public partial class UC_VssModuleState : UIUserControl, IRefreshUI
    {
        #region 字段属性

        private const string ErrorInfo = "— — — —";

        private const string DisplayFormat = "F2";

        #endregion

        #region 构造

        public UC_VssModuleState()
        {
            InitializeComponent();
        }

        #endregion

        #region IRefreshUI

        public void RefreshUI()
        {
            if(GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData != null)
            {
                lblWaterTrapMigration.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapIntrusion.ToString();
                lblWaterTrapFloat.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapFloat.ToString();
                lblWaterTrapIntrusion.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapIntrusion.ToString();
                lblWaterTrapWaterColor.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapWaterColor.ToString();
                lblWaterTrapWaterLevel.Text = double.IsNaN(GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.WaterTrapWaterLevel)
                    ? ErrorInfo
                    : GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData?.WaterTrapWaterLevel.ToString(DisplayFormat);
                lblStationOutsideIntrusion.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.StationOutsideIntrusion.ToString();
                lblStationInsideIntrusion.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.StationInsideIntrusion.ToString();
                lblUndressedSuit.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.UndressedSuit.ToString();
                lblSmoking.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.Smoking.ToString();
                lblMaterialStacking.Text = GlobalDataCache.GetInstance().VideoAlgorithmModuleStateData.MaterialStacking.ToString();
            }
            else
            {
                lblWaterTrapMigration.Text = ErrorInfo;
                lblWaterTrapFloat.Text = ErrorInfo;
                lblWaterTrapIntrusion.Text = ErrorInfo;
                lblWaterTrapWaterColor.Text = ErrorInfo;
                lblWaterTrapWaterLevel.Text = ErrorInfo;
                lblStationOutsideIntrusion.Text = ErrorInfo;
                lblStationInsideIntrusion.Text = ErrorInfo;
                lblUndressedSuit.Text = ErrorInfo;
                lblSmoking.Text = ErrorInfo;
                lblMaterialStacking.Text = ErrorInfo;
            }
        }

        #endregion
    }
}