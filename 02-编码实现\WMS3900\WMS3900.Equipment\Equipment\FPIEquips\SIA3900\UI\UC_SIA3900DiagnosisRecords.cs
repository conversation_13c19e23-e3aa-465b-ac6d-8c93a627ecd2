﻿using System;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.SIA3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900DiagnosisRecords : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        private const string ErrorInfo = "— — — — —";

        #endregion

        #region 构造

        public UC_SIA3900DiagnosisRecords()
        {
            InitializeComponent();
            EnumOperate.BandEnumToCmb(cmbDeviceCheckType, typeof(eSIA3900DeviceCheckType));
        }

        #endregion

        #region 事件

        private void btnRefresh_Click(object sender, System.EventArgs e)
        {
            RefreshUI();
        }

        /// <summary>
        /// 器件自检类型设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeviceCheckTypeSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(cmbDeviceCheckType.SelectedIndex == -1)
                {
                    throw new Exception("请选择器件自检类型！");
                }
                _device.WriteDeviceCheck((eSIA3900DeviceCheckType)cmbDeviceCheckType.SelectedValue);

                FpiMessageBox.ShowInfo($"执行器件自检成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void btnSelfCheck_Click(object sender, EventArgs e)
        {
            try
            {
                _device.StartOper(eMeasureDeviceOperType.SelfCheck);

                FpiMessageBox.ShowInfo($"触发设备自诊断成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(SIA3900Equipment device)
        {
            _device = device;
            uc_DiagnosisQueryData.SetTragetDevice(_device);
            RefreshUI();
        }

        #endregion

        #region 私有方法


        private void RefreshUI()
        {
            if(_device.DiagnosisRecords != null)
            {
                // 最新一条诊断记录
                lblCurrentTime.Text = _device.DiagnosisRecords.TimeStamp == DateTime.MinValue ? ErrorInfo :
                _device.DiagnosisRecords.TimeStamp.ToString(DbConfig.DATETIME_FORMAT);
                if(Enum.IsDefined(typeof(eSIA3900DiagnosisType), _device.DiagnosisRecords.CurrentTask))
                {
                    lblCurrentTask.Text = _device.DiagnosisRecords.CurrentTask.ToString();
                }

                if(Enum.IsDefined(typeof(eSIA3900DiagnosisResult), _device.DiagnosisRecords.DiagnosisResult))
                {
                    lblCurrentResult.Text = _device.DiagnosisRecords.DiagnosisResult.ToString();
                }
            }
        }

        #endregion

    }
}
