﻿using System.ComponentModel;
using Fpi.Alarm;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 系统报警帮助类
    /// </summary>
    public static class SystemAlarmHelper
    {
        #region 字段属性

        /// <summary>
        /// 系统报警组Id
        /// </summary>
        [Description("系统报警组Id")]
        public const string SystemAlarmGroupId = "SYS";

        /// <summary>
        /// 系统报警源Id
        /// </summary>
        [Description("系统报警源Id")]
        public const string SystemAlarmSourceId = "SYS_Alarm";

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加系统报警
        /// </summary>
        /// <param name="alarmCodeId"></param>
        public static void AddSystemAlarm(string alarmCodeId)
        {
            AlarmManager.GetInstance().AddAlarm(SystemAlarmSourceId, alarmCodeId);
        }

        /// <summary>
        /// 移除系统报警
        /// </summary>
        /// <param name="alarmCodeId"></param>
        public static void RemoveSystemAlarm(string alarmCodeId)
        {
            AlarmManager.GetInstance().RemoveAlarm(SystemAlarmSourceId, alarmCodeId);
        }

        /// <summary>
        /// 获取系统报警组
        /// </summary>
        public static AlarmGroup GetSystemAlarmGroup()
        {
            return AlarmManager.GetInstance().alarmGroups.FindNode(SystemAlarmGroupId) as AlarmGroup;
        }

        /// <summary>
        /// 获取系统报警源
        /// </summary>
        public static AlarmSource GetSystemAlarmSource()
        {
            return AlarmManager.GetInstance().alarmSources.FindNode(SystemAlarmSourceId) as AlarmSource;
        }

        #endregion
    }
}
