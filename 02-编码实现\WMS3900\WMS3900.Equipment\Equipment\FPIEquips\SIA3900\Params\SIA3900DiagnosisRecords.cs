﻿using System;
using System.ComponentModel;
using System.Data;
using Fpi.Communication.Converter;
using Fpi.DB;
using Fpi.DB.Manager;
using Fpi.WMS3000.DB;

namespace Fpi.WMS3000.Equipment.SIA3900
{
    /// <summary>
    /// 诊断记录
    /// </summary>
    public class SIA3900DiagnosisRecords
    {
        #region 字段属性

        /// <summary>
        /// 时间戳
        /// </summary>
        [Description("诊断时间")]
        public DateTime TimeStamp { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 任务类型
        /// </summary>
        [Description("任务类型")]
        public eSIA3900DiagnosisType CurrentTask { get; set; } = (eSIA3900DiagnosisType)(-1);

        /// <summary>
        /// 诊断结果
        /// </summary>
        [Description("诊断结果")]
        public eSIA3900DiagnosisResult DiagnosisResult { get; set; } = (eSIA3900DiagnosisResult)(-1);

        #endregion

        #region 公有方法

        /// <summary>
        /// 更新当前设备自检信息
        /// </summary>
        /// <param name="data"></param>
        /// <param name="startIndex"></param>
        /// <exception cref="Exception"></exception>
        public void UpdataValue(byte[] data, int startIndex, string deviceid)
        {
            if(data.Length < startIndex + 8)
            {
                throw new Exception("读取诊断记录回应数据不完整！");
            }

            SIA3900DiagnosisRecords CurrentDeviceDiagnosisResult = new()
            {
                TimeStamp = DataConvertHelper.GetDateTimeFromUnixTimeSeconds2143(data, startIndex),
                CurrentTask = (eSIA3900DiagnosisType)DataConverter.GetInstance().ToInt32(data, startIndex + 4),
                DiagnosisResult = (eSIA3900DiagnosisResult)DataConverter.GetInstance().ToInt32(data, startIndex + 6)
            };

            // 判断是否为新数据         
            if(this.TimeStamp != CurrentDeviceDiagnosisResult.TimeStamp)
            {
                try
                {
                    // 新数据入数据库
                    CurrentDeviceDiagnosisResult.SaveToDb(deviceid);
                }
                catch
                {
                }
                this.TimeStamp = CurrentDeviceDiagnosisResult.TimeStamp;
                this.CurrentTask = CurrentDeviceDiagnosisResult.CurrentTask;
                this.DiagnosisResult = CurrentDeviceDiagnosisResult.DiagnosisResult;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 保存数据到数据库中
        /// </summary>
        /// <param name="deviceId"></param>
        private void SaveToDb(string deviceId)
        {
            if(!string.IsNullOrWhiteSpace(deviceId) && TimeStamp != DateTime.MinValue)
            {
                // 判断数据库中是否有同一时间的数据
                string strSql = $"select * from {DbConfig.DEVICE_DIAGNOSISRECORDS_TABLE}  where datatime='{TimeStamp.ToString(DbConfig.DATETIME_FORMAT)}' ";
                IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql);
                if(!reader.Read())
                {
                    WriteDeviceLogToDb(deviceId, this);
                }
            }
        }

        private static readonly object lockObj = new();

        /// <summary>
        /// 写诊断记录到数据库
        /// </summary>
        /// <param name="time"></param>
        /// <param name="sourceId"></param>
        /// <param name="info"></param>
        private static void WriteDeviceLogToDb(string sourceId, SIA3900DiagnosisRecords info)
        {
            FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.DEVICE_DIAGNOSISRECORDS_TABLE) ?? throw new Exception("设备诊断记录表不存在！");
            lock(lockObj)
            {
                FpiRow row = new FpiRow();
                row.SetFieldValue("sourceid", sourceId);
                row.SetFieldValue("datatime", info.TimeStamp);
                row.SetFieldValue("task", info.CurrentTask);
                row.SetFieldValue("result", info.DiagnosisResult);
                table.AddRecord(row);
            }
        }

        #endregion
    }
}