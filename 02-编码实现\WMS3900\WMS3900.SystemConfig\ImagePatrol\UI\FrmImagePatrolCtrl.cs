﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.HB.Business.Tasks;
using Fpi.Timers.UI.PC;
using Fpi.Util.ThreadRelated;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.SystemConfig.ImagePatrol;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol;
using Fpi.WMS3000.SystemConfig.UI;
using Sunny.UI;
using Timer = Fpi.Timers.Timer;

namespace Fpi.WMS3000.SystemConfig
{
    /// <summary>
    /// 图像巡检控制界面
    /// </summary>
    public partial class FrmImagePatrolCtrl : UIForm
    {
        #region 构造

        public FrmImagePatrolCtrl()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmImagePatrolCtrl_Load(object sender, EventArgs e)
        {
            // 初始化界面控件
            InitCtrl();

            // 根据当前巡检情况（是否进行中）设置各控件值
            SetCtrlInfo();

            // 初始化事件绑定
            InitEvent();
        }

        /// <summary>
        /// 关闭界面时解绑通知事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmImagePatrolCtrl_FormClosing(object sender, FormClosingEventArgs e)
        {
            ImagePatrolManager.GetInstance().PatrolStartEvent -= FrmImagePatrolCtrl_PatrolStartEvent;
            ImagePatrolManager.GetInstance().PatrolEndEvent -= FrmImagePatrolCtrl_PatrolEndEvent;
            ImagePatrolManager.GetInstance().PatrolLogEvent -= FrmImagePatrolCtrl_PatrolLogEvent;
            ImagePatrolManager.GetInstance().SingleUnitPatrolStartEvent -= FrmImagePatrolCtrl_SingleUnitPatrolStartEvent;
            ImagePatrolManager.GetInstance().SingleUnitPatrolEndEvent -= FrmImagePatrolCtrl_SingleUnitPatrolEndEvent;
        }

        /// <summary>
        /// 开始/停止
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartDo_Click(object sender, EventArgs e)
        {
            if(btnStartDo.Text == "开始")
            {
                // 触发执行
                var task = ImagePatrolManager.GetInstance().StartDoPatrol(eImagePatrolTriggerType.手动触发);
                if(task != null)
                {
                    // 记录系统操作日志
                    SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"触发执行图像巡检", eOpType.控制操作, eOpStyle.本地操作));

                    btnStartDo.Text = "停止";

                    btnOpenResult.Enabled = btnTimerConfig.Enabled = false;
                }
            }
            else
            {
                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"触发停止图像巡检", eOpType.控制操作, eOpStyle.本地操作));

                // 触发停止
                ImagePatrolManager.GetInstance().StopDoPatrol();
            }
        }

        /// <summary>
        /// 查看结果
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOpenResult_Click(object sender, EventArgs e)
        {
            if(ImagePatrolManager.GetInstance().LatestImagePatrolResult is ImagePatrolResult result)
            {
                new FrmImagePatrolResultShow(result).ShowDialog();
            }
        }

        /// <summary>
        /// 图像巡检定时配置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTimerConfig_Click(object sender, EventArgs e)
        {
            // 标志任务是否是本次新增的
            bool newAdd = false;
            if(CustomTaskManager.GetInstance().GetCustomTaskByType(typeof(ImagePatrolTask)) is not ImagePatrolTask imagePatrolTask)
            {
                newAdd = true;
                imagePatrolTask = new ImagePatrolTask();
                imagePatrolTask.id = "ImagePatrolTask";
                imagePatrolTask.name = "图像巡检任务";
                imagePatrolTask.Description = "图像巡检任务";
                imagePatrolTask.IsValid = true;

                if(imagePatrolTask.timer == null)
                {
                    imagePatrolTask.timer = new Timer { description = "图像巡检任务定时器" };
                }
            }

            FormConfigTimerUser timerForm = new FormConfigTimerUser(imagePatrolTask.timer);
            if(timerForm.ShowDialog() == DialogResult.OK)
            {
                imagePatrolTask.timer = timerForm.Timer;
                if(imagePatrolTask.timer != null)
                {
                    imagePatrolTask.timer.description = "图像巡检任务定时器";
                }

                if(newAdd)
                {
                    CustomTaskManager.GetInstance().CustomTasks.AddNode(imagePatrolTask);
                }

                CustomTaskManager.GetInstance().Save();
            }
        }

        private void btnParamConfig_Click(object sender, EventArgs e)
        {
            var form = new FrmImagePatrolConfig();
            if(form.ShowDialog() == DialogResult.OK)
            {
                // 更新巡检内容项
                InitCtrl();
            }
        }

        #region 事件实现

        /// <summary>
        /// 巡检流程开始
        /// </summary>
        private void FrmImagePatrolCtrl_PatrolStartEvent()
        {
            var result = ImagePatrolManager.GetInstance().LatestImagePatrolResult;
            ThreadHelper.UpdateControlText(lblStartTime, result.StartTime.ToString("yyyy-MM-dd HH:mm:ss"));
            ThreadHelper.UpdateControlText(lblTriggerType, result.PatrolTriggerType.ToString());
            ThreadHelper.UpdateControlText(txtResultInfo, $"巡检开始时间：{result.StartTime:yyyy-MM-dd HH:mm:ss}，触发方式：{result.PatrolTriggerType}\r\n");
            ThreadHelper.UpdateControlText(lblPatrolState, ImagePatrolManager.GetInstance().PatrolState.ToString());
            ThreadHelper.UpdateGifAvatarActive(gifAvatar, true);
        }

        /// <summary>
        /// 巡检流程结束
        /// </summary>
        private void FrmImagePatrolCtrl_PatrolEndEvent()
        {
            ThreadHelper.UpdateControlText(btnStartDo, "开始");
            ThreadHelper.UpdateControlEnable(btnOpenResult, true);
            ThreadHelper.UpdateControlEnable(btnTimerConfig, true);
            ThreadHelper.UpdateGifAvatarActive(gifAvatar, false);
            ThreadHelper.UpdateUCStepStep(stepCtrl, stepCtrl.Steps.Length);
            ThreadHelper.UpdateControlText(lblPatrolState, ImagePatrolManager.GetInstance().PatrolState.ToString());
        }

        /// <summary>
        /// 巡检日志通知
        /// </summary>
        /// <param name="info"></param>
        private void FrmImagePatrolCtrl_PatrolLogEvent(string info)
        {
            ThreadHelper.AppendText(txtResultInfo, $"{info}\r\n");
        }

        /// <summary>
        /// 模块巡检开始
        /// </summary>
        /// <param name="model"></param>
        /// <param name="step"></param>
        private void FrmImagePatrolCtrl_SingleUnitPatrolEndEvent(ImageUnitSmartPatrolBase model, int step)
        {
            ThreadHelper.UpdateUCStepStep(stepCtrl, step);
        }

        /// <summary>
        /// 模块巡检结束
        /// </summary>
        /// <param name="model"></param>
        /// <param name="step"></param>
        private void FrmImagePatrolCtrl_SingleUnitPatrolStartEvent(ImageUnitSmartPatrolBase model, int step)
        {
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化界面控件
        /// </summary>
        private void InitCtrl()
        {
            // 步骤表
            List<string> stepList = new List<string>();
            stepList.Add("开始");
            foreach(var model in ImagePatrolManager.GetInstance().PatrolUnitList)
            {
                stepList.Add(model.UnitName.TrimEnd('测', '检', '态', '状', '脏', '污', '液', '位'));
            }
            stepList.Add("结束");
            stepCtrl.Steps = stepList.ToArray();
            stepCtrl.StepIndex = 0;
        }

        /// <summary>
        /// 根据当前巡检情况（是否进行中）设置各控件值
        /// </summary>
        private void SetCtrlInfo()
        {
            // 巡检进度
            lblPatrolState.Text = ImagePatrolManager.GetInstance().PatrolState.ToString();

            if(ImagePatrolManager.GetInstance().PatrolState == ePatrolState.巡检中)
            {
                // 进度表
                stepCtrl.StepIndex = ImagePatrolManager.GetInstance().CurrnetUnitIndex;
                // gif动画
                gifAvatar.Active = true;
                // 开始时间
                lblStartTime.Text = ImagePatrolManager.GetInstance().LatestImagePatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                // 触发方式
                lblTriggerType.Text = ImagePatrolManager.GetInstance().LatestImagePatrolResult.PatrolTriggerType.ToString();
                // 启动按钮
                btnStartDo.Text = "停止";
                // 查看结果
                btnOpenResult.Enabled = false;
                // 自动巡检定时配置
                btnTimerConfig.Enabled = false;
            }
            else if(ImagePatrolManager.GetInstance().PatrolState == ePatrolState.未开始)
            {
                // 查看结果
                btnOpenResult.Enabled = false;
            }
            // 巡检完成
            // 巡检异常
            else
            {
                // 开始时间
                lblStartTime.Text = ImagePatrolManager.GetInstance().LatestImagePatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                // 触发方式
                lblTriggerType.Text = ImagePatrolManager.GetInstance().LatestImagePatrolResult.PatrolTriggerType.ToString();
            }
        }

        /// <summary>
        /// 初始化事件绑定
        /// </summary>
        private void InitEvent()
        {
            ImagePatrolManager.GetInstance().PatrolStartEvent += FrmImagePatrolCtrl_PatrolStartEvent;
            ImagePatrolManager.GetInstance().PatrolEndEvent += FrmImagePatrolCtrl_PatrolEndEvent;
            ImagePatrolManager.GetInstance().PatrolLogEvent += FrmImagePatrolCtrl_PatrolLogEvent;
            ImagePatrolManager.GetInstance().SingleUnitPatrolStartEvent += FrmImagePatrolCtrl_SingleUnitPatrolStartEvent;
            ImagePatrolManager.GetInstance().SingleUnitPatrolEndEvent += FrmImagePatrolCtrl_SingleUnitPatrolEndEvent;
        }

        #endregion
    }
}