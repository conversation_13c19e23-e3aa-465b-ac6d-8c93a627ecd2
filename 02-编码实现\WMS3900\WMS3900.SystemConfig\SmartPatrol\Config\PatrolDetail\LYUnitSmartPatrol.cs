﻿using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 留样单元巡检执行类
    /// </summary>
    public class LYUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public LYUnitSmartPatrol()
        {
            UnitId = "SampleRetention";
            UnitName = "留样单元";
            Description = "水站版";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            LYUnitPatrolResult result = new LYUnitPatrolResult();

            if(ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice is HDZSC_VIISampleEquip sampleEquip)
            {
                result.ComState = sampleEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
                result.WorkState = eModuleWorkingState.正常;
                result.SampleNum = sampleEquip.SampleNum;
            }

            return result;
        }

        #endregion
    }
}