using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// UC_DataReport使用示例
    /// 展示如何在实际项目中使用污染数据统计报表功能
    /// </summary>
    public class UC_DataReport_Usage_Example
    {
        /// <summary>
        /// 示例：创建日报表
        /// </summary>
        public static void CreateDayReportExample()
        {
            Console.WriteLine("=== 日报表创建示例 ===");
            
            // 1. 创建报表控件实例
            // UC_DataReport reportControl = new UC_DataReport("污染源监测组");
            
            // 2. 设置报表参数
            var reportDate = DateTime.Today; // 今天的日报表
            Console.WriteLine($"生成日期: {reportDate:yyyy年MM月dd日}");
            
            // 3. 模拟24小时时段数据结构
            var hourlyTimeSlots = new List<string>();
            for(int hour = 0; hour < 24; hour++)
            {
                hourlyTimeSlots.Add($"{hour:D2}-{(hour + 1):D2}时");
            }
            
            Console.WriteLine("时段列表:");
            foreach(var timeSlot in hourlyTimeSlots)
            {
                Console.WriteLine($"  {timeSlot}");
            }
            
            // 4. 模拟统计因子
            var factors = new[]
            {
                new { Code = "w01001", Name = "pH值", Unit = "" },
                new { Code = "w01009", Name = "COD", Unit = "mg/L" },
                new { Code = "w01018", Name = "氨氮", Unit = "mg/L" },
                new { Code = "w01019", Name = "总磷", Unit = "mg/L" },
                new { Code = "e1204", Name = "累计流量", Unit = "m³" }
            };
            
            Console.WriteLine("\n统计因子:");
            foreach(var factor in factors)
            {
                Console.WriteLine($"  {factor.Code}: {factor.Name} ({factor.Unit})");
                
                // pH因子不统计总量
                if(factor.Name.ToLower().Contains("ph"))
                {
                    Console.WriteLine($"    -> 统计项: 最大值、最小值、平均值");
                }
                // 累计流量因子只统计总量
                else if(factor.Name.Contains("累计流量"))
                {
                    Console.WriteLine($"    -> 统计项: 总量（取最后有效值）");
                }
                else
                {
                    Console.WriteLine($"    -> 统计项: 最大值、最小值、平均值、总量");
                }
            }
            
            Console.WriteLine("\n额外列: 小时流量(m³)");
        }

        /// <summary>
        /// 示例：创建月报表
        /// </summary>
        public static void CreateMonthReportExample()
        {
            Console.WriteLine("\n=== 月报表创建示例 ===");
            
            var reportMonth = new DateTime(2024, 6, 1); // 2024年6月
            Console.WriteLine($"报表月份: {reportMonth:yyyy年MM月}");
            
            // 月报表使用日数据进行统计
            Console.WriteLine("数据源: 日数据 (datatype = 日数据)");
            Console.WriteLine("时间范围: 2024-06-01 00:00:00 至 2024-06-30 23:59:59");
            
            // 模拟月度统计行
            var monthlyRows = new List<string>();
            for(int day = 1; day <= 30; day++)
            {
                monthlyRows.Add($"2024年06月{day:D2}日");
            }
            
            Console.WriteLine($"统计行数: {monthlyRows.Count}行");
            Console.WriteLine("每行包含各因子的最大值、最小值、平均值、总量统计");
        }

        /// <summary>
        /// 示例：创建季报表
        /// </summary>
        public static void CreateQuarterReportExample()
        {
            Console.WriteLine("\n=== 季报表创建示例 ===");
            
            var reportQuarter = new DateTime(2024, 4, 15); // 2024年第2季度
            int quarter = (reportQuarter.Month - 1) / 3 + 1;
            Console.WriteLine($"报表季度: {reportQuarter.Year}年第{quarter}季度");
            
            // 季度时间范围计算
            var quarterStart = new DateTime(2024, 4, 1); // Q2开始
            var quarterEnd = new DateTime(2024, 6, 30, 23, 59, 59); // Q2结束
            Console.WriteLine($"时间范围: {quarterStart:yyyy-MM-dd HH:mm:ss} 至 {quarterEnd:yyyy-MM-dd HH:mm:ss}");
            
            Console.WriteLine("数据源: 日数据 (datatype = 日数据)");
            Console.WriteLine("统计粒度: 按月分组统计");
            
            var quarterlyRows = new[] { "2024年04月", "2024年05月", "2024年06月" };
            Console.WriteLine($"统计行: {string.Join(", ", quarterlyRows)}");
        }

        /// <summary>
        /// 示例：报表列头结构
        /// </summary>
        public static void ShowReportColumnStructure()
        {
            Console.WriteLine("\n=== 报表列头结构示例 ===");
            
            var factors = new[]
            {
                new { Name = "pH值", Unit = "" },
                new { Name = "COD", Unit = "mg/L" },
                new { Name = "氨氮", Unit = "mg/L" }
            };
            
            Console.WriteLine("列头结构:");
            Console.WriteLine("1. 时间列 (时段/日期/月份等)");
            
            int columnIndex = 2;
            foreach(var factor in factors)
            {
                Console.WriteLine($"{columnIndex++}. {factor.Name}最大值({factor.Unit})");
                Console.WriteLine($"{columnIndex++}. {factor.Name}最小值({factor.Unit})");
                Console.WriteLine($"{columnIndex++}. {factor.Name}平均值({factor.Unit})");
                
                if(!factor.Name.ToLower().Contains("ph"))
                {
                    Console.WriteLine($"{columnIndex++}. {factor.Name}总量({factor.Unit})");
                }
            }
            
            Console.WriteLine($"{columnIndex}. 小时流量(m³) [仅日报表]");
        }

        /// <summary>
        /// 示例：数据查询SQL结构
        /// </summary>
        public static void ShowSqlQueryStructure()
        {
            Console.WriteLine("\n=== 数据查询SQL结构示例 ===");
            
            Console.WriteLine("日报表查询 (小时数据):");
            Console.WriteLine("SELECT * FROM pollution_measure_data");
            Console.WriteLine("WHERE datatime >= '2024-06-15 00:00:00'");
            Console.WriteLine("  AND datatime <= '2024-06-15 23:59:59'");
            Console.WriteLine("  AND datatype = 3  -- 小时数据");
            Console.WriteLine("ORDER BY datatime ASC");
            
            Console.WriteLine("\n其他报表查询 (日数据):");
            Console.WriteLine("SELECT * FROM pollution_measure_data");
            Console.WriteLine("WHERE datatime >= '2024-06-01 00:00:00'");
            Console.WriteLine("  AND datatime <= '2024-06-30 23:59:59'");
            Console.WriteLine("  AND datatype = 4  -- 日数据");
            Console.WriteLine("ORDER BY datatime ASC");
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static void RunAllExamples()
        {
            Console.WriteLine("UC_DataReport 使用示例演示\n");
            
            CreateDayReportExample();
            CreateMonthReportExample();
            CreateQuarterReportExample();
            ShowReportColumnStructure();
            ShowSqlQueryStructure();
            
            Console.WriteLine("\n示例演示完成！");
            Console.WriteLine("\n使用说明:");
            Console.WriteLine("1. 在界面上选择时间周期类型（日/周/月/季/年）");
            Console.WriteLine("2. 设置对应的时间范围");
            Console.WriteLine("3. 点击查询按钮生成报表");
            Console.WriteLine("4. 系统会自动根据周期类型查询相应的数据并进行统计计算");
            Console.WriteLine("5. 结果显示在DataGridView中，支持导出功能");
        }
    }
}
