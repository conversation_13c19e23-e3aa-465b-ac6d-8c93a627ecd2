﻿using System;
using System.ComponentModel;
using System.Reflection;
using System.Windows.Forms;
using Fpi.WMS3000.Equipment.Common.CustomAttribute;
using Fpi.WMS3000.Equipment.Common.UI;

namespace Fpi.WMS3000.Equipment
{
    public static class CommonFunctionHelper
    {
        /// <summary>
        /// 通过反射得到类属性值，展示在控件上
        /// </summary>
        /// <param name="pnlMain"></param>
        /// <param name="objParams"></param>
        public static void ReflectAttributeToUI(FlowLayoutPanel pnlMain, object objParams)
        {
            pnlMain.Controls.Clear();

            Type type = objParams.GetType();
            PropertyInfo[] propertyInfos = type.GetProperties();
            foreach(var propertyInfo in propertyInfos)
            {
                // 当前属性是否显示
                bool visible = true;

                // 获取属性的所有特性
                object[] attributes = propertyInfo.GetCustomAttributes(false);
                // 遍历，检查是否有特性描述不显示当前属性
                foreach(object attr in attributes)
                {
                    if(attr.GetType() == typeof(VisibleAttribute) && !((VisibleAttribute)attr).Visible)
                    {
                        visible = false;
                        ;
                    }
                }

                // 当前属性不显示
                if(!visible)
                {
                    continue;
                }

                object value = propertyInfo.GetValue(objParams, null);

                foreach(object obj in attributes)
                {
                    if(obj is DescriptionAttribute)
                    {
                        string str = (obj as DescriptionAttribute).Description;
                        var paramPanle = new UC_OneParamData(str, value);

                        pnlMain.Controls.Add(paramPanle);
                    }
                }
            }
        }
    }
}