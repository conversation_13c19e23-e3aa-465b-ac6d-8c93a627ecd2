﻿using System;
using System.Linq;
using Fpi.Devices;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 试剂存储单元巡检执行类
    /// </summary>
    public class SJUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public SJUnitSmartPatrol()
        {
            UnitId = "ReagentStorage";
            UnitName = "试剂存储单元";
            Description = "水站版";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            SJUnitPatrolResult result = new SJUnitPatrolResult();

            foreach(var siaEquip in DeviceManager.GetInstance().GetDeviceListUsed().Where(x => x.GetType() == typeof(SIA3900Equipment)).Cast<SIA3900Equipment>())
            {
                if(siaEquip.ReagentMaintenance.CheckFluidInfos.Count > 0)
                {
                    var reagent = siaEquip.ReagentMaintenance.CheckFluidInfos.Values.First();
                    var usageState = new ReagentUsageState();

                    if(Enum.TryParse(siaEquip.TypeDesc, out eDeviceMeasureType deviceType))
                    {
                        usageState.ReagentName = EnumOperate.GetEnumDesc(deviceType) + "分析仪试剂";
                    }
                    else
                    {
                        usageState.ReagentName = siaEquip.name;
                    }
                    usageState.LiquidResidual = reagent.LiquidResidual;
                    usageState.RemainDay = reagent.RemainDay;
                    usageState.GuaranteePeriod = reagent.GuaranteePeriod;
                    result.ReagentList.Add(usageState);
                }
            }

            foreach(var qcdEquip in DeviceManager.GetInstance().GetDeviceListUsed().Where(x => x.GetType() == typeof(QCD3900Equip)).Cast<QCD3900Equip>())
            {
                if(qcdEquip.ElementLifeInfos.LiquidParamInfos.Count > 0)
                {
                    var reagent = qcdEquip.ElementLifeInfos.LiquidParamInfos.Values.First();
                    var usageState = new ReagentUsageState();

                    if(Enum.TryParse(qcdEquip.TypeDesc, out eDeviceMeasureType deviceType))
                    {
                        usageState.ReagentName = EnumOperate.GetEnumDesc(deviceType) + "质控仪试剂";
                    }
                    else
                    {
                        usageState.ReagentName = qcdEquip.name;
                    }
                    usageState.ReagentName = qcdEquip.name;
                    usageState.LiquidResidual = float.IsNaN(reagent.Residual) ? -1 : (int)reagent.Residual;
                    usageState.RemainDay = reagent.GuaranteePeriod == -1 || reagent.ChangeTime == DateTime.MinValue ? -1 : reagent.GuaranteePeriod - (int)(DateTime.Now - reagent.ChangeTime).TotalDays;
                    usageState.GuaranteePeriod = reagent.GuaranteePeriod;
                    result.ReagentList.Add(usageState);
                }
            }

            result.CodMnReagentTemp = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.CodMnReagentTempNode?.GetValue();
            result.NH4ReagentTemp = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.NH4ReagentTempNode?.GetValue();
            result.TPReagentTemp = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.TPReagentTempNode?.GetValue();
            result.TNReagentTemp = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.TNReagentTempNode?.GetValue();
            result.FiveParamReagentTempNode = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.FiveParamReagentTempNode?.GetValue();

            return result;
        }

        #endregion
    }
}