﻿using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace Fpi.WMS3000.Inspection
{
    public class GridToImageExporter
    {
        public static void ExportToPng(Grid grid, string filePath)
        {
            // 解除父容器的滚动约束（例如 ScrollViewer）
            Grid unconstrainedGrid = ReleaseScrollConstraints(grid);

            // 强制 Grid 重新布局以包含所有内容
            unconstrainedGrid.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
            unconstrainedGrid.Arrange(new Rect(unconstrainedGrid.DesiredSize));
            unconstrainedGrid.UpdateLayout();

            // 创建位图并渲染
            RenderTargetBitmap renderBitmap = new RenderTargetBitmap(
                (int)unconstrainedGrid.ActualWidth,
                (int)unconstrainedGrid.ActualHeight,
                96, // DPI X
                96, // DPI Y
                PixelFormats.Pbgra32
            );
            renderBitmap.Render(unconstrainedGrid);

            // 保存为 PNG
            using(FileStream fs = File.Create(filePath))
            {
                PngBitmapEncoder encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));
                encoder.Save(fs);
            }
        }

        // 解除 ScrollViewer 的约束（如果存在）
        private static Grid ReleaseScrollConstraints(Grid originalGrid)
        {
            if(VisualTreeHelper.GetParent(originalGrid) is ScrollViewer scrollViewer)
            {
                // 创建一个无约束的新 Grid（克隆原 Grid）
                Grid newGrid = CloneGrid(originalGrid);
                newGrid.Width = double.NaN;  // 恢复 Auto 尺寸
                newGrid.Height = double.NaN;
                return newGrid;
            }
            return originalGrid;
        }

        // 克隆 Grid（深拷贝）
        private static Grid CloneGrid(Grid original)
        {
            string xaml = XamlWriter.Save(original);
            return (Grid)XamlReader.Parse(xaml);
        }
    }

}
