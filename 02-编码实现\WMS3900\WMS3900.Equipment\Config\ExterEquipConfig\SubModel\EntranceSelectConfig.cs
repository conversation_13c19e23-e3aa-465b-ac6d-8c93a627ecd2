﻿using System.ComponentModel;
using Fpi.Entrance;
using Newtonsoft.Json;

namespace Fpi.WMS3000.Equipment.Config
{
    /// <summary>
    /// 门禁选择配置
    /// </summary>
    public class EntranceSelectConfig
    {
        /// <summary>
        /// 主门禁ID
        /// </summary>
        [Description("主门禁ID")]
        public string MainEntranID { get; set; }

        /// <summary>
        /// 主门禁
        /// </summary>
        [Description("主门禁")]
        [JsonIgnore]
        public BaseNETEntrance MainEntran => EntranceManager.GetInstance().GetEntranceById(MainEntranID);
    }
}