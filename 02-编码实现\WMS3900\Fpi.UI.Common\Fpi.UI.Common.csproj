﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C238E665-75B4-4EDA-B574-A37F2794BA54}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>
    </ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>Fpi.UI.Common</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <RootNamespace>Fpi.UI.Common</RootNamespace>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <StartupObject>
    </StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>pdbonly</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Drawing">
      <Name>System.Drawing</Name>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Name>System.Windows.Forms</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources.en-US.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.en-US.resx</DependentUpon>
    </Compile>
    <Compile Include="UI\Common\Interfaces\ISetTitile.cs" />
    <Compile Include="UI\Common\Interfaces\ISetParentTitile.cs" />
    <Compile Include="UI\Common\PC\Configure\IConfigView.cs" />
    <Compile Include="UI\Common\Interfaces\ISetEnable.cs" />
    <Compile Include="UI\Common\Interfaces\ISetVisible.cs" />
    <Compile Include="UI\Common\Interfaces\IExitApp.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\Common\Interfaces\IFirstTask.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\Common\Interfaces\IMenuClickListener.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\Common\Interfaces\IMessageObserver.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Configure\BaseConfigureForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Configure\BaseConfigureForm.Designer.cs">
      <DependentUpon>BaseConfigureForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Common\PC\Configure\BaseConfigureView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Configure\BaseConfigureView.Designer.cs">
      <DependentUpon>BaseConfigureView.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Common\PC\Configure\IConfigure.cs" />
    <Compile Include="UI\Common\PC\Controls\FpiUiTreeView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Controls\ToolBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\CustomMessageBox\FpiMessageBox.cs" />
    <Compile Include="UI\Common\PC\Form\FormInfoBarDetail.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Form\FormInfoBarDetail.designer.cs">
      <DependentUpon>FormInfoBarDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Common\PC\Wizard\BaseWizardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Wizard\BaseWizardForm.Designer.cs">
      <DependentUpon>BaseWizardForm.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Common\PC\Wizard\IWizard.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Wizard\Wizard.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Wizard\WizardException.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UI\Common\PC\Controls\FpiTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.en-US.resx">
      <SubType>Designer</SubType>
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.en-US.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <SubType>Designer</SubType>
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Common\PC\Configure\BaseConfigureForm.en-US.resx">
      <DependentUpon>BaseConfigureForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Common\PC\Configure\BaseConfigureForm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>BaseConfigureForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Common\PC\Configure\BaseConfigureView.en-US.resx">
      <DependentUpon>BaseConfigureView.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Common\PC\Configure\BaseConfigureView.resx">
      <SubType>Designer</SubType>
      <DependentUpon>BaseConfigureView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Common\PC\Form\FormInfoBarDetail.resx">
      <DependentUpon>FormInfoBarDetail.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Common\PC\Wizard\BaseWizardForm.en-US.resx">
      <DependentUpon>BaseWizardForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Common\PC\Wizard\BaseWizardForm.resx">
      <DependentUpon>BaseWizardForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <None Include="Resources\add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\remove.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6e37d7b3-8d08-4ef3-a924-3b87982ab246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\DoingNow.png" />
    <Content Include="Resources\donow.png" />
    <Content Include="Resources\LeftLogo.png" />
    <Content Include="Resources\nodo.png" />
    <Content Include="Resources\NormalState.png" />
    <None Include="Resources\按钮1.png" />
    <None Include="Resources\按钮.png" />
    <None Include="Resources\按钮白.png" />
    <None Include="Resources\弹出框 %282%29.png" />
    <None Include="Resources\弹出框 %285%29.png" />
    <None Include="Resources\弹出框 %283%29.png" />
    <None Include="Resources\弹出框 %281%29.png" />
    <None Include="Resources\弹出框 %284%29.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>