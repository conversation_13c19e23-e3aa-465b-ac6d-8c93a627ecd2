﻿using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;
using Sunny.UI;

namespace Fpi.WMS3000.Pollution.UI.ExterDevices
{
    /// <summary>
    /// 采水单元数据显示
    /// </summary>
    public partial class UC_CollectionModuleState : UIUserControl, IRefreshUI
    {
        #region 字段属性

        private const string ErrorInfo = "— — — —";

        private const string DisplayFormat = "F2";

        #endregion

        #region 构造

        public UC_CollectionModuleState()
        {
            InitializeComponent();
        }

        #endregion

        #region IRefreshUI

        public void RefreshUI()
        {
            lblWaterCollectionState.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterCollectionState.ToString();
            lblSampleWaitingTime.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.SampleWaitingTime.ToString("F0");
            lblWaterPipeState.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPipeState.ToString();
            lblWaterPressState.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressState.ToString();
            lblPumpUsingMode.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.PumpUsingMode.ToString();
            lblLastUsedPump.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.LastUsedPump.ToString();
            lblWaterPump1State.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump1State.ToString();
            lblWaterPump2State.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPump2State.ToString();
            lblWaterPressure.Text = double.IsNaN(GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressure)
                ? ErrorInfo
                : GlobalDataCache.GetInstance().WaterCollectionModuleData.WaterPressure.ToString(DisplayFormat);
            lblCurrentBucket.Text = GlobalDataCache.GetInstance().WaterCollectionModuleData.CurrentSampleBucketSet.ToString();
        }

        #endregion
    }
}