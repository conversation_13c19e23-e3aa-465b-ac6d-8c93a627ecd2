using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Windows.Forms;
using Fpi.DB;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.Util;
using Fpi.WMS3000.DB;
using Sunny.UI;
using DbConfig = Fpi.WMS3000.Pollution.DB.DbConfig;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 数据报表
    /// 日、周、月、季、年报表查询
    /// </summary>
    public partial class UC_DataReport : UIUserControl
    {
        #region 属性、字段

        /// <summary>
        /// 左上角显示表名
        /// </summary>
        public string TitleName { get; set; } = "统计报表";

        /// <summary>
        /// 对应统计因子
        /// </summary>
        private List<QueryNode> _queryNodes = new List<QueryNode>();

        #endregion

        #region 构造

        public UC_DataReport()
        {
            InitializeComponent();
        }

        public UC_DataReport(string groupId)
        {
            InitializeComponent();

            // 设置报表因子组
            QueryGroup queryGroup = ReportManager.GetInstance().GetQueryGroupByGroupIdOrFirst(groupId);
            if(queryGroup != null)
            {
                foreach(QueryNode queryNode in queryGroup.QueryNodes)
                {
                    this._queryNodes.Add(queryNode);
                }

                TitleName = queryGroup.name;
            }
        }

        #endregion

        #region 事件

        private void UC_QueryData_Load(object sender, EventArgs e)
        {
            InitialTitle();
            InitialDateTimePicker();
            SetDataGridViewHead();
        }

        /// <summary>
        /// 点击查询按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void btnStartQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                // 清空界面数据
                dgvData.Rows.Clear();

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"查询{TitleName}", eOpType.浏览操作, eOpStyle.本地操作));

                // 检查查询条件
                Check();

                // 查询数据数量
                QueryDataCount();

                // 刷新页面显示
                FlushView();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 报告导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if(dgvData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Path.Combine(Application.StartupPath, "query");
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName = Path.Combine(filePath, $"{TitleName}_{dtpStartTime.Value:yyyy-MM-dd HH-mm-ss}_{dtpEndTime.Value:yyyy-MM-dd HH-mm-ss}.xlsx");

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvData, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if(dtpStartTime.Value > dtpEndTime.Value)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            // 检查界面查询条件

        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryDataCount()
        {
            int recordCount = 0;
            try
            {
                string strSql = $"select count(*) from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{dtpStartTime.Value.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{dtpEndTime.Value.ToString(DbConfig.DATETIME_FORMAT)}' order by datatime asc";
                recordCount = DbAccess.QueryRecordCount(strSql);
            }
            catch
            {
                throw new Exception("数据库连接异常!");
            }

            if(recordCount == 0)
            {
                throw new Exception("无当前条件下的查询数据!");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            try
            {
                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");
                // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                Thread.Sleep(50);

                // 清空原数据
                dgvData.Rows.Clear();

                string strSql = $"select * from {DbConfig.POLLUTION_MEASURE_DATA_TABLE}  where datatime>='{dtpStartTime.Value.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{dtpEndTime.Value.ToString(DbConfig.DATETIME_FORMAT)}' order by datatime asc";
                IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql);

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                if(reader != null)
                {

                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        #region 初始化控件

        /// <summary>
        /// 设置表头
        /// </summary>
        private void SetDataGridViewHead()
        {

        }

        /// <summary>
        /// 设置标题
        /// </summary>
        private void InitialTitle()
        {
            this.labTitle.Text = TitleName;
        }

        /// <summary>
        /// 初始化时间选择控件
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today.AddDays(1).AddSeconds(-1);
        }

        #endregion

        #endregion
    }
}