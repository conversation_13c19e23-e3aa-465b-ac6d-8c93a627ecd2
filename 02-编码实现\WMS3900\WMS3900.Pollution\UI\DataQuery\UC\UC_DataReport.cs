using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using Fpi.DB;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.Util;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Pollution.DB;
using Sunny.UI;
using DbConfig = Fpi.WMS3000.Pollution.DB.DbConfig;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 污染数据统计报表时间周期枚举
    /// </summary>
    public enum ePollutionReportPeriodType
    {
        /// <summary>
        /// 日报表
        /// </summary>
        [Description("日")]
        Day = 0,

        /// <summary>
        /// 周报表
        /// </summary>
        [Description("周")]
        Week = 1,

        /// <summary>
        /// 月报表
        /// </summary>
        [Description("月")]
        Month = 2,

        /// <summary>
        /// 季报表
        /// </summary>
        [Description("季")]
        Quarter = 3,

        /// <summary>
        /// 年报表
        /// </summary>
        [Description("年")]
        Year = 4
    }

    /// <summary>
    /// 数据报表
    /// 日、周、月、季、年报表查询
    /// </summary>
    public partial class UC_DataReport : UIUserControl
    {
        #region 属性、字段

        /// <summary>
        /// 左上角显示表名
        /// </summary>
        public string TitleName { get; set; } = "数据统计报表";

        /// <summary>
        /// 对应统计因子
        /// </summary>
        private List<QueryNode> _queryNodes = new List<QueryNode>();

        /// <summary>
        /// 当前选择的时间周期类型
        /// </summary>
        private ePollutionReportPeriodType _currentPeriodType = ePollutionReportPeriodType.Day;

        /// <summary>
        /// 选择的时间点（某天、某月等）
        /// </summary>
        private DateTime _selectedTime;

        /// <summary>
        /// 实际查询起始时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _startTime;

        /// <summary>
        /// 实际查询结束时间（根据选择时间点和周期类型计算）
        /// </summary>
        private DateTime _endTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        #endregion

        #region 界面控件（需要在设计器中添加）

        // 报表类型选择控件
        // private UIRadioButton rbDay;      // 日报表
        // private UIRadioButton rbWeek;     // 周报表
        // private UIRadioButton rbMonth;    // 月报表
        // private UIRadioButton rbQuarter;  // 季报表
        // private UIRadioButton rbYear;     // 年报表

        // 时间选择控件
        // private UIDatePicker dtpSelectedTime;  // 时间选择器

        // 操作按钮
        // private UIButton btnQuery;       // 查询按钮
        // private UIButton btnExport;      // 导出按钮

        // 信息显示控件
        // private UILabel lblCurrentPeriod;  // 当前周期显示标签

        // 数据显示控件
        // private UIDataGridView dgvData;    // 主数据表格

        // 容器控件
        // private UIPanel pnlTop;           // 顶部控制面板

        #endregion

        #region 构造

        public UC_DataReport()
        {
            InitializeComponent();
        }

        public UC_DataReport(string groupId)
        {
            InitializeComponent();

            // 设置报表因子组
            QueryGroup queryGroup = ReportManager.GetInstance().GetQueryGroupByGroupIdOrFirst(groupId);
            if(queryGroup != null)
            {
                foreach(QueryNode queryNode in queryGroup.QueryNodes)
                {
                    this._queryNodes.Add(queryNode);
                }

                TitleName = queryGroup.name;
            }
        }

        #endregion

        #region 事件

        private void UC_QueryData_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialTitle();
                InitialDateTimePicker();
                InitializePeriodTypeControls();
                UpdateDateSelectorVisibility();
                SetDataGridViewHead();
                UpdateCurrentPeriodLabel();
            }
        }

        /// <summary>
        /// 时间周期选择变更事件
        /// </summary>
        private void rbPeriodType_CheckedChanged(object sender, EventArgs e)
        {
            var radioButton = sender as UIRadioButton;
            if(radioButton != null && radioButton.Checked)
            {
                // 根据单选按钮确定周期类型
                if(radioButton.Name == "rbDay")
                    _currentPeriodType = ePollutionReportPeriodType.Day;
                else if(radioButton.Name == "rbWeek")
                    _currentPeriodType = ePollutionReportPeriodType.Week;
                else if(radioButton.Name == "rbMonth")
                    _currentPeriodType = ePollutionReportPeriodType.Month;
                else if(radioButton.Name == "rbQuarter")
                    _currentPeriodType = ePollutionReportPeriodType.Quarter;
                else if(radioButton.Name == "rbYear")
                    _currentPeriodType = ePollutionReportPeriodType.Year;

                // 重新计算查询时间范围
                CalculateQueryTimeRange();

                UpdateDateSelectorVisibility();
                UpdateCurrentPeriodLabel();
                SetDataGridViewHead(); // 重新设置表头
            }
        }

        /// <summary>
        /// 时间选择变更事件
        /// </summary>
        private void dateTimePicker_ValueChanged(object sender, EventArgs e)
        {
            // 更新选择的时间点
            if(sender is DateTimePicker dtp)
            {
                _selectedTime = dtp.Value;
                CalculateQueryTimeRange();
                UpdateCurrentPeriodLabel();
            }
        }

        /// <summary>
        /// 点击查询按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void btnStartQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                // 清空界面数据
                dgvData.Rows.Clear();

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"查询{TitleName}", eOpType.浏览操作, eOpStyle.本地操作));

                // 根据当前周期类型计算查询时间范围
                CalculateQueryTimeRange();

                // 检查查询条件
                Check();

                // 查询数据数量
                QueryDataCount();

                // 刷新页面显示
                FlushView();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 报告导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if(dgvData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Path.Combine(Application.StartupPath, "query");
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName = Path.Combine(filePath, $"{TitleName}_{_selectedTime:yyyy-MM-dd HH-mm-ss}.xlsx");

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvData, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if(_startTime > _endTime)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            // 检查查询因子
            if(_queryNodes == null || _queryNodes.Count == 0)
            {
                throw new Exception("未配置查询因子！");
            }
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryDataCount()
        {
            int recordCount = 0;
            try
            {
                // 根据报表类型确定数据类型
                int dataType = GetDataTypeByPeriod(_currentPeriodType);
                string strSql = $"select count(*) from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={dataType}";
                recordCount = DbAccess.QueryRecordCount(strSql);
            }
            catch
            {
                throw new Exception("数据库连接异常!");
            }

            if(recordCount == 0)
            {
                throw new Exception("无当前条件下的查询数据!");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            try
            {
                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");
                // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                Thread.Sleep(50);

                // 清空原数据
                dgvData.Rows.Clear();

                // 根据报表类型生成数据
                if(_currentPeriodType == ePollutionReportPeriodType.Day)
                {
                    GenerateDayReport();
                }
                else
                {
                    GeneratePeriodReport();
                }

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        #region 初始化控件

        /// <summary>
        /// 设置表头
        /// </summary>
        private void InitialTitle()
        {
            // 设置标题，假设有标题控件
            if(this.Text != null)
                this.Text = TitleName;
        }

        /// <summary>
        /// 初始化时间选择控件
        /// </summary>
        private void InitialDateTimePicker()
        {
            // 初始化选择时间点为今天
            _selectedTime = DateTime.Today;

            // 根据当前周期类型计算实际查询时间范围
            CalculateQueryTimeRange();
        }

        /// <summary>
        /// 初始化报表类型选择控件
        /// </summary>
        private void InitializePeriodTypeControls()
        {
            // 注意：以下代码需要在设计器中添加对应控件后取消注释

            // 绑定单选按钮事件
            // rbDay.CheckedChanged += rbPeriodType_CheckedChanged;
            // rbWeek.CheckedChanged += rbPeriodType_CheckedChanged;
            // rbMonth.CheckedChanged += rbPeriodType_CheckedChanged;
            // rbQuarter.CheckedChanged += rbPeriodType_CheckedChanged;
            // rbYear.CheckedChanged += rbPeriodType_CheckedChanged;

            // 设置默认选择
            // rbDay.Checked = true;

            // 设置单选按钮文本
            // rbDay.Text = "日报表";
            // rbWeek.Text = "周报表";
            // rbMonth.Text = "月报表";
            // rbQuarter.Text = "季报表";
            // rbYear.Text = "年报表";
        }

        /// <summary>
        /// 初始化时间选择控件事件
        /// </summary>
        private void InitializeTimeControls()
        {
            // 注意：以下代码需要在设计器中添加对应控件后取消注释

            // 绑定时间选择器事件
            // dtpSelectedTime.ValueChanged += dateTimePicker_ValueChanged;

            // 设置默认时间
            // dtpSelectedTime.Value = DateTime.Today;
        }

        /// <summary>
        /// 初始化按钮控件
        /// </summary>
        private void InitializeButtons()
        {
            // 注意：以下代码需要在设计器中添加对应控件后取消注释

            // btnQuery.Click += btnStartQuery_Click;
            // btnExport.Click += btnExport_Click;

            // 设置按钮文本
            // btnQuery.Text = "查询";
            // btnExport.Text = "导出";
        }



        /// <summary>
        /// 更新日期选择器的可见性和类型
        /// </summary>
        private void UpdateDateSelectorVisibility()
        {
            // 根据当前周期类型设置时间选择控件的显示方式
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：显示日期选择器（年月日）
                    // 如果有UIDatePicker控件，设置ShowType为YearMonthDay
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：显示日期选择器（选择某一天，自动计算所在周）
                    // 设置ShowType为YearMonthDay
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：显示年月选择器
                    // 设置ShowType为YearMonth
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：显示年月选择器（选择某个月，自动计算所在季度）
                    // 设置ShowType为YearMonth
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：显示年选择器
                    // 设置ShowType为Year
                    break;
            }
        }

        /// <summary>
        /// 更新当前统计周期标签
        /// </summary>
        private void UpdateCurrentPeriodLabel()
        {
            string periodText = GetPeriodDisplayText(_currentPeriodType, _selectedTime);
            // 这里需要根据具体的标签控件来显示周期信息
            // 例如：lblPeriod.Text = periodText;
        }

        /// <summary>
        /// 根据选择的时间点和报表周期类型计算查询时间范围
        /// </summary>
        private void CalculateQueryTimeRange()
        {
            switch(_currentPeriodType)
            {
                case ePollutionReportPeriodType.Day:
                    // 日报表：查询选择日期当天的数据
                    _startTime = _selectedTime.Date;
                    _endTime = _startTime.AddDays(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Week:
                    // 周报表：查询选择日期所在周的数据
                    var weekStart = _selectedTime.AddDays(-(int)_selectedTime.DayOfWeek);
                    _startTime = weekStart.Date;
                    _endTime = weekStart.AddDays(7).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Month:
                    // 月报表：查询选择日期所在月的数据
                    var monthStart = new DateTime(_selectedTime.Year, _selectedTime.Month, 1);
                    _startTime = monthStart;
                    _endTime = monthStart.AddMonths(1).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Quarter:
                    // 季报表：查询选择日期所在季度的数据
                    var quarterStart = GetQuarterStart(_selectedTime);
                    _startTime = quarterStart;
                    _endTime = quarterStart.AddMonths(3).AddSeconds(-1);
                    break;
                case ePollutionReportPeriodType.Year:
                    // 年报表：查询选择日期所在年的数据
                    var yearStart = new DateTime(_selectedTime.Year, 1, 1);
                    _startTime = yearStart;
                    _endTime = yearStart.AddYears(1).AddSeconds(-1);
                    break;
            }
        }

        /// <summary>
        /// 根据报表类型获取对应的数据类型
        /// </summary>
        private int GetDataTypeByPeriod(ePollutionReportPeriodType periodType)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Day:
                    return (int)ePollutionDataType.小时数据; // 日报表查询小时数据
                default:
                    return (int)ePollutionDataType.日数据; // 其他报表查询日数据
            }
        }

        /// <summary>
        /// 获取季度开始时间
        /// </summary>
        private DateTime GetQuarterStart(DateTime date)
        {
            int quarter = (date.Month - 1) / 3 + 1;
            int startMonth = (quarter - 1) * 3 + 1;
            return new DateTime(date.Year, startMonth, 1);
        }

        /// <summary>
        /// 获取周期显示文本
        /// </summary>
        private string GetPeriodDisplayText(ePollutionReportPeriodType periodType, DateTime selectedTime)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Day:
                    return $"{selectedTime:yyyy年MM月dd日}";
                case ePollutionReportPeriodType.Week:
                    var weekStart = selectedTime.AddDays(-(int)selectedTime.DayOfWeek);
                    var weekEnd = weekStart.AddDays(6);
                    return $"{weekStart:yyyy年MM月dd日} 至 {weekEnd:yyyy年MM月dd日}";
                case ePollutionReportPeriodType.Month:
                    return $"{selectedTime:yyyy年MM月}";
                case ePollutionReportPeriodType.Quarter:
                    int quarter = (selectedTime.Month - 1) / 3 + 1;
                    return $"{selectedTime.Year}年第{quarter}季度";
                case ePollutionReportPeriodType.Year:
                    return $"{selectedTime.Year}年";
                default:
                    return "";
            }
        }

        /// <summary>
        /// 生成日报表数据
        /// </summary>
        private void GenerateDayReport()
        {
            try
            {
                // 查询小时数据
                int dataType = (int)ePollutionDataType.小时数据;
                string strSql = $"select * from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={dataType} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 生成24小时时段的报表
                for(int hour = 0; hour < 24; hour++)
                {
                    DateTime hourTime = _startTime.Date.AddHours(hour);
                    string timeLabel = $"{hour:D2}-{(hour + 1):D2}时";

                    // 获取该小时的数据
                    var hourData = dataTable.AsEnumerable()
                        .Where(row => DateTime.Parse(row["datatime"].ToString()).Hour == hour)
                        .ToList();

                    // 创建行数据
                    List<object> rowValues = new List<object> { timeLabel };

                    // 添加统计因子数据
                    foreach(var queryNode in _queryNodes)
                    {
                        var factorData = hourData.Where(row => row["factorcode"].ToString() == queryNode.id).ToList();

                        // 计算统计值
                        var stats = CalculateStatistics(factorData, queryNode.name);
                        rowValues.Add(stats.Max);
                        rowValues.Add(stats.Min);
                        rowValues.Add(stats.Average);
                        if(!IsPhFactor(queryNode.name))
                        {
                            rowValues.Add(stats.Total);
                        }
                    }

                    // 添加小时流量数据
                    var flowData = hourData.Where(row => row["factorcode"].ToString() == DbConfig.TOTAL_FLOW).ToList();
                    if(flowData.Any())
                    {
                        double flowValue = 0;
                        if(double.TryParse(flowData.First()["factorvalue"].ToString(), out flowValue))
                        {
                            rowValues.Add(flowValue);
                        }
                        else
                        {
                            rowValues.Add(0);
                        }
                    }
                    else
                    {
                        rowValues.Add(0);
                    }

                    // 添加到DataGridView
                    dgvData.Rows.Add(rowValues.ToArray());
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"生成日报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 生成周期报表数据（周、月、季、年）
        /// </summary>
        private void GeneratePeriodReport()
        {
            try
            {
                // 查询日数据
                int dataType = (int)ePollutionDataType.日数据;
                string strSql = $"select * from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{_startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{_endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype={dataType} order by datatime asc";

                DataTable dataTable = new DataTable();
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql))
                {
                    dataTable.Load(reader);
                }

                // 按时间分组统计
                var timeGroups = GetTimeGroups(dataTable, _currentPeriodType);

                foreach(var timeGroup in timeGroups)
                {
                    List<object> rowValues = new List<object> { timeGroup.Key };

                    // 添加统计因子数据
                    foreach(var queryNode in _queryNodes)
                    {
                        var factorData = timeGroup.Value.Where(row => row["factorcode"].ToString() == queryNode.id).ToList();

                        // 计算统计值
                        var stats = CalculateStatistics(factorData, queryNode.name);
                        rowValues.Add(stats.Max);
                        rowValues.Add(stats.Min);
                        rowValues.Add(stats.Average);
                        if(!IsPhFactor(queryNode.name))
                        {
                            rowValues.Add(stats.Total);
                        }
                    }

                    // 添加到DataGridView
                    dgvData.Rows.Add(rowValues.ToArray());
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"生成周期报表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 统计数据结构
        /// </summary>
        private struct StatisticsResult
        {
            public double Max { get; set; }
            public double Min { get; set; }
            public double Average { get; set; }
            public double Total { get; set; }
        }

        /// <summary>
        /// 计算统计值
        /// </summary>
        private StatisticsResult CalculateStatistics(List<DataRow> factorData, string factorName)
        {
            var result = new StatisticsResult();

            if(factorData == null || factorData.Count == 0)
            {
                return result;
            }

            var validValues = factorData
                .Where(row => row["factorvalue"] != DBNull.Value && !string.IsNullOrEmpty(row["factorvalue"].ToString()))
                .Select(row =>
                {
                    if(double.TryParse(row["factorvalue"].ToString(), out double value))
                        return (double?)value;
                    return null;
                })
                .Where(v => v.HasValue)
                .Select(v => v.Value)
                .ToList();

            if(validValues.Count > 0)
            {
                result.Max = validValues.Max();
                result.Min = validValues.Min();
                result.Average = validValues.Average();

                // 累计流量因子的总量取最后一个有效值
                if(IsCumulativeFlowFactor(factorName))
                {
                    result.Total = validValues.LastOrDefault();
                }
                else if(!IsPhFactor(factorName))
                {
                    result.Total = validValues.Sum();
                }
            }

            return result;
        }

        /// <summary>
        /// 判断是否为pH因子
        /// </summary>
        private bool IsPhFactor(string factorName)
        {
            return factorName != null && factorName.ToLower().Contains("ph");
        }

        /// <summary>
        /// 判断是否为累计流量因子
        /// </summary>
        private bool IsCumulativeFlowFactor(string factorName)
        {
            return factorName != null && factorName.Contains("累计流量");
        }

        /// <summary>
        /// 根据周期类型获取时间分组
        /// </summary>
        private Dictionary<string, List<DataRow>> GetTimeGroups(DataTable dataTable, ePollutionReportPeriodType periodType)
        {
            var groups = new Dictionary<string, List<DataRow>>();

            foreach(DataRow row in dataTable.Rows)
            {
                DateTime dataTime = DateTime.Parse(row["datatime"].ToString());
                string groupKey = GetTimeGroupKey(dataTime, periodType);

                if(!groups.ContainsKey(groupKey))
                {
                    groups[groupKey] = new List<DataRow>();
                }
                groups[groupKey].Add(row);
            }

            return groups;
        }

        /// <summary>
        /// 获取时间分组键
        /// </summary>
        private string GetTimeGroupKey(DateTime dateTime, ePollutionReportPeriodType periodType)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Week:
                    var weekStart = dateTime.AddDays(-(int)dateTime.DayOfWeek);
                    return $"{weekStart:yyyy年MM月dd日} 至 {weekStart.AddDays(6):yyyy年MM月dd日}";
                case ePollutionReportPeriodType.Month:
                    return $"{dateTime:yyyy年MM月}";
                case ePollutionReportPeriodType.Quarter:
                    int quarter = (dateTime.Month - 1) / 3 + 1;
                    return $"{dateTime.Year}年第{quarter}季度";
                case ePollutionReportPeriodType.Year:
                    return $"{dateTime.Year}年";
                default:
                    return dateTime.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// 设置DataGridView表头
        /// </summary>
        private void SetDataGridViewHead()
        {
            try
            {
                // 清除现有列
                dgvData.Columns.Clear();

                // 添加时间列
                string timeColumnName = GetTimeColumnName(_currentPeriodType);
                dgvData.Columns.Add("Time", timeColumnName);

                // 添加统计因子列
                foreach(var queryNode in _queryNodes)
                {
                    string factorName = queryNode.name;
                    string unit = queryNode.UnitId ?? "";

                    // 添加最大值列
                    dgvData.Columns.Add($"Max_{queryNode.id}", $"{factorName}最大值({unit})");

                    // 添加最小值列
                    dgvData.Columns.Add($"Min_{queryNode.id}", $"{factorName}最小值({unit})");

                    // 添加平均值列
                    dgvData.Columns.Add($"Avg_{queryNode.id}", $"{factorName}平均值({unit})");

                    // pH因子不添加总量列
                    if(!IsPhFactor(factorName))
                    {
                        dgvData.Columns.Add($"Total_{queryNode.id}", $"{factorName}总量({unit})");
                    }
                }

                // 日报表添加小时流量列
                if(_currentPeriodType == ePollutionReportPeriodType.Day)
                {
                    dgvData.Columns.Add("HourFlow", "小时流量(m?)");
                }

                // 设置列宽和样式
                foreach(DataGridViewColumn column in dgvData.Columns)
                {
                    column.Width = 120;
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                }

                // 时间列稍宽一些
                if(dgvData.Columns.Count > 0)
                {
                    dgvData.Columns[0].Width = 150;
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"设置表头失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取时间列名称
        /// </summary>
        private string GetTimeColumnName(ePollutionReportPeriodType periodType)
        {
            switch(periodType)
            {
                case ePollutionReportPeriodType.Day:
                    return "时段";
                case ePollutionReportPeriodType.Week:
                    return "周";
                case ePollutionReportPeriodType.Month:
                    return "月";
                case ePollutionReportPeriodType.Quarter:
                    return "季度";
                case ePollutionReportPeriodType.Year:
                    return "年";
                default:
                    return "时间";
            }
        }

        #endregion

        #endregion
    }
}