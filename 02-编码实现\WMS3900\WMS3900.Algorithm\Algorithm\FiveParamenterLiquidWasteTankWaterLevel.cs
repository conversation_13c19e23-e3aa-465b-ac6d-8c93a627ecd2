﻿using System;
using System.Collections.Generic;
using Fpi.WMS3000.Algorithm.Utils;
using OpenCvSharp;

namespace Fpi.WMS3000.Algorithm
{
    /// <summary>
    /// 2.4五参数废液桶-纯水桶液位识别
    /// </summary>
    public class FiveParamenterLiquidWasteTankWaterLevel
    {
        private static string scaleplateLocationModelPath = "bin/ImageAlgorithm/scaleplateLocation.onnx"; // 模型文件路径
        private static OpenCvSharp.Dnn.Net scaleplateLocationNet; // 模型初始化
        private static int inputHeight = 736; // 模型输入图像高度
        private static int inputWidth = 1280; // 模型输入图像宽度
        public static float objThreshold = 0.1f; // 是目标的概率
        public static float nmsThreshold = 0.1f; // nms

        private static int scaleInterval = 40;
        private static int minDistinction = 6;
        /// <summary>
        /// 五参数废液桶、纯水桶、废水桶液位识别
        /// 返回废液桶状态、废水桶状态、纯水桶状态，bit位表示。废液桶满（大于16）置1，纯水桶空（小于4）置1）
        /// 0（0000 0000）表示一切正常；1（0000 0001）表示废液桶满；2（0000 0010）表示纯水桶空；
        /// <param name="image">图像</param>
        /// <param name="maxWaterLevel">废液桶最高水位</param>
        /// <param name="minWaterLevel">纯水桶最低水位</param>
        /// <param name="disposeModel">释放模型资源</param>
        /// <returns></returns>
        public static byte detect(Mat image, double maxWaterLevel = 0.8, double minWaterLevel = 0.2, bool disposeModel = true)
        {
            byte isAbnormal = 0;
            try
            {
                if(scaleplateLocationNet == null || scaleplateLocationNet.IsDisposed)
                {
                    scaleplateLocationNet = OpenCvSharp.Dnn.CvDnn.ReadNet(scaleplateLocationModelPath); // 模型初始化
                }
                Tuple<List<Rect>, List<Rect>> objRect = detectScaleplateLocation(image);
                List<Rect> redScaleplateBoxes = objRect.Item1;
                List<Rect> blueScaleplateBoxes = objRect.Item2;

                if(redScaleplateBoxes.Count != 1)
                {
                    isAbnormal |= 1;
                    Console.WriteLine($"废液桶标尺检测失败");
                }
                else
                {
                    Rect redScaleplateBox = redScaleplateBoxes[0];
                    Mat redScaleImage = image.Clone(new Rect(redScaleplateBox.Right, redScaleplateBox.Top, redScaleplateBox.Width * 3, redScaleplateBox.Height));
                    int waterLevel = detectWaterLevel(redScaleImage);
                    //OpenCvSharp.Cv2.ImShow("img", redScaleImage);
                    //OpenCvSharp.Cv2.WaitKey(0);
                    if(waterLevel * 1.0 / scaleInterval < minWaterLevel)
                    {
                        isAbnormal |= 1;
                    }
                }
                if(blueScaleplateBoxes.Count != 1)
                {
                    isAbnormal |= 1;
                    Console.WriteLine($"纯水桶标尺检测失败");
                }
                else
                {
                    Rect blueScaleplateBox = blueScaleplateBoxes[0];
                    Mat blueScaleImage = image.Clone(new Rect(blueScaleplateBox.Right, blueScaleplateBox.Top, blueScaleplateBox.Width * 3, blueScaleplateBox.Height));
                    int waterLevel = detectWaterLevel(blueScaleImage);
                    //OpenCvSharp.Cv2.ImShow("img", blueScaleImage);
                    //OpenCvSharp.Cv2.WaitKey(0);
                    if(waterLevel * 1.0 / scaleInterval < minWaterLevel)
                    {
                        isAbnormal |= 1 << 1;
                    }
                }
            }
            catch(Exception ex)
            {
                Console.WriteLine($"五参数废液桶、纯水桶液位识别出错: {ex}");
            }
            if(disposeModel && scaleplateLocationNet != null && !scaleplateLocationNet.IsDisposed)
            {
                scaleplateLocationNet.Dispose(); // 模型资源释放
            }
            return 0;
        }

        private static Tuple<List<Rect>, List<Rect>> detectScaleplateLocation(Mat image)
        {

            List<Rect> redScaleLocation = new List<Rect>();
            List<Rect> blueScaleLocation = new List<Rect>();
            try
            {
                int newH = 0, newW = 0, padH = 0, padW = 0;

                Mat dstImg = Preprocess.ResizeImage(image, inputHeight, inputWidth, out newH, out newW, out padH, out padW);
                float ratioH = 1.0f * image.Rows / newH, ratioW = 1.0f * image.Cols / newW;
                dstImg = OpenCvSharp.Dnn.CvDnn.BlobFromImage(dstImg, 1 / 255.0, new Size(inputWidth, inputHeight), new Scalar(0, 0, 0), true, false);
                //配置图片输入数据
                scaleplateLocationNet.SetInput(dstImg);

                //输出数据，节点是output，这个是export.py定义好的
                Mat result = scaleplateLocationNet.Forward("output0");
                //维度变换
                result = result.Reshape(1, result.Size().Width);
                //预测框
                List<Rect> redScaleBoxes = new List<Rect>();
                List<Rect> blueScaleBoxes = new List<Rect>();
                //预测框的得分
                List<float> redScaleScores = new List<float>();
                List<float> blueScaleScores = new List<float>();

                for(int r = 0; r < result.Rows; r++)
                {
                    float redScaleConf = result.Get<float>(r, 4) * result.Get<float>(r, 5);
                    float blueScaleConf = result.Get<float>(r, 4) * result.Get<float>(r, 6);
                    if(redScaleConf > objThreshold)
                    {
                        float cx = result.Get<float>(r, 0);
                        float cy = result.Get<float>(r, 1);
                        float w = result.Get<float>(r, 2);
                        float h = result.Get<float>(r, 3);

                        int left = (int)((cx - padW - 0.5 * w) * ratioW);
                        int top = (int)((cy - padH - 0.5 * h) * ratioH);


                        redScaleBoxes.Add(new Rect(left, top, (int)(w * ratioW), (int)(h * ratioH)));
                        redScaleScores.Add(redScaleConf);
                    }
                    if(blueScaleConf > objThreshold)
                    {
                        float cx = result.Get<float>(r, 0);
                        float cy = result.Get<float>(r, 1);
                        float w = result.Get<float>(r, 2);
                        float h = result.Get<float>(r, 3);

                        int left = (int)((cx - padW - 0.5 * w) * ratioW);
                        int top = (int)((cy - padH - 0.5 * h) * ratioH);

                        blueScaleBoxes.Add(new Rect(left, top, (int)(w * ratioW), (int)(h * ratioH)));
                        blueScaleScores.Add(blueScaleConf);
                    }
                }

                int[] redScaleIndices;
                int[] blueScaleIndices;
                //使用opencv NMSBoxes函数处理数据，得到预测结果
                OpenCvSharp.Dnn.CvDnn.NMSBoxes(redScaleBoxes, redScaleScores, objThreshold, nmsThreshold, out redScaleIndices);
                OpenCvSharp.Dnn.CvDnn.NMSBoxes(blueScaleBoxes, blueScaleScores, objThreshold, nmsThreshold, out blueScaleIndices);

                for(int i = 0; i < redScaleIndices.Length; i++)
                {
                    redScaleLocation.Add(redScaleBoxes[redScaleIndices[i]]);
                }
                for(int i = 0; i < blueScaleIndices.Length; i++)
                {
                    blueScaleLocation.Add(blueScaleBoxes[blueScaleIndices[i]]);
                }

                redScaleLocation.Sort((a, b) => (a.Left.CompareTo(b.Left)));
                blueScaleLocation.Sort((a, b) => (a.Left.CompareTo(b.Left)));

                if(redScaleLocation.Count < 1)
                {
                    throw new Exception($"识别废液桶、废水桶出错");
                }

                while(redScaleLocation.Count > 1)
                {
                    if(redScaleLocation[0].Left < image.Cols - redScaleLocation[redScaleLocation.Count - 1].Right)
                    {
                        redScaleLocation.RemoveAt(0);
                    }
                    else
                    {
                        redScaleLocation.RemoveAt(redScaleLocation.Count - 1);
                    }
                }
                if(blueScaleLocation.Count < 1)
                {
                    throw new Exception($"识别纯水桶出错");
                }

                while(blueScaleLocation.Count > 1)
                {
                    if(blueScaleLocation[0].Left < image.Cols - blueScaleLocation[blueScaleLocation.Count - 1].Right)
                    {
                        blueScaleLocation.RemoveAt(0);
                    }
                    else
                    {
                        blueScaleLocation.RemoveAt(blueScaleLocation.Count - 1);
                    }
                }

            }
            catch(Exception ex)
            {
                Console.WriteLine(ex);
            }
            return new Tuple<List<Rect>, List<Rect>>(redScaleLocation, blueScaleLocation);
        }

        private static int detectWaterLevel(Mat image)
        {
            Mat imageGray = new Mat();
            Cv2.CvtColor(image, imageGray, ColorConversionCodes.BGR2GRAY);
            //OpenCvSharp.Cv2.MedianBlur(imageGray, imageGray, 7);
            Rect rect = new Rect((int)(imageGray.Cols * 0.1), (int)(imageGray.Rows * 1 / 21f), (int)(imageGray.Cols * 0.8), (int)(imageGray.Rows * 20 / 21f));
            Mat cropImageGray = imageGray.Clone(rect);

            Cv2.Flip(cropImageGray, cropImageGray, FlipMode.X);

            int[] intersityMode = new int[scaleInterval];
            int[] mode = new int[256];
            int intersityInd = 1;
            int maxNum = 0;
            for(int row = 0; row < cropImageGray.Rows; row++)
            {
                if(row >= intersityInd * cropImageGray.Rows / (scaleInterval))
                {
                    maxNum = 0;
                    mode = new int[256];
                    intersityInd++;
                }
                for(int col = 0; col < cropImageGray.Cols; col++)
                {
                    int intersity = cropImageGray.Get<byte>(row, col);
                    mode[intersity] += 1;
                    if(mode[intersity] > maxNum)
                    {
                        maxNum = mode[intersity];
                        intersityMode[intersityInd - 1] = intersity;
                    }
                }
            }
            int waterLevel = 0;
            int maxDistinction = minDistinction;
            for(int i = 1; i < intersityMode.Length - 1; i++)
            {
                if(intersityMode[i - 1] == 0) continue;
                int distinction = intersityMode[i] - intersityMode[i - 1];
                if(distinction > maxDistinction && intersityMode[i - 1] < intersityMode[i + 1])
                {
                    maxDistinction = distinction;
                    waterLevel = i;
                }
            }
            return waterLevel;
        }
    }
}