﻿using System;
using Fpi.Alarm;
using Fpi.Communication;
using Fpi.Communication.Interfaces;
using Fpi.Data.Config;
using Fpi.Devices;
using Fpi.Devices.Channel;
using Fpi.Devices.DeviceProtocols;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.JDRKRS;

namespace Fpi.WMS3000.Equipment
{
    /// <summary>
    /// 建大仁科RS-KTC-N01空调控制器
    /// </summary>
    public class JDRKRSEquip : Device, IAirConditionControl
    {
        #region 字段属性

        /// <summary>
        /// 设备参数
        /// </summary>
        public JDRKRSParam DeviceParams { get; } = new JDRKRSParam();

        #endregion

        #region 构造

        public JDRKRSEquip()
        {
            this.DeviceType = eDeviceType.EXTER;

            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "建大仁科RS-KTC-N01-1空调控制器。通道1：湿度；通道2：温度；通道3：互感电流。";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "RSKTCN01_Equip";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "建大仁科RS-KTC-N01-1空调控制器";
            }

            if(string.IsNullOrEmpty(this.AlarmGroupId))
            {
                this.AlarmGroupId = "RSKTCN01_Equip";
            }

            if(string.IsNullOrEmpty(this.AlarmSourceId))
            {
                this.AlarmSourceId = "RSKTCN01_Equip";
            }

            if(string.IsNullOrEmpty(this.ProtocolImp))
            {
                this.ProtocolImp = typeof(ModbusProtocol).FullName;
            }
        }

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName") ? "建大仁科RS-KTC-N01-1空调控制器" : this.name;
        }

        public override void GetDeviceData()
        {
            try
            {
                byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x00, 0x00, 0x00, 0x06 };
                IByteStream bs = new ByteArrayWrap(dataSend);

                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 15)
                {
                    throw new Exception("回应数据长度不合法！");
                }
                if(dataReceive[2] != 0x0C)
                {
                    throw new Exception("回应数据错位！");
                }

                DeviceParams.UpdateParam(dataReceive, 3);

                if(this.InValueChannels.GetCount() > 0 && ((InValueChannel)this.InValueChannels[0]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[0]).ChannelValue = DeviceParams.Humidity;
                    ((InValueChannel)this.InValueChannels[0]).ValueNode.State = (int)eValueNodeState.N;
                }
                if(this.InValueChannels.GetCount() > 1 && ((InValueChannel)this.InValueChannels[1]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[1]).ChannelValue = DeviceParams.Temp;
                    ((InValueChannel)this.InValueChannels[1]).ValueNode.State = (int)eValueNodeState.N;
                }
                if(this.InValueChannels.GetCount() > 2 && ((InValueChannel)this.InValueChannels[2]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[2]).ChannelValue = DeviceParams.ACurrent;
                    ((InValueChannel)this.InValueChannels[2]).ValueNode.State = (int)eValueNodeState.N;
                }

                //消除通信异常报警
                AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                this._communicationErrorCount = 0;
            }
            catch(Exception ex)
            {
                if(this._communicationErrorCount++ >= 3)
                {
                    foreach(InValueChannel inValueChl in this.InValueChannels)
                    {
                        inValueChl.ChannelValue = float.NaN;
                        if(inValueChl.ValueNode != null)
                        {
                            inValueChl.ValueNode.State = (int)eValueNodeState.F;
                        }
                    }

                    //增加通信异常报警
                    AlarmManager.GetInstance().AddAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                }

                throw new Exception($"[{name}]读数失败：{ex.Message}");
            }
            finally
            {
                this.SetRealDataToNode();
            }
        }

        #endregion

        #region IAirConditionControl

        /// <summary>
        /// 设置空调状态
        /// </summary>
        /// <param name="state"></param>
        public void SetACState(eACState state)
        {
            try
            {
                byte addr = 0x01;
                switch(state)
                {
                    case eACState.开机:
                        addr = 0x00;
                        break;
                    case eACState.关机:
                        addr = 0x01;
                        break;
                    case eACState.自动:
                        addr = 0x02;
                        break;
                    case eACState.制冷:
                        addr = 0x03;
                        break;
                    case eACState.除湿:
                        addr = 0x04;
                        break;
                    case eACState.通风:
                        addr = 0x05;
                        break;
                    case eACState.制热:
                        addr = 0x06;
                        break;
                    default:
                        break;
                }

                byte[] dataSend = new byte[] { byte.Parse(Addr), 0x06, 0x11, addr, 0x00, 0x01 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应！");
                }

                byte[] dataRecv = bsRecv.GetBytes();
                if(dataRecv.Length > 2 && dataRecv[1] == 0x86)
                {
                    throw new Exception($"{(eControlErrorType)dataRecv[2]}!");
                }

                if(dataRecv.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }

                // 修改状态缓存
                DeviceParams.DeviceState = state;
            }
            catch(Exception e)
            {
                throw new Exception($"设置[{this.name}]状态为[{state}]出错：{e.Message}");
            }
        }

        /// <summary>
        /// 设置空调风速
        /// </summary>
        /// <param name="state"></param>
        public void SetACWindSpeedState(eACWindSpeedState state)
        {
            try
            {
                byte[] dataSend = new byte[] { byte.Parse(Addr), 0x06, 0x11, (byte)(0x29 + state), 0x00, 0x01 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应！");
                }

                byte[] dataRecv = bsRecv.GetBytes();
                if(dataRecv.Length > 2 && dataRecv[1] == 0x86)
                {
                    throw new Exception($"{(eControlErrorType)dataRecv[2]}!");
                }

                if(dataRecv.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception e)
            {
                throw new Exception($"设置[{this.name}]风速为[{state}]出错：{e.Message}");
            }
        }

        /// <summary>
        /// 设置空调温度
        /// </summary>
        /// <param name="temp"></param>
        public void SetACTemp(int temp)
        {
            try
            {
                if(temp < 16 || temp > 32)
                {
                    throw new Exception("空调温度范围应为16-32℃！");
                }

                byte[] dataSend = new byte[] { byte.Parse(Addr), 0x06, 0x11, (byte)(temp - 9), 0x00, 0x01 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应！");
                }

                byte[] dataRecv = bsRecv.GetBytes();
                if(dataRecv.Length > 2 && dataRecv[1] == 0x86)
                {
                    throw new Exception($"{(eControlErrorType)dataRecv[2]}!");
                }

                if(dataRecv.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception e)
            {
                throw new Exception($"设置[{this.name}]温度为[{temp}]出错：{e.Message}");
            }
        }

        #endregion
    }
}