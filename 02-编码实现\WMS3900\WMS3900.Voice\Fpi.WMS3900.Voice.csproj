﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A291C52A-B74C-493B-B4C6-22B53E3C3C9C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.WMS3000.Voice</RootNamespace>
    <AssemblyName>Fpi.WMS3900.Voice</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\Product\Debug\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\Product\Release\bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>latest</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>latest</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="SunnyUI">
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, PublicKeyToken=null" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Config\VoiceControlCmd.cs" />
    <Compile Include="VoiceCmds\GlobalNameDefine.cs" />
    <Compile Include="Config\VoiceControlCmdManager.cs" />
    <Compile Include="Enum\Enums.cs" />
    <Compile Include="CustomVoiceCmd.cs" />
    <Compile Include="VoiceCmds\Helper\VoiceHelper.cs" />
    <Compile Include="Helper\VoiceLogHelper.cs" />
    <Compile Include="Interface\ILoadVoiceManager.cs" />
    <Compile Include="Interface\IVoiceConfig.cs" />
    <Compile Include="Interface\IVoiceConfigView.cs" />
    <Compile Include="Interface\IVoicesTemplateConfigView.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UI\FrmEditVoiceCmd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\FrmEditVoiceCmd.Designer.cs">
      <DependentUpon>FrmEditVoiceCmd.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\FrmConfigVoiceCmd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\FrmConfigVoiceCmd.Designer.cs">
      <DependentUpon>FrmConfigVoiceCmd.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\FrmVoiceTemplateParaEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\FrmVoiceTemplateParaEdit.Designer.cs">
      <DependentUpon>FrmVoiceTemplateParaEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\UC\UC_UserVoiceControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\UC\UC_UserVoiceControl.Designer.cs">
      <DependentUpon>UC_UserVoiceControl.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\UC\UC_VoiceControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\UC\UC_VoiceControl.Designer.cs">
      <DependentUpon>UC_VoiceControl.cs</DependentUpon>
    </Compile>
    <Compile Include="VoiceCmds\VoicesTemplate\AirConditionModeControl.cs" />
    <Compile Include="VoiceCmds\VoicesTemplate\AirConditionTempControl.cs" />
    <Compile Include="VoiceCmds\VoicesTemplate\DeviceParamUISwitch.cs" />
    <Compile Include="VoiceCmds\VoicesTemplate\EntranceControl.cs" />
    <Compile Include="VoiceCmds\VoicesTemplate\StateNodeControl.cs" />
    <Compile Include="VoiceCmds\VoicesTemplate\SoftUISwitch.cs" />
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_AirConditionModeControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_AirConditionModeControl.Designer.cs">
      <DependentUpon>UC_AirConditionModeControl.cs</DependentUpon>
    </Compile>
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_DeviceParamUISwitch.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_DeviceParamUISwitch.Designer.cs">
      <DependentUpon>UC_DeviceParamUISwitch.cs</DependentUpon>
    </Compile>
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_StateNodeControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_StateNodeControl.Designer.cs">
      <DependentUpon>UC_StateNodeControl.cs</DependentUpon>
    </Compile>
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_SoftUISwitch.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="VoiceCmds\VoiceTemplate_UC\UC_SoftUISwitch.Designer.cs">
      <DependentUpon>UC_SoftUISwitch.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="UI\FrmEditVoiceCmd.resx">
      <DependentUpon>FrmEditVoiceCmd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FrmConfigVoiceCmd.resx">
      <DependentUpon>FrmConfigVoiceCmd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FrmVoiceTemplateParaEdit.resx">
      <DependentUpon>FrmVoiceTemplateParaEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\UC\UC_UserVoiceControl.resx">
      <DependentUpon>UC_UserVoiceControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\UC\UC_VoiceControl.resx">
      <DependentUpon>UC_VoiceControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="VoiceCmds\VoiceTemplate_UC\UC_AirConditionModeControl.resx">
      <DependentUpon>UC_AirConditionModeControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="VoiceCmds\VoiceTemplate_UC\UC_DeviceParamUISwitch.resx">
      <DependentUpon>UC_DeviceParamUISwitch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="VoiceCmds\VoiceTemplate_UC\UC_StateNodeControl.resx">
      <DependentUpon>UC_StateNodeControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="VoiceCmds\VoiceTemplate_UC\UC_SoftUISwitch.resx">
      <DependentUpon>UC_SoftUISwitch.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Device\Fpi.Device.csproj">
      <Project>{88FEF5D2-E039-4AC0-942B-442F23755978}</Project>
      <Name>Fpi.Device</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Entrance\Fpi.Entrance.csproj">
      <Project>{1A61D7D4-AEDD-487F-84D9-7AE75B3B1AEF}</Project>
      <Name>Fpi.Entrance</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.HB.UI\Fpi.HB.UI.csproj">
      <Project>{25031008-3C88-4D40-9172-F4FE61B1C993}</Project>
      <Name>Fpi.HB.UI</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Json\Fpi.Json.csproj">
      <Project>{958C97C1-360F-4434-9C37-6C6030EB5FCD}</Project>
      <Name>Fpi.Json</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.NLPutilsUI\Fpi.NLPutilsUI.csproj">
      <Project>{16bebed9-a33d-4f44-af70-46c01cb3f535}</Project>
      <Name>Fpi.NLPutilsUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.NLPutils\Fpi.NLPutils.csproj">
      <Project>{b6f3e90e-1b45-4bd6-9387-df10e744b0b8}</Project>
      <Name>Fpi.NLPutils</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{c238e665-75b4-4eda-b574-a37f2794ba54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2D502016-B3B3-43FF-9BAE-AD1D2A18D42E}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.User\Fpi.User.csproj">
      <Project>{7D3E1D03-9F81-4B4A-B6B3-334D829DB1F4}</Project>
      <Name>Fpi.User</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.Equipment\Fpi.WMS3900.Equipment.csproj">
      <Project>{63a37282-fea3-4f07-98f1-164045b58d8b}</Project>
      <Name>Fpi.WMS3900.Equipment</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.SystemConfig\Fpi.WMS3900.SystemConfig.csproj">
      <Project>{74B5BD59-A90B-4F35-8F41-74A54A290940}</Project>
      <Name>Fpi.WMS3900.SystemConfig</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>