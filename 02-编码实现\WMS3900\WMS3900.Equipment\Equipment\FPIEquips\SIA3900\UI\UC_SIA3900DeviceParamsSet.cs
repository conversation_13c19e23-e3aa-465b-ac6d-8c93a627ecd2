﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900DeviceParamsSet : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        #endregion

        #region 构造

        public UC_SIA3900DeviceParamsSet()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(SIA3900Equipment device)
        {
            _device = device;
        }

        #endregion

        #region 事件

        #region 设置

        #region 参数设置

        /// <summary>
        /// 分钟
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMin_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtMin.Text, out int min))
                {
                    throw new Exception("分钟输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置分钟为{min}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x01, (short)min);

                    FpiMessageBox.ShowInfo($"修改分钟成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 温度设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTemp_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtTemp.Text, out int temp))
                {
                    throw new Exception("温度输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置温度为{temp}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x02, (short)temp);

                    FpiMessageBox.ShowInfo($"修改温度成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 上阈值设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpper_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtUpper.Text, out float upper))
                {
                    throw new Exception("上阈值输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置上阈值为{upper}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x05, upper);

                    FpiMessageBox.ShowInfo($"修改上阈值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 下阈值
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLower_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtLower.Text, out float lower))
                {
                    throw new Exception("下阈值输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置下阈值为{lower}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x07, lower);

                    FpiMessageBox.ShowInfo($"修改下阈值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 标液浓度设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConcentration_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtConcentration.Text, out float concentration))
                {
                    throw new Exception("标液浓度输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置标液浓度为{concentration}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x09, concentration);

                    FpiMessageBox.ShowInfo($"修改标液浓度成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 设置液体检测器1
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLique1_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtLique1.Text, out float lique1))
                {
                    throw new Exception("液体检测器1设置值输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置液体检测器1值为{lique1}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x0C, lique1);

                    FpiMessageBox.ShowInfo($"修改液体检测器1值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 液体检测器2
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLique2_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtLique2.Text, out float lique2))
                {
                    throw new Exception("液体检测器2设置值输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置液体检测器2值为{lique2}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x0E, lique2);

                    FpiMessageBox.ShowInfo($"修改液体检测器2值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 上限
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpperLimit_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtUpperLimit.Text, out float upperLimit))
                {
                    throw new Exception("上限输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置上限值为{upperLimit}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x11, upperLimit);

                    FpiMessageBox.ShowInfo($"修改上限值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 下限设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLowerLimit_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtLowerLimit.Text, out float lowerLimit))
                {
                    throw new Exception("下限输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置下限值为{lowerLimit}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x13, lowerLimit);

                    FpiMessageBox.ShowInfo($"修改下限值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 核查浓度设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCheckConcent_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtCheckConcent.Text, out float checkConcent))
                {
                    throw new Exception("核查浓度输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置核查浓度为{checkConcent}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x16, checkConcent);

                    FpiMessageBox.ShowInfo($"修改核查浓度成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 阈值设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnthresholds_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtthresholds.Text, out int thresholds))
                {
                    throw new Exception("阈值输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置阈值为{thresholds}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x18, (short)thresholds);

                    FpiMessageBox.ShowInfo($"修改阈值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 浊度参数设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnParam_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtParam.Text, out float param))
                {
                    throw new Exception("核查浊度参数输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置浊度参数为{param}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x1B, param);

                    FpiMessageBox.ShowInfo($"修改浊度参数成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 消解控制
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDigestSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbDigestOpen.Checked)
                {
                    state = 1;
                }

                _device.WriteInt16ParamToDevice(0x00, state);

                FpiMessageBox.ShowInfo($"触发消解控制动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发消解控制动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 量程切换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeSwitchSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbRangeSwitchOpen.Checked)
                {
                    state = 1;
                }

                _device.WriteInt16ParamToDevice(0x04, state);

                FpiMessageBox.ShowInfo($"触发量程切换动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发量程切换动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 浊度补偿
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReductSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbReduct.Checked)
                {
                    state = 1;
                }
                else if(rdbAutoReduct.Checked)
                {
                    state = 2;
                }
                _device.WriteInt16ParamToDevice(0x03, state);

                FpiMessageBox.ShowInfo($"触发浊度补偿动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发浊度补偿动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 单位显示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUnitSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbmg.Checked)
                {
                    state = 1;
                }
                else if(rdbng.Checked)
                {
                    state = 2;
                }
                _device.WriteInt16ParamToDevice(0x0B, state);

                FpiMessageBox.ShowInfo($"触发单位显示动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发单位显示动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 报警限值
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAlarmLimitSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbAlarmLimitOpen.Checked)
                {
                    state = 1;
                }

                _device.WriteInt16ParamToDevice(0x10, state);

                FpiMessageBox.ShowInfo($"触发报警限值动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发报警限值动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 核查设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCheckSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbCheckOpen.Checked)
                {
                    state = 1;
                }

                _device.WriteInt16ParamToDevice(0x15, state);

                FpiMessageBox.ShowInfo($"触发核查设置动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发核查设置动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUploadSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbUploadOpen.Checked)
                {
                    state = 1;
                }

                _device.WriteInt16ParamToDevice(0x19, state);

                FpiMessageBox.ShowInfo($"触发上传设置动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发上传设置动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 当前量程
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeSet_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbRangeMiddle.Checked)
                {
                    state = 1;
                }
                else if(rdbRangeHight.Checked)
                {
                    state = 2;
                }
                _device.WriteInt16ParamToDevice(0x1A, state);

                FpiMessageBox.ShowInfo($"触发当前量程设置动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发当前量程设置动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 打灯次数
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLightCountSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtLightCount.Text, out int lightCount))
                {
                    throw new Exception("打灯次数输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置打灯次数为{lightCount}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x1D, (short)lightCount);

                    FpiMessageBox.ShowInfo($"修改打灯次数成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 信号灯电流
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLampCurrentSet_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtLampCurrent.Text, out float lampCurrent))
                {
                    throw new Exception("信号灯电流输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置信号灯电流为{lampCurrent}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x1E, lampCurrent);

                    FpiMessageBox.ShowInfo($"修改信号灯电流成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 跨度核查设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCheckSetWrite_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbRangeCheckOpen.Checked)
                {
                    state = 1;
                }

                _device.WriteInt16ParamToDevice(0x20, state);

                FpiMessageBox.ShowInfo($"触发跨度核查设置动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发跨度核查设置动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 跨度核查浓度设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCheckConWrite_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtRangeCheckCon.Text, out float con))
                {
                    throw new Exception("跨度核查浓度输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置跨度核查浓度为{con}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x21, con);

                    FpiMessageBox.ShowInfo($"修改跨度核查浓度成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 跨度阈值设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCheckValueWrite_Click(object sender, EventArgs e)
        {
            try
            {
                if(!int.TryParse(txtRangeCheckValue.Text, out int value))
                {
                    throw new Exception(" 跨度阈值输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置跨度阈值为{value}？") == DialogResult.Yes)
                {
                    _device.WriteInt16ParamToDevice(0x23, (short)value);

                    FpiMessageBox.ShowInfo($"修改跨度阈值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 零点核查设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnZeroCheckSetWrite_Click(object sender, EventArgs e)
        {
            try
            {
                byte state = 0;
                if(rdbZeroCheckOpen.Checked)
                {
                    state = 1;
                }

                _device.WriteInt16ParamToDevice(0x24, state);

                FpiMessageBox.ShowInfo($"触发零点核查设置动作成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"触发零点核查设置动作出错:{ex.Message}");
            }
        }

        /// <summary>
        /// 零点核查浓度设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnZeroCheckConWrite_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtZeroCheckCon.Text, out float con))
                {
                    throw new Exception("零点核查浓度输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置零点核查浓度为{con}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x25, con);

                    FpiMessageBox.ShowInfo($"修改零点核查浓度成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 零点阈值设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnZeroCheckValueWrite_Click(object sender, EventArgs e)
        {
            try
            {
                if(!float.TryParse(txtZeroCheckValue.Text, out float value))
                {
                    throw new Exception("零点核查阈值输入不合法！");
                }

                if(FpiMessageBox.ShowQuestion($"确认设置零点核查阈值为{value}？") == DialogResult.Yes)
                {
                    _device.WriteFloatParamToDevice(0x27, value);

                    FpiMessageBox.ShowInfo($"修改零点核查阈值成功！");
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #endregion

        #region 读取

        /// <summary>
        /// 分钟读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMinRead_Click(object sender, EventArgs e)
        {
            try
            {
                int min = _device.ReadInt16ParamFromDevice(0x01);
                txtMin.Text = min.ToString();

                FpiMessageBox.ShowInfo($"读取分钟成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 温度读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTempRead_Click(object sender, EventArgs e)
        {
            try
            {
                int temp = _device.ReadInt16ParamFromDevice(0x02);
                txtTemp.Text = temp.ToString();

                FpiMessageBox.ShowInfo($"读取温度成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 上阈值读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpperRead_Click(object sender, EventArgs e)
        {
            try
            {
                float upper = _device.ReadFloatParamFromDevice(0x05);
                txtUpper.Text = upper.ToString();

                FpiMessageBox.ShowInfo($"读取上阈值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 下阈值读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLowerRead_Click(object sender, EventArgs e)
        {
            try
            {
                float lower = _device.ReadFloatParamFromDevice(0x07);
                txtLower.Text = lower.ToString();

                FpiMessageBox.ShowInfo($"读取下阈值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 标液浓度读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConcentrationRead_Click(object sender, EventArgs e)
        {
            try
            {
                float concentration = _device.ReadFloatParamFromDevice(0x09);
                txtConcentration.Text = concentration.ToString();

                FpiMessageBox.ShowInfo($"读取标液浓度成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 液体检测器1读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLique1Read_Click(object sender, EventArgs e)
        {
            try
            {
                float lique1 = _device.ReadFloatParamFromDevice(0x0C);
                txtLique1.Text = lique1.ToString();

                FpiMessageBox.ShowInfo($"读取液体检测器1值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 液体检测器2读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLique2Read_Click(object sender, EventArgs e)
        {
            try
            {
                float lique2 = _device.ReadFloatParamFromDevice(0x0E);
                txtLique2.Text = lique2.ToString();

                FpiMessageBox.ShowInfo($"读取液体检测器2值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 上限读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpperLimitRead_Click(object sender, EventArgs e)
        {
            try
            {
                float upperLimit = _device.ReadFloatParamFromDevice(0x11);
                txtUpperLimit.Text = upperLimit.ToString();

                FpiMessageBox.ShowInfo($"读取上限值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 下限读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLowerLimitRead_Click(object sender, EventArgs e)
        {
            try
            {
                float lowerLimit = _device.ReadFloatParamFromDevice(0x13);
                txtLowerLimit.Text = lowerLimit.ToString();

                FpiMessageBox.ShowInfo($"读取下限值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 核查浓度读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCheckConcentRead_Click(object sender, EventArgs e)
        {
            try
            {
                float checkConcent = _device.ReadFloatParamFromDevice(0x16);
                txtCheckConcent.Text = checkConcent.ToString();

                FpiMessageBox.ShowInfo($"读取核查浓度成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 阈值读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnthresholdsRead_Click(object sender, EventArgs e)
        {
            try
            {
                int thresholds = _device.ReadInt16ParamFromDevice(0x18);
                txtthresholds.Text = thresholds.ToString();

                FpiMessageBox.ShowInfo($"读取阈值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 浊度参数
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnParamRead_Click(object sender, EventArgs e)
        {
            try
            {
                float param = _device.ReadFloatParamFromDevice(0x1B);
                txtParam.Text = param.ToString();

                FpiMessageBox.ShowInfo($"读取浊度参数成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 消解控制读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDigestSetRead_Click(object sender, EventArgs e)
        {
            try
            {
                int digest = _device.ReadInt16ParamFromDevice(0x00);
                rdbDigestClose.Checked = digest == 0;
                rdbDigestOpen.Checked = digest == 1;

                FpiMessageBox.ShowInfo($"读取消解控制成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 量程切换读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeSwitchRead_Click(object sender, EventArgs e)
        {
            try
            {
                int rangeSwitch = _device.ReadInt16ParamFromDevice(0x04);
                rdbRangeSwitchClose.Checked = rangeSwitch == 0;
                rdbRangeSwitchOpen.Checked = rangeSwitch == 1;
                FpiMessageBox.ShowInfo($"读取量程切换成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 浊度补偿读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReductRead_Click(object sender, EventArgs e)
        {
            try
            {
                int reduct = _device.ReadInt16ParamFromDevice(0x03);
                if(reduct == 0)
                {
                    rdbNoReduct.Checked = true;
                }
                else if(reduct == 1)
                {
                    rdbReduct.Checked = true;
                }
                else
                {
                    rdbAutoReduct.Checked = true;
                }

                FpiMessageBox.ShowInfo($"读取浊度补偿成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 单位显示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUnitRead_Click(object sender, EventArgs e)
        {
            try
            {
                int unit = _device.ReadInt16ParamFromDevice(0x0B);
                if(unit == 0)
                {
                    rdbμg.Checked = true;
                }
                else if(unit == 1)
                {
                    rdbmg.Checked = true;
                }
                else
                {
                    rdbng.Checked = true;
                }

                FpiMessageBox.ShowInfo($"读取单位显示成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 报警限值读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAlarmLimitRead_Click(object sender, EventArgs e)
        {
            try
            {
                int alarmLimit = _device.ReadInt16ParamFromDevice(0x10);
                rdbAlarmLimitClose.Checked = alarmLimit == 0;
                rdbAlarmLimitOpen.Checked = alarmLimit == 1;

                FpiMessageBox.ShowInfo($"读取报警限值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 核查设置读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCheckRead_Click(object sender, EventArgs e)
        {
            try
            {
                int check = _device.ReadInt16ParamFromDevice(0x15);
                rdbCheckClose.Checked = check == 0;
                rdbCheckOpen.Checked = check == 1;

                FpiMessageBox.ShowInfo($"读取核查设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 上传读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUploadRead_Click(object sender, EventArgs e)
        {
            try
            {
                int upload = _device.ReadInt16ParamFromDevice(0x19);
                rdbUploadClose.Checked = upload == 0;
                rdbUploadOpen.Checked = upload == 1;

                FpiMessageBox.ShowInfo($"读取上传成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 当前量程读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeRead_Click(object sender, EventArgs e)
        {
            try
            {
                int range = _device.ReadInt16ParamFromDevice(0x1A);
                if(range == 0)
                {
                    rdbRangeLow.Checked = true;
                }
                else if(range == 1)
                {
                    rdbRangeMiddle.Checked = true;
                }
                else
                {
                    rdbRangeHight.Checked = true;
                }

                FpiMessageBox.ShowInfo($"读取当前量程成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 打灯次数读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLightCountRead_Click(object sender, EventArgs e)
        {
            try
            {
                int lightCount = _device.ReadInt16ParamFromDevice(0x1D);
                txtLightCount.Text = lightCount.ToString();

                FpiMessageBox.ShowInfo($"读取打灯次数成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 信号灯电流读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLampCurrentRead_Click(object sender, EventArgs e)
        {
            try
            {
                float lampCurrent = _device.ReadFloatParamFromDevice(0x1E);
                txtLampCurrent.Text = lampCurrent.ToString();

                FpiMessageBox.ShowInfo($"读取信号灯电流成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 跨度核查设置读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCheckSetRead_Click(object sender, EventArgs e)
        {
            try
            {
                int state = _device.ReadInt16ParamFromDevice(0x20);
                rdbRangeCheckClose.Checked = state == 0;
                rdbRangeCheckOpen.Checked = state == 1;

                FpiMessageBox.ShowInfo($"读取跨度核查设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 跨度核查浓度读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCheckConRead_Click(object sender, EventArgs e)
        {
            try
            {
                float value = _device.ReadFloatParamFromDevice(0x21);
                txtRangeCheckCon.Text = value.ToString();

                FpiMessageBox.ShowInfo($"读取跨度核查浓度成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 跨度阈值读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRangeCheckValueRead_Click(object sender, EventArgs e)
        {
            try
            {
                float value = _device.ReadInt16ParamFromDevice(0x23);
                txtRangeCheckValue.Text = value.ToString();

                FpiMessageBox.ShowInfo($"读取跨度阈值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 零点核查设置读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnZeroCheckSetRead_Click(object sender, EventArgs e)
        {
            try
            {
                int state = _device.ReadInt16ParamFromDevice(0x24);
                rdbZeroCheckClose.Checked = state == 0;
                rdbZeroCheckOpen.Checked = state == 1;

                FpiMessageBox.ShowInfo($"读取零点核查设置成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 零点核查浓度读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnZeroCheckConRead_Click(object sender, EventArgs e)
        {
            try
            {
                float value = _device.ReadFloatParamFromDevice(0x25);
                txtZeroCheckCon.Text = value.ToString();

                FpiMessageBox.ShowInfo($"读取零点核查浓度成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 零点核查阈值读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnZeroCheckValueRead_Click(object sender, EventArgs e)
        {
            try
            {
                float value = _device.ReadFloatParamFromDevice(0x27);
                txtZeroCheckValue.Text = value.ToString();

                FpiMessageBox.ShowInfo($"读取零点核查阈值成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #endregion

    }
}