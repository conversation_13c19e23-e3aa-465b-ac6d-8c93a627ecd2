﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 高指氨氮质控仪状态检测执行类
    /// </summary>
    public class CodMnNH4QCDPatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public CodMnNH4QCDPatrol()
        {
            UnitId = "CodMnNH4QCD";
            UnitName = "高指氨氮质控仪状态检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(ref ImageUnitPatrolResultBase patrolResult)
        {
            patrolResult = new CodMnNH4QCDPatrolResult();

            if(ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4QCDCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            // 拍照
            ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4QCDCamera.ScreenShot(out string picPath);

            // 算法分析
            AlgorithmHelper.CheckQCD3900State(new Mat(picPath), out bool leftWaterTubeSmutState, out bool leftSampleTubeSmutState, out bool leftWaterCupSmutState, out bool leftSampleCupSmutState, out bool rightWaterTubeSmutState, out bool rightSampleTubeSmutState, out bool rightWaterCupSmutState, out bool rightSampleCupSmutState, out bool wasteWaterBucketWarn, out bool wasteTankBucketWarn, out bool waterBucketWarn);

            patrolResult.ImagePath = FileExtension.GetRelativePath(picPath);
            ((CodMnNH4QCDPatrolResult)patrolResult).CodMnWaterTubeSmutState = leftWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).CodMnSampleTubeSmutState = leftSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).CodMnWaterCupSmutState = leftWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).CodMnSampleCupSmutState = leftSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).NH4WaterTubeSmutState = rightWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).NH4SampleTubeSmutState = rightSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).NH4WaterCupSmutState = rightWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).NH4SampleCupSmutState = rightSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).CodMnNH4WasteWaterBucketState = wasteWaterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).CodMnNH4WasteTankBucketState = wasteTankBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
            ((CodMnNH4QCDPatrolResult)patrolResult).CodMnNH4WaterBucketState = waterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
        }

        #endregion
    }
}