﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Markup;
using System.Windows.Media;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Border = System.Windows.Controls.Border;

namespace Fpi.WMS3000.Inspection.Config
{
    public static class ExcelExporter
    {
        public static void ExportToExcel(FrameworkElement reportControl, string filePath)
        {
            var fileInfo = new FileInfo(filePath);

            if(fileInfo.Exists) fileInfo.Delete();

            using(var package = new ExcelPackage(fileInfo))
            {
                var worksheet = package.Workbook.Worksheets.Add("Report");
                var elements = ParseVisualElements(reportControl);

                // 先写入所有内容
                foreach(var element in elements)
                {
                    if(string.IsNullOrEmpty(element.Text)) continue;

                    var cell = worksheet.Cells[element.Row + 1, element.Column + 1];
                    cell.Value = element.Text;
                    ApplyCellStyle(cell, element.Style);
                }

                // 后处理合并单元格
                foreach(var element in elements)
                {
                    if(element.RowSpan > 1 || element.ColumnSpan > 1)
                    {
                        var startCell = worksheet.Cells[element.Row + 1, element.Column + 1];
                        var endCell = worksheet.Cells[
                            element.Row + element.RowSpan,
                            element.Column + element.ColumnSpan
                        ];
                        worksheet.Cells[startCell.Address + ":" + endCell.Address].Merge = true;
                    }
                }

                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                package.Save();
            }
        }

        private static List<ReportElement> ParseVisualElements(FrameworkElement control)
        {
            var elements = new List<ReportElement>();
            TraverseVisualTree(control, elements);
            return elements;
        }

        private static void TraverseVisualTree(DependencyObject parent, List<ReportElement> elements,
                                       int currentRow = 0, int currentColumn = 0)
        {
            foreach(var child in VisualTreeHelperEx.GetVisualChildren(parent))
            {
                if(child is FrameworkElement element)
                {
                    ProcessElement(element, elements, currentRow, currentColumn);
                }
            }
        }

        private static void ProcessElement(FrameworkElement element, List<ReportElement> elements,
                                   int row, int column)
        {
            switch(element)
            {
                case Grid grid:
                    ProcessGrid(grid, elements, row, column);
                    break;
                case Border border:
                    ProcessBorder(border, elements, row, column);
                    break;
                case TextBlock textBlock:
                    AddTextBlockElement(textBlock, elements, row, column);
                    break;
                case ContentControl contentControl:
                    ProcessContentControl(contentControl, elements, row, column);
                    break;
                default:
                    TraverseVisualTree(element, elements, row, column);
                    break;
            }
        }

        private static void ProcessGrid(Grid grid, List<ReportElement> elements, int baseRow, int baseCol)
        {
            var (rowCount, colCount) = GetEffectiveGridSize(grid);
            var cellMap = new bool[rowCount, colCount];

            foreach(var child in grid.Children.OfType<FrameworkElement>())
            {
                var pos = GetGridPosition(child);
                if(pos.row >= rowCount || pos.column >= colCount) continue;

                MarkCellsAsUsed(cellMap, pos);
                ProcessElement(child, elements, baseRow + pos.row, baseCol + pos.column);

                elements.Add(new ReportElement
                {
                    Row = baseRow + pos.row,
                    Column = baseCol + pos.column,
                    RowSpan = pos.rowSpan,
                    ColumnSpan = pos.columnSpan,
                    Text = GetElementText(child),
                    Style = GetElementStyle(child)
                });
            }
        }

        private static void ProcessBorder(Border border, List<ReportElement> elements, int row, int col)
        {
            var pos = GetGridPosition(border);
            TraverseVisualTree(border, elements, row + pos.row, col + pos.column);
        }

        private static void AddTextBlockElement(TextBlock textBlock, List<ReportElement> elements,
                                       int row, int col)
        {
            var pos = GetGridPosition(textBlock);
            elements.Add(new ReportElement
            {
                Row = row + pos.row,
                Column = col + pos.column,
                RowSpan = pos.rowSpan,
                ColumnSpan = pos.columnSpan,
                Text = textBlock.Text,
                Style = GetElementStyle(textBlock)
            });
        }

        private static void ProcessContentControl(ContentControl contentControl,
                                          List<ReportElement> elements,
                                          int row, int column)
        {
            if(contentControl.Content is FrameworkElement content)
            {
                TraverseVisualTree(content, elements, row, column);
            }
        }

        private static (int rows, int columns) GetEffectiveGridSize(Grid grid)
        {
            int maxRow = grid.RowDefinitions.Count;
            int maxCol = grid.ColumnDefinitions.Count;

            foreach(var child in grid.Children.OfType<FrameworkElement>())
            {
                var pos = GetGridPosition(child);
                maxRow = Math.Max(maxRow, pos.row + pos.rowSpan);
                maxCol = Math.Max(maxCol, pos.column + pos.columnSpan);
            }
            return (maxRow, maxCol);
        }

        private static (int row, int column, int rowSpan, int columnSpan) GetGridPosition(FrameworkElement element)
        {
            return (
                Math.Max(0, Grid.GetRow(element)),
                Math.Max(0, Grid.GetColumn(element)),
                Math.Max(1, Grid.GetRowSpan(element)),
                Math.Max(1, Grid.GetColumnSpan(element))
            );
        }

        private static void MarkCellsAsUsed(bool[,] cellMap, (int row, int col, int rowSpan, int colSpan) pos)
        {
            for(int r = pos.row; r < pos.row + pos.rowSpan; r++)
            {
                for(int c = pos.col; c < pos.col + pos.colSpan; c++)
                {
                    if(r < cellMap.GetLength(0) && c < cellMap.GetLength(1))
                    {
                        cellMap[r, c] = true;
                    }
                }
            }
        }

        private static string GetElementText(FrameworkElement element)
        {
            return element switch
            {
                TextBlock tb => tb.Text,
                Label lbl => lbl.Content?.ToString(),
                ContentControl cc => cc.Content?.ToString(),
                _ => GetContentPropertyValue(element)?.ToString() ?? string.Empty
            };
        }

        private static object GetContentPropertyValue(FrameworkElement element)
        {
            var contentProperty = element.GetType()
                .GetCustomAttribute<ContentPropertyAttribute>()?.Name;

            return contentProperty != null
                ? element.GetType().GetProperty(contentProperty)?.GetValue(element)
                : null;
        }

        private static CellStyle GetElementStyle(FrameworkElement element)
        {
            var style = new CellStyle();

            // 处理TextBlock类型
            if(element is TextBlock textBlock)
            {
                style.FontName = textBlock.FontFamily?.Source;
                style.FontSize = (int)textBlock.FontSize;
                style.FontColor = (textBlock.Foreground as SolidColorBrush)?.Color;
                style.BackgroundColor = (textBlock.Background as SolidColorBrush)?.Color;
                style.HorizontalAlignment = textBlock.HorizontalAlignment;
            }
            // 处理Control类型（如Label、Button）
            else if(element is Control control)
            {
                style.FontName = control.FontFamily?.Source;
                style.FontSize = (int)control.FontSize;
                style.FontColor = (control.Foreground as SolidColorBrush)?.Color;
                style.BackgroundColor = (control.Background as SolidColorBrush)?.Color;
                style.HorizontalAlignment = control.HorizontalContentAlignment switch
                {
                    HorizontalAlignment.Left => HorizontalAlignment.Left,
                    HorizontalAlignment.Right => HorizontalAlignment.Right,
                    HorizontalAlignment.Center => HorizontalAlignment.Center,
                    _ => HorizontalAlignment.Stretch
                };
            }
            // 处理其他可能包含样式的元素
            else if(element is Panel panel)
            {
                style.BackgroundColor = (panel.Background as SolidColorBrush)?.Color;
            }

            return style;
        }

        private static void ApplyCellStyle(ExcelRange cell, CellStyle style)
        {
            if(!string.IsNullOrEmpty(style.FontName))
                cell.Style.Font.Name = style.FontName;

            cell.Style.Font.Size = style.FontSize;

            if(style.FontColor.HasValue)
                cell.Style.Font.Color.SetColor(ConvertColor(style.FontColor.Value));

            if(style.BackgroundColor.HasValue)
            {
                cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                cell.Style.Fill.BackgroundColor.SetColor(ConvertColor(style.BackgroundColor.Value));
            }

            cell.Style.HorizontalAlignment = style.HorizontalAlignment switch
            {
                HorizontalAlignment.Left => ExcelHorizontalAlignment.Left,
                HorizontalAlignment.Right => ExcelHorizontalAlignment.Right,
                HorizontalAlignment.Center => ExcelHorizontalAlignment.Center,
                _ => ExcelHorizontalAlignment.General
            };
        }

        private static System.Drawing.Color ConvertColor(System.Windows.Media.Color color)
        {
            return System.Drawing.Color.FromArgb(color.A, color.R, color.G, color.B);
        }
    }

    public class ReportElement
    {
        public int Row { get; set; }
        public int Column { get; set; }
        public int RowSpan { get; set; } = 1;
        public int ColumnSpan { get; set; } = 1;
        public string Text { get; set; }
        public CellStyle Style { get; set; }
    }

    public class CellStyle
    {
        public string FontName { get; set; }
        public int FontSize { get; set; }
        public System.Windows.Media.Color? FontColor { get; set; }
        public System.Windows.Media.Color? BackgroundColor { get; set; }
        public HorizontalAlignment HorizontalAlignment { get; set; }
    }

    public static class VisualTreeHelperEx
    {
        public static IEnumerable<DependencyObject> GetVisualChildren(DependencyObject parent)
        {
            if(parent == null) yield break;

            int count = VisualTreeHelper.GetChildrenCount(parent);
            for(int i = 0; i < count; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if(child != null)
                {
                    yield return child;

                    if(child is ContentControl cc && cc.Content is DependencyObject content)
                    {
                        foreach(var subChild in GetVisualChildren(content))
                            yield return subChild;
                    }
                    else if(child is ItemsControl ic)
                    {
                        foreach(var item in ic.Items)
                        {
                            if(item is DependencyObject obj)
                            {
                                foreach(var subChild in GetVisualChildren(obj))
                                    yield return subChild;
                            }
                        }
                    }
                }
            }
        }
    }
}
