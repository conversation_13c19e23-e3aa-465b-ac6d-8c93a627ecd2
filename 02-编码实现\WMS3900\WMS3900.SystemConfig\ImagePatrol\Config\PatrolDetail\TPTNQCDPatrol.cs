﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 总磷总氮质控仪状态检测执行类
    /// </summary>
    public class TPTNQCDPatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public TPTNQCDPatrol()
        {
            UnitId = "TPTNQCD";
            UnitName = "总磷总氮质控仪状态检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(ref ImageUnitPatrolResultBase patrolResult)
        {
            patrolResult = new TPTNQCDPatrolResult();

            if(ExterEquipConfigManager.GetInstance().CameraSelect.TPTNQCDCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            // 拍照
            ExterEquipConfigManager.GetInstance().CameraSelect.TPTNQCDCamera.ScreenShot(out string picPath);

            // 算法分析
            AlgorithmHelper.CheckQCD3900State(new Mat(picPath), out bool leftWaterTubeSmutState, out bool leftSampleTubeSmutState, out bool leftWaterCupSmutState, out bool leftSampleCupSmutState, out bool rightWaterTubeSmutState, out bool rightSampleTubeSmutState, out bool rightWaterCupSmutState, out bool rightSampleCupSmutState, out bool wasteWaterBucketWarn, out bool wasteTankBucketWarn, out bool waterBucketWarn);

            patrolResult.ImagePath = FileExtension.GetRelativePath(picPath);
            ((TPTNQCDPatrolResult)patrolResult).TPWaterTubeSmutState = leftWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TPSampleTubeSmutState = leftSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TPWaterCupSmutState = leftWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TPSampleCupSmutState = leftSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TNWaterTubeSmutState = rightWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TNSampleTubeSmutState = rightSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TNWaterCupSmutState = rightWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TNSampleCupSmutState = rightSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TPTNWasteWaterBucketState = wasteWaterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TPTNWasteTankBucketState = wasteTankBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
            ((TPTNQCDPatrolResult)patrolResult).TPTNWaterBucketState = waterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
        }

        #endregion
    }
}